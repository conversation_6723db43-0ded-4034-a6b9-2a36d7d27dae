package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.StockCostConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.stock.StkTaskIReleaseExecuteDto;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.stock.resp.BatchExecRowDetVo;
import com.meta.supplychain.entity.dto.stock.resp.BatchExecRowVo;
import com.meta.supplychain.entity.dto.stock.resp.BatchRecordResp;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.goods.GoodsPeriodFlagEnum;
import com.meta.supplychain.enums.wds.*;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.MoneyUtil;
import feign.codec.DecodeException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ShipOrderAggregationService {

    @Resource
    private IShipBatchDetailRepository shipBatchDetailRepository;
    @Resource
    private IPickOrderRepository pickOrderRepository;
    @Resource
    private IShipBillRepository shipOrderRepository;
    @Resource
    private IWdDeliveryDetailPickRefRepository deliveryDetailPickRefRepository;
    @Resource
    private IWdDeliveryBillDetailRepository deliveryBillDetailRepository;
    @Resource
    private ICommonStockService commonStockService;
    @Resource
    private ICommonFranchiseService commonFranchiseService;
    @Resource
    private IWdDeliveryBillRepository iWdDeliveryBillRepository;

    @Resource
    private DeliveryOrderAuditService deliveryOrderAuditService;

    @Resource
    private ISupplychainControlEngineService supplychainControlEngineService;

    /**
     * 根据拣货单列表 批量保存发货单
     *
     * @param pickOrderList       拣货单列表
     * @param shipBillList        发货单列表
     * @param shipBatchDetailList 发货单明细
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean saveBatchShipBill(List<PickBillPO> pickOrderList, List<ShipBillPO> shipBillList, List<ShipBatchDetailPO> shipBatchDetailList) {
        pickOrderRepository.lambdaUpdate().in(PickBillPO::getBillNo, pickOrderList.stream().map(PickBillPO::getBillNo).collect(Collectors.toList()))
                .eq(PickBillPO::getStatus, WDBillStatusEnum.PICK_STATUS_2.getStatus())
                .set(PickBillPO::getStatus, WDBillStatusEnum.PICK_STATUS_3.getStatus())
                .update();
        shipBatchDetailRepository.saveBatch(shipBatchDetailList);
        return shipOrderRepository.saveBatch(shipBillList);
    }

    /**
     * 保存冲红单
     *
     * @param shipBillPO                  原单信息
     * @param reversalShip                冲红单信息
     * @param reversalShipBatchDetailList 冲红明细
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveReversalShip(ShipBillPO shipBillPO, ShipBillPO reversalShip, List<ShipBatchDetailPO> reversalShipBatchDetailList) {
        //生成冲红单
        shipOrderRepository.updateById(shipBillPO);
        shipOrderRepository.save(reversalShip);
        shipBatchDetailRepository.saveBatch(reversalShipBatchDetailList);
    }

    /**
     * 库存处理
     * 场景覆盖：拣货/手工配送/差异处理生成/退货收货生成
     *
     * @param bill        配送发货单
     * @param billDetails 配送发货明细
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void handleStock(ShipBillPO bill, List<ShipBatchDetailPO> billDetails) {
        Logs.info("配送发货单【{}】，handleStock start", bill.getBillNo());
        //操作人为发货人
        OpInfo operatorInfo = new OpInfo();
        operatorInfo.setOperatorCode(bill.getShipManCode());
        operatorInfo.setOperatorName(bill.getShipManName());
        FranLineAdjustReq adjustReq = FranLineAdjustReq.builder()
                .timeStamp(System.currentTimeMillis())
                .createCode(operatorInfo.getOperatorCode())
                .createName(operatorInfo.getOperatorName())
                .type(FranLineTypeEnum.DELIVERY_SHIP.getCode())
                .billNumber(bill.getBillNo())
                .amount(bill.getTotalShipTaxMoney().doubleValue())
                .storeCode(bill.getInDeptCode())
                .build();
        try {
            //加盟额度扣减 仓库配送类型处理额度
            if (bill.handleJiaMeng()) {
                handleJiaMeng(bill, adjustReq);
            }
            BatchRecordResp batchRecordResp = doBatchExecute(bill, billDetails);
            //返回数据map
            Map<Long, BatchExecRowVo> costSkuRespMap = batchRecordResp.getSkuList().stream().collect(Collectors.toMap(BatchExecRowVo::getInsideId, Function.identity(), (k1, k2) -> k1));
            //只有配送类型的订单需要回写成本
            if (WDShipTypeEnum.WH_SHIP.getCode().equals(bill.getBillType())) {
                writeBackShipOrderCost(bill, billDetails, costSkuRespMap);
            }
            //发货先前置状态为处理中，事件通知完成后会更新为已发货
            //此处要回写入账时间
            shipOrderRepository.lambdaUpdate()
                    .set(ShipBillPO::getAccDate, batchRecordResp.getAccDate())
                    .set(ShipBillPO::getStatus, WDBillStatusEnum.SHIP_STATUS_PENDING.getStatus())
                    .eq(ShipBillPO::getBillNo, bill.getBillNo()).update();
            Logs.info("配送发货单【{}】，handleStock end", bill.getBillNo());
        } catch (BizException | DecodeException bizException) {
            //业务异常回滚逻辑
            //回滚加盟额度
            franLineRollback(bill, adjustReq);
            BizExceptions.throwWithThrowable(bizException);
        } catch (Exception e) {
            Logs.error("handleStock配送发货单【{}】库存处理失败:", bill.getBillNo(), e);
            //回滚加盟额度
            franLineRollback(bill, adjustReq);
            throw e;
        }

    }

    public void handleJiaMeng(ShipBillPO bill, FranLineAdjustReq adjustReq) {
        // zhm 冲红加盟逻辑待确认
        // 调用加盟系统释放并扣减门店额度（有订单释放并扣减，无订单直接扣减）；
        String srcBillNo = bill.getSrcBillNo();
        if (Objects.equals(WDShipTypeEnum.WH_SHIP.getCode(), bill.getBillType()) &&
                StringUtils.isNotBlank(srcBillNo)) {
            Integer billSource = bill.getBillSource();
            WDShipSourceEnum wdShipSourceEnum = StandardEnum.codeOf(WDShipSourceEnum.class, billSource);
            if (Objects.nonNull(wdShipSourceEnum) && WDShipSourceEnum.WAVE_PICK.getCode().equals(bill.getBillSource())) {
                // 此时 srcBillNo 是 pickBillNo 可能涉及多个 配送订单
                // 此处仅做 是否终止的判断  仅拣货单需要处理终止逻辑
                List<WdDeliveryBillDetailPickRefPO> pickRefPOList = deliveryDetailPickRefRepository.queryByPickBillNo(srcBillNo);
                if (CollectionUtils.isNotEmpty(pickRefPOList)) {
                    // 按配送订单分组
                    Map<String, List<WdDeliveryBillDetailPickRefPO>> deliveryBillMap = pickRefPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPickRefPO::getBillNo));
                    List<WdDeliveryBillPO> wdDeliveryBillPOS = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(new ArrayList<>(deliveryBillMap.keySet()));
                    List<FranLineAdjustReq.OrderNumber> collect = wdDeliveryBillPOS.stream().map(wdDeliveryBillPO ->
                            FranLineAdjustReq.OrderNumber.builder()
                                    .orderNumber(wdDeliveryBillPO.getBillNo())
                                    .amount(wdDeliveryBillPO.getTotalTaxMoney().doubleValue())
                                    .build()
                    ).collect(Collectors.toList());
                    adjustReq.setOrderNumberList(collect);
                    adjustReq.setType(FranLineTypeEnum.DELIVERY_SHIP_WITH_APPLY.getCode());
                }
            } else {
                WdDeliveryBillPO wdDeliveryBillPO = iWdDeliveryBillRepository.queryDeliveryOrderBillByBillNo(srcBillNo);
                if (Objects.nonNull(wdDeliveryBillPO)) {
                    adjustReq.setType(FranLineTypeEnum.DELIVERY_SHIP_WITH_APPLY.getCode());
                    adjustReq.setOrderNumberList(Collections.singletonList(FranLineAdjustReq.OrderNumber.builder()
                            .orderNumber(wdDeliveryBillPO.getBillNo())
                            .amount(wdDeliveryBillPO.getTotalTaxMoney().doubleValue())
                            .build()));
                }
            }
        }
        if (YesOrNoEnum.YES.getCode().equals(bill.getReversalBillSign())) {
            adjustReq.setType(FranLineTypeEnum.DELIVERY_SHIP_REVERSAL.getCode());
            adjustReq.setOrderNumberList(null);
        }
        commonFranchiseService.adjustFranLine(adjustReq);
    }

    //回滚加盟额度
    private void franLineRollback(ShipBillPO bill, FranLineAdjustReq rollbackReq) {
        if (DeptOperateModeEnum.JM.getCode().equals(bill.getDeptOperateMode())) {
            commonFranchiseService.franLineAdjustRollback(rollbackReq);
        }
    }

    private BatchRecordResp doBatchExecute(ShipBillPO bill, List<ShipBatchDetailPO> billDetails) {
        List<StkTaskItemExecuteDto> batchStocksGoods = new ArrayList<>();
        String stockBillType = CommonBillTypeEnum.DN.getCode();
        String operateCode = CommonOperateEnum.WH_POST.getCode();
        String whCode = bill.getWhCode();
        String targetDeptCode = bill.getInDeptCode();
        if (Objects.equals(bill.getReversalBillSign(), YesOrNoEnum.YES.getCode())) {
            //如果是冲红单需要重置操作类型
            operateCode = CommonOperateEnum.WH_RED.getCode();
        }
        WDShipTypeEnum shipType = StandardEnum.codeOf(WDShipTypeEnum.class, bill.getBillType());
        switch (Objects.requireNonNull(shipType)) {
            case DEPT_RETURN:
                // 更新配送单 验收中
                stockBillType = CommonBillTypeEnum.DNR.getCode();
                break;
            case DIFF_AUDIT:
                stockBillType = CommonBillTypeEnum.DNR_DIFF.getCode();
                break;
            default:
        }
        Integer negativeAllowedFlag = YesOrNoEnum.NO.getCode();
        if (Objects.equals(bill.getBillDirection(), WDBillDirectionEnum.NORMAL.getCode())) {
            negativeAllowedFlag = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.DNSTOCKCONTROL, bill.getWhCode());
        }
        for (ShipBatchDetailPO shipBillDetail : billDetails) {
            StkTaskItemExecuteDto detail = StockCostConvert.INSTANCE.shipBatchDetail2StockDetail(shipBillDetail);
            detail.setWhCode(whCode);
            detail.setDeptCode(whCode);
            detail.setWhName(bill.getWhName());
            detail.setDeptName(bill.getWhName());
            detail.setBillType(stockBillType);
            detail.setOperateCode(operateCode);
            detail.setTargetDeptCode(targetDeptCode);
            detail.setInTaxRate(shipBillDetail.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            detail.setOutTaxRate(shipBillDetail.getOutputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            detail.setOutCostTaxPrice(shipBillDetail.getShipPrice());
            detail.setOutCostTaxMoney(shipBillDetail.getShipTaxMoney());
            detail.setOutCostTax(shipBillDetail.getShipTax());
            detail.setChangePriceFlag(Objects.equals(shipBillDetail.getShipPriceModel(), WDShipPricePolicyEnum.COST_PRICE.getCode()) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
            //有加价率 则传递加价率
            if (Objects.equals(shipBillDetail.getShipPriceModel(), WDShipPricePolicyEnum.MAKEUP_PRICE.getCode())) {
                detail.setMarkupRate(shipBillDetail.getMarkupRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            }
            detail.setNegativeAllowedFlag(negativeAllowedFlag);
            //赠品默认按改价0处理成本
            if (Objects.equals(shipBillDetail.getSkuType(), WDSkuTypeEnum.GIFT.getCode())) {
                detail.setOutCostPrice(BigDecimal.ZERO);
                detail.setOutCostTaxPrice(BigDecimal.ZERO);
                detail.setOutCostTaxMoney(BigDecimal.ZERO);
                detail.setOutCostTax(BigDecimal.ZERO);
                detail.setOutCostMoney(BigDecimal.ZERO);
                detail.setOutCostTaxMoney(BigDecimal.ZERO);
                detail.setChangePriceFlag(YesOrNoEnum.YES.getCode());
            }
            //配送发货不传原单信息
            if (stockBillType.equals(CommonBillTypeEnum.DN.getCode()) && operateCode.equals(CommonOperateEnum.WH_POST.getCode())) {
                detail.setSrcBillNo(null);
                detail.setSrcBillType(null);
                detail.setSrcInsideId(null);
            }
            batchStocksGoods.add(detail);
        }

        BatchRecordReq batchRecord = BatchRecordReq.builder()
                .billNo(bill.getBillNo())
                .whCode(whCode)
                .deptCode(whCode)
                .deptName(bill.getWhName())
                .whName(bill.getWhName())
                .billType(stockBillType)
                .dpgzType(bill.getAccSign())
                .billTime(bill.getCreateTime())
                .skuList(batchStocksGoods)
                .build();
        if (Objects.equals(bill.getBillSource(), WDShipSourceEnum.WAVE_PICK.getCode()) && Objects.equals(bill.getReversalBillSign(), YesOrNoEnum.NO.getCode())) {
            //如果是拣货单生成 则需求释放拣货单锁定库存和配送订单占用
            List<StkTaskIReleaseExecuteDto> releaseSkuList = deliveryDetailPickRefRepository.lambdaQuery().eq(WdDeliveryBillDetailPickRefPO::getPickBillNo, bill.getSrcBillNo()).list()
                    .stream().map(pickRef ->
                            StkTaskIReleaseExecuteDto.builder()
                                    .whCode(pickRef.getInDeptCode())
                                    .outWhCode(pickRef.getWhCode())
                                    .srcBillNo(pickRef.getBillNo())
                                    .srcBillType(CommonBillTypeEnum.DO.getCode())
                                    .operateCode(CommonOperateEnum.WH_POST.getCode())
                                    .skuCode(pickRef.getSkuCode())
                                    .skuType(pickRef.getSkuType())
                                    .realQty(pickRef.getPickQty()).build()
                    ).collect(Collectors.toList());
            Logs.info("配送发货单【{}】，releaseSkuList:{}", bill.getBillNo(), releaseSkuList);
            batchRecord.setReleaseSkuList(releaseSkuList);
            batchRecord.setReleaseBillNo(bill.getSrcBillNo());
            batchRecord.setReleaseBillType(CommonBillTypeEnum.PL.getCode());
        } else if ((Objects.equals(bill.getBillSource(), WDShipSourceEnum.WH_SHIP.getCode()))
                && StringUtils.isNotEmpty(bill.getSrcBillNo())) {
            //如果是调配送订单 则需要按照关联的配送订单明细释放占用的库存列表 单次履行终止
            List<Long> srcInsideIdList = billDetails.stream().filter(billDetail -> Objects.nonNull(billDetail.getSrcBillNo())).map(ShipBatchDetailPO::getSrcInsideId).collect(Collectors.toList());
            List<WdDeliveryBillDetailPO> deliveryDetailList = deliveryBillDetailRepository.lambdaQuery().eq(WdDeliveryBillDetailPO::getBillNo, bill.getSrcBillNo())
                    .in(WdDeliveryBillDetailPO::getInsideId, srcInsideIdList).list();
            List<StkTaskIReleaseExecuteDto> releaseSkuList =
                    deliveryDetailList.stream().map(billDetail ->
                            StkTaskIReleaseExecuteDto.builder()
                                    .whCode(billDetail.getInDeptCode())
                                    .outWhCode(billDetail.getWhCode())
                                    .srcBillNo(billDetail.getBillNo())
                                    .srcBillType(CommonBillTypeEnum.DO.getCode())
                                    .operateCode(CommonOperateEnum.WH_POST.getCode())
                                    .skuCode(billDetail.getSkuCode())
                                    .skuType(billDetail.getSkuType())
                                    .realQty(billDetail.getDeliveryQty()).build()).collect(Collectors.toList());
            batchRecord.setReleaseSkuList(releaseSkuList);
        }
        return commonStockService.costStockExecute(batchRecord);
    }

    /**
     * 回写配送单成本信息
     * 1.非指定效期的需要 按效期信息回写明细
     * 2.回写配送明细成本信息
     * 3.当配送价取成本价场景，需要按成本价赋值配送价信息
     */
    private void writeBackShipOrderCost(ShipBillPO bill, List<ShipBatchDetailPO> billDetails, Map<Long, BatchExecRowVo> costSkuRespMap) {
        List<ShipBatchDetailPO> saveOrUpdateDetails = new ArrayList<>();
        OptionalLong maxInsideId = billDetails.stream()
                .mapToLong(ShipBatchDetailPO::getInsideId)
                .max();
        AtomicLong maxInsideVal = new AtomicLong(maxInsideId.isPresent() ? maxInsideId.getAsLong() + 1L : 1L);
        boolean resetTotalShipMoney = false;
        for (ShipBatchDetailPO billDetail : billDetails) {
            BatchExecRowVo costSkuResp = costSkuRespMap.get(billDetail.getInsideId());
            billDetail.setCostPrice(costSkuResp.getCostTaxPrice());
            billDetail.setCostTax(costSkuResp.getCostTax());
            billDetail.setCostTaxMoney(costSkuResp.getCostTaxMoney());
            //如果取价模式是成本价则 需要按成本价回写配送价 赠品为0不回写
            if (Objects.equals(billDetail.getShipPriceModel(), WDShipPricePolicyEnum.COST_PRICE.getCode()) && Objects.equals(billDetail.getSkuType(), WDSkuTypeEnum.NORMAL.getCode())) {
                billDetail.setShipPrice(costSkuResp.getCostTaxPrice());
                billDetail.setShipTax(costSkuResp.getCostTax());
                billDetail.setShipTaxMoney(costSkuResp.getCostTaxMoney());
                resetTotalShipMoney = true;
            }
            billDetail.setDelFlag(YesOrNoEnum.NO.getCode());
            //如果是效期品且未填效期信息 则需要根据成本返回的明细重新覆盖配送发货明细数据
            if (Objects.equals(billDetail.getPeriodFlag(), GoodsPeriodFlagEnum.PERIOD_MODEL.getCode()) && Objects.isNull(billDetail.getProductDate()) && Objects.isNull(billDetail.getExpireDate())) {
                Map<String, ShipBatchDetailPO> insertDetailMap = new HashMap<>();
                for (BatchExecRowDetVo skuPeriodDtl : costSkuResp.getBatchSkuList()) {
                    String skuKey = String.join(SysConstants.COLON_DELIMITER, skuPeriodDtl.getSkuCode(), skuPeriodDtl.getInsideId(), DateUtil.yyyyMMdd(skuPeriodDtl.getProductDate()), DateUtil.yyyyMMdd(skuPeriodDtl.getExpiryDate()));
                    ShipBatchDetailPO insertDetail = insertDetailMap.get(skuKey);
                    if (Objects.isNull(insertDetail)) {
                        insertDetail = CglibCopier.copy(billDetail, ShipBatchDetailPO.class);
                        //数据插入
                        insertDetail.setId(null);
                        insertDetail.setInsideId(maxInsideVal.getAndIncrement());
                        insertDetail.setProductDate(skuPeriodDtl.getProductDate());
                        insertDetail.setExpireDate(skuPeriodDtl.getExpiryDate());
                        insertDetail.setPeriodBatchNo(skuPeriodDtl.getPeriodBatchNo());
                        insertDetail.setWholeQty(BigDecimal.ZERO);
                        insertDetail.setOddQty(BigDecimal.ZERO);
                        insertDetail.setShipQty(BigDecimal.ZERO);
                        insertDetail.setShipTaxMoney(BigDecimal.ZERO);
                        insertDetail.setShipTax(BigDecimal.ZERO);
                    }
                    insertDetail.setShipQty(insertDetail.getShipQty().add(skuPeriodDtl.getRealQty()));
                    insertDetail.setShipTax(MoneyUtil.getTaxMoneyTotal(insertDetail.getShipPrice(), insertDetail.getOutputTaxRate(), insertDetail.getShipQty()));
                    insertDetail.setShipTaxMoney(MoneyUtil.getMultiplyMoneyTotal(insertDetail.getShipPrice(), insertDetail.getShipQty()));
                    insertDetail.setCostTax(insertDetail.getCostTax().add(skuPeriodDtl.getBatchCostTaxMoney().subtract(skuPeriodDtl.getCostMoney())));
                    insertDetail.setCostTaxMoney(insertDetail.getCostTax().add(skuPeriodDtl.getCostTaxMoney()));
                    insertDetailMap.put(skuKey, insertDetail);
                }
                saveOrUpdateDetails.addAll(insertDetailMap.values());
                //软删除标记
                billDetail.setDelFlag(YesOrNoEnum.YES.getCode());
            }
            saveOrUpdateDetails.add(billDetail);
        }
        if (resetTotalShipMoney) {
            //需要重算配送总金额
            bill.setTotalShipTaxMoney(saveOrUpdateDetails.stream()
                    .filter(detail -> Objects.equals(detail.getDelFlag(), YesOrNoEnum.NO.getCode()))
                    .map(ShipBatchDetailPO::getShipTaxMoney)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            bill.setTotalShipTax(saveOrUpdateDetails.stream()
                    .filter(detail -> Objects.equals(detail.getDelFlag(), YesOrNoEnum.NO.getCode()))
                    .map(ShipBatchDetailPO::getShipTax)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            shipOrderRepository.lambdaUpdate().eq(ShipBillPO::getBillNo, bill.getBillNo())
                    .set(ShipBillPO::getTotalShipTaxMoney, bill.getTotalShipTaxMoney())
                    .set(ShipBillPO::getTotalShipTax, bill.getTotalShipTax())
                    .update();
        }
        shipBatchDetailRepository.saveOrUpdateBatch(saveOrUpdateDetails);
    }
}
