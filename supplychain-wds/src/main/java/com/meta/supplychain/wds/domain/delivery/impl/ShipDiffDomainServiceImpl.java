package com.meta.supplychain.wds.domain.delivery.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizBillRuleService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.convert.wds.ShiDiffConvert;
import com.meta.supplychain.convert.wds.ShipAcceptConvert;
import com.meta.supplychain.convert.wds.ShipOrderConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.stock.StkTaskIReleaseExecuteDto;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.wds.RefundAcceptConfigDTO;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillOptParams;
import com.meta.supplychain.entity.dto.wds.req.WdShipAccDiffBillQueryReq;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBatchDetailResp;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import com.meta.supplychain.entity.po.wds.*;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.CommonBillTypeEnum;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.DirectSignEnum;
import com.meta.supplychain.enums.wds.*;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.*;
import com.meta.supplychain.util.BizExceptionUtil;
import com.meta.supplychain.util.MoneyUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.wds.domain.delivery.IShipAcceptDomainService;
import com.meta.supplychain.wds.domain.delivery.IShipDiffDomainService;
import com.meta.supplychain.wds.domain.delivery.IShipOrderDomainService;
import feign.codec.DecodeException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 差异处理单领域服务实现类
 */
@Service
public class ShipDiffDomainServiceImpl implements IShipDiffDomainService {

    @Resource
    private IWdShipAccDiffBillRepository shipAccDiffBillRepository;

    @Resource
    private IWdShipAccDiffBatchDetailRepository shipAccDiffBatchDetailRepository;

    @Resource
    IShipAcceptDomainService shipAcceptDomainService;

    @Resource
    public IRefundAcceptBatchDetailRepository refundAcceptDtlRepository;

    @Resource
    private ISupplychainBizBillRuleService supplychainBizBillRuleService;

    @Resource
    IShipOrderDomainService shipOrderDomainService;
    @Resource
    ICommonStockService iCommonStockService;
    @Resource
    private UserUtil userUtil;

    @Resource
    private BillEventServiceFactory billEventServiceFactory;

    @Resource
    private IWdShipAcceptBillRepository shipAcceptBillRepository;
    @Resource
    private IWdShipAcceptBatchDetailRepository shipAcceptBatchDetailRepository;
    @Resource
    private IShipBillRepository shipOrderRepository;
    @Resource
    private IShipBatchDetailRepository shipBatchDetailRepository;


    @Override
    public PageResult<WdShipAccDiffBillResp> queryShipDiffBillList(WdShipAccDiffBillQueryReq query) {
        if (CollectionUtils.isNotEmpty(query.getStatusList()) && query.getStatusList().size() == 1) {
            query.setStatus(query.getStatusList().get(0));
            query.getStatusList().clear();
        }
//        OpInfo operatorInfo = userUtil.getDeptOpInfoWithThrow();
//        query.fulfillDeptList(operatorInfo);
        if (com.alibaba.ageiport.common.utils.CollectionUtils.isEmpty(query.getWhCodeList())) {
            query.setWhCodeList(userUtil.getDeptOpInfoWithThrow().getManageDeptCodeList());
        }
        // 查询差异处理单列表
        IPage<WdShipAccDiffBillPO> pageResult = shipAccDiffBillRepository.queryDiffPage(query, new Page(query.getCurrent(), query.getPageSize()));
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return new PageResult<>(0, new ArrayList<>());
        }
        // 转换为响应对象
        IPage<WdShipAccDiffBillResp> convert = pageResult.convert(ShiDiffConvert.INSTANCE::convertToResult);

        return new PageResult<>(pageResult.getTotal(), convert.getRecords());
    }

    @Override
    public List<WdShipAccDiffBatchDetailResp> queryShipDiffBatchDetail(WdShipAccDiffBillOptParams optParams) {

        // 查询差异处理单
        WdShipAccDiffBillPO billPO = shipAccDiffBillRepository.queryByBillNo(optParams.getBillNo());
        if (billPO == null) {
            Logs.error("差异处理单不存在：{}", optParams.getBillNo());
            return null;
        }

        // 查询差异处理单批次明细
        List<WdShipAccDiffBatchDetailPO> detailPOList = shipAccDiffBatchDetailRepository.queryByBillNo(optParams.getBillNo());

        return ShiDiffConvert.INSTANCE.convertToBatchDetailRespList(detailPOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> audit(WdShipAccDiffBillOptParams optParams) {

        // 查询差异处理单
        WdShipAccDiffBillPO billPO = shipAccDiffBillRepository.queryByBillNo(optParams.getBillNo());
        if (billPO == null) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_007_D001);
        }
        if (!WDAcceptDiffStatusEnum.SHIP_DIFF_STATUS_PENDING.getCode().equals(billPO.getStatus())) {
            BizExceptions.throwWithErrorCode(WDErrorCodeEnum.SC_WDS_007_D002);
        }
        OpInfo opInfo = userUtil.getDeptAndUpDeptOpInfoWithThrow();

        //  这里 更新审核数量
        List<WdShipAccDiffBatchDetailPO> detailPOList = shipAccDiffBatchDetailRepository.queryByBillNo(optParams.getBillNo());
        Map<Long, WdShipAccDiffBatchDetailPO> detailPOMap = detailPOList.stream()
                .collect(Collectors.toMap(WdShipAccDiffBatchDetailPO::getInsideId, detailPO -> detailPO));
        List<WdShipAccDiffBatchDetailPO> updateDetailPOList = new ArrayList<>();
        optParams.getDetailList().forEach(item -> {
            WdShipAccDiffBatchDetailPO wdShipAccDiffBatchDetailPO = detailPOMap.get(item.getInsideId());
            WdShipAccDiffBatchDetailPO build = WdShipAccDiffBatchDetailPO.builder()
                    .id(wdShipAccDiffBatchDetailPO.getId())
                    .approveQty(item.getApproveQty())
                    .ownerMode(item.getOwnerMode())
                    .remark(item.getRemark())
                    .build();
            wdShipAccDiffBatchDetailPO.setApproveQty(item.getApproveQty());
            wdShipAccDiffBatchDetailPO.setOwnerMode(item.getOwnerMode());
            updateDetailPOList.add(build);
        });
        shipAccDiffBatchDetailRepository.updateBatchById(updateDetailPOList);
        billPO.setApproveManCode(opInfo.getOperatorCode());
        billPO.setApproveManName(opInfo.getOperatorName());
        billPO.setApproveTime(LocalDateTime.now());

        // 过滤责任方为配送中心的 生成明细数据
        Map<Integer, List<WdShipAccDiffBatchDetailPO>> ownerGroup = detailPOList.stream().collect(Collectors.groupingBy(WdShipAccDiffBatchDetailPO::getOwnerMode));
        // 更改状态
        WdShipAccDiffBillPO update = WdShipAccDiffBillPO.builder()
                .id(billPO.getId())
                .build();

        //  驳回 释放差异节点 DECR
        if (optParams.getOptType() == 2) {
            update.setStatus(WDAcceptDiffStatusEnum.SHIP_DIFF_STATUS_REJECTED.getCode()); // 假设1表示已审核
            convertStockReq(billPO, detailPOList, CommonOperateEnum.DECR, WDAcceptDiffOwnerModeEnum.OWNER_MODE_RECEIVE_STORE);
        } else {
            update.setStatus(WDAcceptDiffStatusEnum.SHIP_DIFF_STATUS_APPROVED.getCode()); // 假设1表示已审核
            List<WdShipAccDiffBatchDetailPO> receiveOwnModeList = ownerGroup.get(WDAcceptDiffOwnerModeEnum.OWNER_MODE_RECEIVE_STORE.getCode());
            if (CollectionUtils.isNotEmpty(receiveOwnModeList)) {
                // 责任方是门店 同驳回 释放 DECR
                convertStockReq(billPO, receiveOwnModeList, CommonOperateEnum.DECR, WDAcceptDiffOwnerModeEnum.OWNER_MODE_RECEIVE_STORE);
            }
            List<WdShipAccDiffBatchDetailPO> transferOwnModeList = ownerGroup.get(WDAcceptDiffOwnerModeEnum.OWNER_MODE_DELIVERY_CENTER.getCode());
            if (CollectionUtils.isNotEmpty(transferOwnModeList)) {
                try {
                    // 责任方是配送中心不需要同步库存
                    // 责任方是配送中心 生成 逆向配送单 数量为审核数量
                    String shipBillNo = supplychainBizBillRuleService.getBillNo(MdBillNoBillTypeEnum.SHIP_NOTE, billPO.getWhCode());
                    // 发出事件  生成配送验收单 逆向 并自动收货   配送发货单 自动发货
                    // 生成逆向配送验收
                    RefundAcceptConfigDTO refundAcceptConfigDTO = buildRefundAcceptBill(shipBillNo, billPO, transferOwnModeList);
                    // 生成逆向配送单
                    ShipBillPO shipBillPO = buildRefundShipBill(shipBillNo, refundAcceptConfigDTO, billPO, transferOwnModeList);
                    WdShipAcceptBillPO acceptBillPO = refundAcceptConfigDTO.getAcceptBillPO();
                    List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOList = refundAcceptConfigDTO.getShipAcceptBatchDetailPOList();
                    BatchRecordReq batchRecordReq = shipAcceptDomainService.convertStockReq(CommonBillTypeEnum.DNR_DIFF, CommonOperateEnum.POST,billPO.getBillNo(), acceptBillPO, shipAcceptBatchDetailPOList);
                    List<StkTaskIReleaseExecuteDto> stkTaskIReleaseExecuteDtos = buildReleaseSkuList(transferOwnModeList);
                    batchRecordReq.setReleaseSkuList(stkTaskIReleaseExecuteDtos);
                    iCommonStockService.costStockExecute(batchRecordReq);
                    billEventServiceFactory.publishEvent(shipBillPO.getBillNo(), BillActionTypeEnum.AUTO_SHIP_SEND, shipBillPO);
                    update.setRefundAcceptBillNo(refundAcceptConfigDTO.getAcceptBillPO().getBillNo());
                    update.setRefundShipBillNo(shipBillNo);
                } catch (BizException | DecodeException exception) {
                    BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_007_D003, exception.getMessage());
                } catch (Exception e) {
                    Logs.error("退配收货 {} 同步库存报错 ", billPO.getBillNo(), e);
                    BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_007_D003, e.getMessage());
                }
            } else {
                Logs.warn("差异单审核，单号 {} 无责任方为配送中心的明细行", billPO.getBillNo());
            }
        }
        // 更新差异处理单状态为已审核
        update.setApproveManCode(opInfo.getOperatorCode());
        update.setApproveManName(opInfo.getOperatorName());
        update.setApproveTime(LocalDateTime.now());
        update.setRemark(optParams.getRemark());
        shipAccDiffBillRepository.updateById(update);
        return Results.ofSuccess(true);
    }

    /**
     * 组装释放信息
     * @param transferOwnModeList
     * @return
     */
    List<StkTaskIReleaseExecuteDto> buildReleaseSkuList(List<WdShipAccDiffBatchDetailPO> transferOwnModeList) {
        return transferOwnModeList.stream()
                .map(item -> {
                    return StkTaskIReleaseExecuteDto.builder()
                            .whCode(item.getWhCode())
                            .skuCode(item.getSkuCode())
                            .skuType(item.getSkuType())
                            .operateCode(CommonOperateEnum.DECR.getCode())
                            .srcBillNo(item.getBillNo())
                            .srcBillType(CommonBillTypeEnum.DN_DIFF.getCode())
                            .realQty(item.getDiffQty())
                            .build();
                }).collect(Collectors.toList());
    }

    public void convertStockReq(WdShipAccDiffBillPO diffBillPO,
                                List<WdShipAccDiffBatchDetailPO> diffBatchDetailPOS,
                                CommonOperateEnum operateEnum, WDAcceptDiffOwnerModeEnum ownerMode) {
        try {
            String deptCode;
            String deptName;
            if (ownerMode == WDAcceptDiffOwnerModeEnum.OWNER_MODE_DELIVERY_CENTER) {
                deptCode = diffBillPO.getWhCode();
                deptName = diffBillPO.getWhName();
            } else {
                deptCode = diffBillPO.getDeptCode();
                deptName = diffBillPO.getDeptName();
            }
            CommonBillTypeEnum billTypeEnum = CommonBillTypeEnum.DN_DIFF;
            BatchRecordReq build = BatchRecordReq.builder()
                    .deptCode(deptCode)
                    .deptName(deptName)
                    .whCode(deptCode)
                    .whName(deptName)
                    .billNo(diffBillPO.getBillNo())
                    .seqNo(diffBillPO.getBillNo() + "_" + ownerMode.getCode())
                    .billType(billTypeEnum.getCode())
                    .billTime(LocalDateTime.now())
                    .skuList(diffBatchDetailPOS.stream().map(item -> {
                                BigDecimal realQty = ownerMode == WDAcceptDiffOwnerModeEnum.OWNER_MODE_DELIVERY_CENTER ? item.getApproveQty() : item.getDiffQty();
                                return StkTaskItemExecuteDto.builder()
                                        .deptCode(deptCode)
                                        .deptName(deptName)
                                        .whCode(deptCode)
                                        .whName(deptName)
                                        .billType(billTypeEnum.getCode())
                                        .insideId(item.getInsideId())
                                        .skuCode(item.getSkuCode())
                                        .skuName(item.getSkuName())
                                        .skuType(item.getSkuType())
                                        .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                        .operateCode(operateEnum.getCode())
                                        .costTaxPrice(item.getDiffTaxPrice())
                                        .realQty(realQty)
                                        .refBillType(CommonBillTypeEnum.DA.getCode())
                                        .refBillNo(diffBillPO.getAcceptBillNo())
                                        .refBillAccDate(diffBillPO.getCreateTime().toLocalDate())
                                        .build();
                            }).collect(Collectors.toList())
                    )
                    .build();
            iCommonStockService.costStockExecute(build);
        } catch (BizException | DecodeException exception) {
            BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_007_D003, exception.getMessage());
        } catch (Exception e) {
            Logs.error("退配收货 {} 同步库存报错 ", diffBillPO.getBillNo(), e);
            BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_007_D003, e.getMessage());
        }
    }


    public ShipBillPO buildRefundShipBill(String shipBillNo, RefundAcceptConfigDTO refundAcceptConfigDTO, WdShipAccDiffBillPO diffBillPo, List<WdShipAccDiffBatchDetailPO> detailPOList) {

        ShipBillPO shipBillPO = new ShipBillPO();
        shipBillPO.setBillNo(shipBillNo);
        shipBillPO.setWhCode(diffBillPo.getWhCode());
        shipBillPO.setWhName(diffBillPo.getWhName());
        shipBillPO.setInDeptCode(diffBillPo.getDeptCode());
        shipBillPO.setInDeptName(diffBillPo.getDeptName());
        shipBillPO.setBillDirection(WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode());
        shipBillPO.setBillType(WDShipTypeEnum.DIFF_AUDIT.getCode());
        shipBillPO.setBillSource(WDShipSourceEnum.DIFF_AUDIT.getCode());
        shipBillPO.setStatus(WDBillStatusEnum.SHIP_STATUS_PENDING.getStatus());
        shipBillPO.setRemark("差异审核生成");
        shipBillPO.setSrcBillNo(diffBillPo.getShipBillNo());
        shipBillPO.setRefundBillNo(diffBillPo.getBillNo());
        shipBillPO.setCreateCode(diffBillPo.getApproveManCode());
        shipBillPO.setCreateName(diffBillPo.getApproveManName());
        shipBillPO.setCreateTime(diffBillPo.getApproveTime());

        shipOrderDomainService.fillDeptInfo(shipBillPO);
        WdShipAcceptBillPO acceptBillPO = refundAcceptConfigDTO.getAcceptBillPO();
        List<ShipBatchDetailPO> shipBatchDetailPOS = ShipOrderConvert.INSTANCE.wdShipAccDiffBatchDetailPOList2ShipBatchDetailPOList(detailPOList);
        shipBatchDetailPOS.forEach(item -> {
            item.setBillNo(shipBillPO.getBillNo());
            item.setSrcBillNo(acceptBillPO.getBillNo());
            item.setSrcBillType(CommonBillTypeEnum.DNR_DIFF.getCode());
            item.setSrcInsideId(item.getInsideId());
            item.setBillDirection(WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode());
            item.setBillType(WDShipTypeEnum.DIFF_AUDIT.getCode());
            item.setBillSource(WDShipSourceEnum.DIFF_AUDIT.getCode());
            item.setAcceptSign(YesOrNoEnum.YES.getCode());
            item.setAcceptTax(MoneyUtil.getTaxMoneyTotal(item.getShipPrice(), item.getInputTaxRate(),item.getAcceptQty()));
            item.setAcceptTaxMoney(MoneyUtil.round2HalfUp(item.getShipPrice().multiply(item.getAcceptQty())));
            item.setShipTax(MoneyUtil.getTaxMoneyTotal(item.getShipPrice(), item.getInputTaxRate(),item.getShipQty()));
            item.setShipTaxMoney(MoneyUtil.round2HalfUp(item.getShipPrice().multiply(item.getShipQty())));
        });
        BigDecimal totalShipQty = shipBatchDetailPOS.stream().map(ShipBatchDetailPO::getShipQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        shipBillPO.setTotalShipQty(totalShipQty);
        BigDecimal totalShipTax = shipBatchDetailPOS.stream().map(ShipBatchDetailPO::getShipTax).reduce(BigDecimal.ZERO, BigDecimal::add);
        shipBillPO.setTotalShipTax(MoneyUtil.round2HalfUp(totalShipTax));
        BigDecimal totalShipTaxMoney = shipBatchDetailPOS.stream().map(ShipBatchDetailPO::getShipTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        shipBillPO.setTotalShipTaxMoney(MoneyUtil.round2HalfUp(totalShipTaxMoney));
        shipOrderRepository.save(shipBillPO);
        shipBatchDetailRepository.saveBatch(shipBatchDetailPOS);
        return shipBillPO;
    }

    public RefundAcceptConfigDTO buildRefundAcceptBill(String shipBillNo, WdShipAccDiffBillPO refundAcceptBillPO, List<WdShipAccDiffBatchDetailPO> detailPOList) {
        String billNo = supplychainBizBillRuleService.getBillNo(MdBillNoBillTypeEnum.INCOMING_DELIVERY, refundAcceptBillPO.getDeptCode());

        WdShipAcceptBillPO shipAcceptBillPO = WdShipAcceptBillPO.builder()
                .billNo(billNo)
                .whCode(refundAcceptBillPO.getWhCode())
                .whName(refundAcceptBillPO.getWhName())
                .deptCode(refundAcceptBillPO.getDeptCode())
                .deptName(refundAcceptBillPO.getDeptName())
                .billDirection(WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode())
                .billType(WDShipTypeEnum.DIFF_AUDIT.getCode())
                .billSource(WDShipSourceEnum.DIFF_AUDIT.getCode())
                .shipBillNo(shipBillNo)
                .remarkAccept("差异审核生成")
                .status(WDShipAcceptBillStatusEnum.ALREADY_AUDIT.getCode())
                .directSign(DirectSignEnum.NOT_DIRECT.getCode())
                .remarkShip(refundAcceptBillPO.getRemark())
                .build();
        shipAcceptBillPO.setCreateCode(refundAcceptBillPO.getApproveManCode());
        shipAcceptBillPO.setCreateName(refundAcceptBillPO.getApproveManName());
        shipAcceptBillPO.setCreateTime(refundAcceptBillPO.getApproveTime());
        shipAcceptBillPO.setApproveManCode(refundAcceptBillPO.getApproveManCode());
        shipAcceptBillPO.setApproveManName(refundAcceptBillPO.getApproveManName());
        shipAcceptBillPO.setApproveTime(refundAcceptBillPO.getApproveTime());

        List<WdShipAcceptBatchDetailPO> shipAcceptBatchDetailPOList = ShipAcceptConvert.INSTANCE.convertDiff2ShipDetailList(detailPOList);
        shipAcceptBatchDetailPOList.forEach(item -> {
            item.setBillNo(shipAcceptBillPO.getBillNo());
            item.setSrcBillNo(refundAcceptBillPO.getBillNo());
            item.setSrcBillType(CommonBillTypeEnum.DNR.getCode());
            item.setSrcInsideId(item.getInsideId());
            item.setBillDirection(WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode());
            item.setBillType(WDShipTypeEnum.DIFF_AUDIT.getCode());
            item.setAcceptTax(MoneyUtil.getTaxMoneyTotal(item.getAcceptPrice(), item.getInputTaxRate(),item.getAcceptQty()));
            item.setAcceptTaxMoney(MoneyUtil.round2HalfUp(item.getAcceptPrice().multiply(item.getAcceptQty())));
            item.setShipTax(MoneyUtil.getTaxMoneyTotal(item.getShipPrice(), item.getInputTaxRate(),item.getShipQty()));
            item.setShipTaxMoney(MoneyUtil.round2HalfUp(item.getShipPrice().multiply(item.getShipQty())));
        });
        shipAcceptBatchDetailRepository.saveBatch(shipAcceptBatchDetailPOList);
        BigDecimal totalAcceptQty = shipAcceptBatchDetailPOList.stream().map(WdShipAcceptBatchDetailPO::getAcceptQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        shipAcceptBillPO.setTotalAcceptQty(totalAcceptQty);
        BigDecimal totalAcceptTax = shipAcceptBatchDetailPOList.stream().map(WdShipAcceptBatchDetailPO::getAcceptTax).reduce(BigDecimal.ZERO, BigDecimal::add);
        shipAcceptBillPO.setTotalAcceptTax(MoneyUtil.round2HalfUp(totalAcceptTax));
        BigDecimal totalAcceptTaxMoney = shipAcceptBatchDetailPOList.stream().map(WdShipAcceptBatchDetailPO::getAcceptTaxMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
        shipAcceptBillPO.setTotalAcceptTaxMoney(MoneyUtil.round2HalfUp(totalAcceptTaxMoney));
        shipAcceptBillRepository.save(shipAcceptBillPO);
        return RefundAcceptConfigDTO.builder()
                .acceptBillPO(shipAcceptBillPO)
                .shipAcceptBatchDetailPOList(shipAcceptBatchDetailPOList)
                .build();
    }

}