package com.meta.supplychain.wds.web.controller.inner;


import cn.linkkids.framework.croods.common.date.LocalDates;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.logger.method.annotation.MethodLog;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import cn.linkkids.framework.croods.trace.util.TraceIds;
import com.meta.supplychain.common.component.service.intf.BillEventServiceFactory;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.entity.po.wds.ShipBillPO;
import com.meta.supplychain.entity.po.wds.WdShipAcceptBillPO;
import com.meta.supplychain.enums.BillActionTypeEnum;
import com.meta.supplychain.enums.pms.PmsSendModEnum;
import com.meta.supplychain.enums.wds.WDBillStatusEnum;
import com.meta.supplychain.enums.wds.WDShipAcceptBillStatusEnum;
import com.meta.supplychain.enums.wds.WDShipTypeEnum;
import com.meta.supplychain.enums.wds.WDSystemParamEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IShipBillRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdShipAcceptBillRepository;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.MoneyUtil;
import com.meta.supplychain.util.spring.SpringUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Tag(name = "内部定时任务接口")
@RequestMapping("${unit-deploy.prefix-main:}-inner/job/api")
@RequiredArgsConstructor
@RestController
@Validated
public class JobController {

    @Resource
    IWdShipAcceptBillRepository shipAcceptBillRepository;
    @Resource
    IShipBillRepository shipBillRepository;

    private final ISupplychainControlEngineService supplychainControlEngineService;

    @Resource
    private BillEventServiceFactory billEventServiceFactory;
    
    /**
     *  配送验收单自动收货
     *  每小时执行一次
     */
    @GetMapping("/autoReceiveTransferOrder")
    public void autoReceiveTransferOrder(@Validated @NotBlank(message = "_platform_num 不能为空")
                                         @RequestParam(value = "_platform_num",required = false) String tenantId) {
        TenantContext.clear();
        TenantContext.set(tenantId);
        TraceIds.clear();
        TraceIds.setOrRandom(null);
        final DateTimeFormatter DEFAULT_LOCAL_DATE_PATTERN = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        final DateTimeFormatter DEFAULT_LOCAL_TIME_PATTERN = DateTimeFormatter.ofPattern("HH:mm:ss");
        Logs.info("租户 {} 配送验收单自动收货开始", tenantId);
        List<ShipBillPO> shipBillPOList = shipBillRepository.queryByTypeAndStatus(WDShipTypeEnum.WH_SHIP.getCode(), new ArrayList<>(Collections.singletonList(WDBillStatusEnum.SHIP_STATUS_2.getStatus())));
        Logs.info("租户 {} 配送单待验收配送单总数 {}", TenantContext.get(), shipBillPOList.size());
        Map<String, List<ShipBillPO>> orderGroup = shipBillPOList.stream().collect(Collectors.groupingBy(ShipBillPO::getInDeptCode));
        orderGroup.forEach((storeCode, transferOrderList) -> {
            String autoDeliverDaysStr = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(WDSystemParamEnum.DEFAULT_AUTO_DELIVERY_DAYS,  storeCode);
            String autoDeliverHourStr = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(WDSystemParamEnum.DEFAULT_AUTO_DELIVERY_HOUR,  storeCode);
            BigDecimal autoDeliverDays = MoneyUtil.str2BigDecimal(autoDeliverDaysStr);
            BigDecimal autoDeliverHour = MoneyUtil.str2BigDecimal(autoDeliverHourStr);
            LocalDate now = LocalDate.now();
            int hour = LocalDateTime.now().getHour();
            String startTime = "";
            String endTime = "";
            if (autoDeliverDays.compareTo(BigDecimal.ZERO) == 0) {
                if (hour >= autoDeliverHour.intValue()) {
                    LocalDate monthAgo = now.minusMonths(1);
                    startTime = monthAgo.format(DEFAULT_LOCAL_DATE_PATTERN) + " 00:00:00";
                    endTime = now.format(DEFAULT_LOCAL_DATE_PATTERN) + " 23:59:59";
                    LocalDateTime start = DateUtil.date2LocalDateTime(startTime);
                    LocalDateTime end = DateUtil.date2LocalDateTime(endTime);
                    List<ShipBillPO> collect = transferOrderList.stream().filter(item ->
                                    start.isBefore(item.getCreateTime())
                                            && item.getCreateTime().isBefore(end) )
                            .collect(Collectors.toList());
                    Logs.info("租户 {} 部门 {} 创建时间判断区间 {} 命中数量 {}", TenantContext.get(),
                            storeCode, startTime + " ~ " + endTime, collect.size());
                    collect = collect.stream().filter(p -> !PmsSendModEnum.TOCUSTOMER.getCode().equals(p.getSendMode()))
                            .collect(Collectors.toList());
                    Logs.info("租户 {} 部门 {} 创建时间判断区间 {} 最终命中数量 {}", TenantContext.get(),
                            storeCode, startTime + " ~ " + endTime, collect.size());
                    collect.forEach(shipBillEvent -> {
                        //  执行手动收货相同逻辑
                        billEventServiceFactory.publishEvent(shipBillEvent.getBillNo(), BillActionTypeEnum.AUTO_SHIP_ACCEPT, shipBillEvent);
                    });

                }
            } else {
                if (hour >= autoDeliverHour.intValue()) {
                    LocalDate localDate = now.minusDays(autoDeliverDays.longValue());
                    endTime = localDate.format(DEFAULT_LOCAL_DATE_PATTERN) + " 23:59:59";
                    LocalDateTime end = DateUtil.date2LocalDateTime(endTime);
                    LocalDate monthAgo = now.minusMonths(1);
                    startTime = monthAgo.format(DEFAULT_LOCAL_DATE_PATTERN) + " 00:00:00";
                    LocalDateTime start = DateUtil.date2LocalDateTime(startTime);
                    List<ShipBillPO> collect = transferOrderList.stream().filter(item ->
                                    start.isBefore(item.getCreateTime())
                                            && item.getCreateTime().isBefore(end) )
                            .collect(Collectors.toList());
                    Logs.info("租户 {} 部门 {} 判断区间 {} 命中数量 {}", TenantContext.get(),
                            storeCode, startTime + " ~ " + endTime, collect.size());
                    collect = collect.stream().filter(p -> !PmsSendModEnum.TOCUSTOMER.getCode().equals(p.getSendMode()))
                            .collect(Collectors.toList());
                    Logs.info("租户 {} 部门 {} 判断区间 {} 最终命中数量 {}", TenantContext.get(),
                            storeCode, startTime + " ~ " + endTime, collect.size());
                    //  执行手动收货相同逻辑
                    collect.forEach(shipBillEvent -> {
                        //  执行手动收货相同逻辑
                        billEventServiceFactory.publishEvent(shipBillEvent.getBillNo(), BillActionTypeEnum.AUTO_SHIP_ACCEPT, shipBillEvent);
                    });
                }
            }
        });
    }

    @XxlJob("autoReceiveTransferOrderAll")
    @GetMapping("/autoReceiveAll")
    public void autoReceiveAll() {
        SpringUtil.getAllTenantIdFromConfig().forEach(this::autoReceiveTransferOrder);
    }


}
