package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.uuid.IdWorker;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdDemandStrategyDomainService;
import com.meta.supplychain.convert.md.MdDemandStrategyAutoMappingConvert;
import com.meta.supplychain.convert.md.MdDemandStrategyExcludeConvert;
import com.meta.supplychain.convert.md.MdDemandStrategyStatusMappingConvert;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyAutoMappingGroupDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMapping4DeptDTO;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMappingDTO;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyAutoMappingBatchDeleteReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyAutoMappingBatchReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyStatusMapping4DeptQueryReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyStatusMappingBatchReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludeBatchCreateReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludeBatchDeleteReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDemandStrategyExcludePageQueryReq;
import com.meta.supplychain.entity.dto.md.resp.demandstrategyexclude.MdDemandStrategyExcludeResponseDTO;
import com.meta.supplychain.entity.po.md.MdDemandStrategyAutoMappingPO;
import com.meta.supplychain.entity.po.md.MdDemandStrategyExcludePO;
import com.meta.supplychain.entity.po.md.MdDemandStrategyStatusMappingPO;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandStrategyAutoMappingRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandStrategyExcludeRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandStrategyStatusMappingRepositoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 需求策略领域服务实现
 * <AUTHOR>
 */
@Service
public class MdDemandStrategyDomainServiceImpl implements IMdDemandStrategyDomainService {

    @Resource
    private IMdDemandStrategyExcludeRepositoryService mdDemandStrategyExcludeRepositoryService;

    @Resource
    private IMdDemandStrategyAutoMappingRepositoryService mdDemandStrategyAutoMappingRepositoryService;

    @Resource
    private IMdDemandStrategyStatusMappingRepositoryService mdDemandStrategyStatusMappingRepositoryService;

    /**
     * 排除策略查询
     * <AUTHOR>
     */
    @Override
    public PageResult<MdDemandStrategyExcludeResponseDTO> pageQueryExclude(MdDemandStrategyExcludePageQueryReq request) {
        LambdaQueryWrapper<MdDemandStrategyExcludePO> condition = Wrappers.lambdaQuery(MdDemandStrategyExcludePO.class)
                .in(!CollectionUtils.isEmpty(request.getTypeList()), MdDemandStrategyExcludePO::getType, request.getTypeList())
                .in(!CollectionUtils.isEmpty(request.getCodeList()), MdDemandStrategyExcludePO::getCode, request.getCodeList())
                .and(StringUtils.isNotBlank(request.getCodeOrName()), wrapper ->
                        wrapper.like(MdDemandStrategyExcludePO::getCode, request.getCodeOrName())
                                .or()
                                .like(MdDemandStrategyExcludePO::getName, request.getCodeOrName())
                )
                .orderByDesc(MdDemandStrategyExcludePO::getCreateTime);

        IPage<MdDemandStrategyExcludePO> page = mdDemandStrategyExcludeRepositoryService
                .page(new Page<>(request.getCurrent(), request.getPageSize()), condition);

        List<MdDemandStrategyExcludeResponseDTO> records = page.getRecords().stream()
                .map(MdDemandStrategyExcludeConvert.INSTANCE::po2dto)
                .collect(Collectors.toList());

        return PageResult.of(page.getTotal(), records);
    }

    /**
     * 批量创建排除策略
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean createExclude(MdDemandStrategyExcludeBatchCreateReq batchRequest) {
        // 清理数据
        mdDemandStrategyExcludeRepositoryService.lambdaUpdate().remove();

        // 删除全部的排除策略
        if (batchRequest == null || CollectionUtils.isEmpty(batchRequest.getItems())) {
            return true;
        }

        List<MdDemandStrategyExcludePO> poList = batchRequest.getItems().stream()
                .distinct()
                .map(MdDemandStrategyExcludeConvert.INSTANCE::createDto2po)
                .collect(Collectors.toList());

        return mdDemandStrategyExcludeRepositoryService.saveBatch(poList);
    }

    /**
     * 批量删除排除策略
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean deleteExclude(MdDemandStrategyExcludeBatchDeleteReq batchRequest) {
        if (batchRequest == null || CollectionUtils.isEmpty(batchRequest.getIds())) {
            return false;
        }

        return mdDemandStrategyExcludeRepositoryService.removeByIds(batchRequest.getIds());
    }

    /**
     * 创建需求策略组
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean createAutoMappingConditionGroup(List<MdDemandStrategyAutoMappingDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return false;
        }

        List<MdDemandStrategyAutoMappingDTO> targetList = dtoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDemandCondition())
                        && StringUtils.isNotBlank(dto.getDemandConditionValue())
                        && StringUtils.isNotBlank(dto.getDemandGroupCode()))
                .collect(Collectors.toList());
        if (targetList.size() != dtoList.size()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD003B001);
        }

        String errorMessage = targetList.stream()
                .map(dto -> {
                    String errorMsg = "";
                    MdDemandStrategyAutoMappingConditionEnum conditionEnum
                            = StandardEnum.codeOf(MdDemandStrategyAutoMappingConditionEnum.class, dto.getDemandCondition());
                    if (conditionEnum == null) {
                        errorMsg += String.format("未知的条件Key [%s];", dto.getDemandCondition());
                    }
                    if (!StringUtils.isBlank(dto.getDemandConditionValueExtend())) {
                        MdDemandConditionValueExtendEnum conditionValueExtendEnum
                                = StandardEnum.codeOf(MdDemandConditionValueExtendEnum.class, dto.getDemandConditionValueExtend());
                        if (conditionValueExtendEnum == null) {
                            errorMsg += String.format("未知的条件值描述 [%s];", dto.getDemandCondition());
                        }
                    }
                    return errorMsg;
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(";"));
        if (!StringUtils.isBlank(errorMessage)) {
            throw new ScBizException(MdErrorCodeEnum.SCMD003B004, errorMessage);
        }

        List<MdDemandStrategyAutoMappingPO> poList = targetList.stream()
                .map(MdDemandStrategyAutoMappingConvert.INSTANCE::dto2po)
                .collect(Collectors.toList());
        return mdDemandStrategyAutoMappingRepositoryService.saveBatch(poList);
    }

    /**
     * 批量创建自动转需求策略
     * <AUTHOR>
     */
    @Transactional
    @Override
    public Boolean createAutoMapping(MdDemandStrategyAutoMappingBatchReq batchRequest) {
        // 删除掉旧数据
        mdDemandStrategyAutoMappingRepositoryService.lambdaUpdate().remove();

        if (batchRequest == null || CollectionUtils.isEmpty(batchRequest.getItems())) {
            return true;
        }

        // 去除重复
        List<MdDemandStrategyAutoMappingGroupDTO> targetList = batchRequest.getItems().stream().distinct().collect(Collectors.toList());

        for (MdDemandStrategyAutoMappingGroupDTO groupDTO : targetList) {
            List<MdDemandStrategyAutoMappingDTO> strategyList = MdDemandStrategyAutoMappingConvert.INSTANCE.convertGroupDto2ConditionsDto(groupDTO);
            if (CollectionUtils.isEmpty(strategyList)) {
                continue;
            }

            // 条件组编码
            String conditionGroupId = StringUtils.isBlank(groupDTO.getStrategyGroupCode()) ? IdWorker.getIdStr() : groupDTO.getStrategyGroupCode();
            strategyList.forEach(dto -> dto.setDemandGroupCode(conditionGroupId));

            if (!createAutoMappingConditionGroup(strategyList)) {
                throw new ScBizException(MdErrorCodeEnum.SCMD003B002);
            }
        }
        return true;
    }

    /**
     * 批量删除自动转需求策略
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean deleteAutoMapping(MdDemandStrategyAutoMappingBatchDeleteReq batchRequest) {
        if (batchRequest == null || CollectionUtils.isEmpty(batchRequest.getDemandGroupCodes())) {
            return false;
        }
        return mdDemandStrategyAutoMappingRepositoryService.lambdaUpdate()
                .in(MdDemandStrategyAutoMappingPO::getDemandGroupCode, batchRequest.getDemandGroupCodes())
                .remove();
    }

    // TODO: 2025/5/23 缓存优化
    @Override
    public List<MdDemandStrategyAutoMappingGroupDTO> listAllStrategyGroups() {
        List<MdDemandStrategyAutoMappingDTO> all = mdDemandStrategyAutoMappingRepositoryService.lambdaQuery().list().stream()
                .map(MdDemandStrategyAutoMappingConvert.INSTANCE::po2dto)
                .collect(Collectors.toList());

        return all.stream()
                .collect(Collectors.groupingBy(MdDemandStrategyAutoMappingDTO::getDemandGroupCode))
                .values().stream()
                .map(MdDemandStrategyAutoMappingConvert.INSTANCE::convertConditionsDto2GroupDto)
                .collect(Collectors.toList());
    }

    /**
     * 查询需求策略
     * <AUTHOR>
     */
    @Override
    public List<MdDemandStrategyAutoMappingGroupDTO> listStrategyGroups() {
        return listAllStrategyGroups().stream()
                .sorted(Comparator.comparing(MdDemandStrategyAutoMappingGroupDTO::getStrategyGroupCode))
                .collect(Collectors.toList());
    }

    /**
     * 检查给定条件的单据是否满足自动转需求条件
     * @return 满足自动转需求条件的查询条件对象集合（会回写生效的条件组编码）
     * <AUTHOR>
     */
    @Override
    public List<MdDemandStrategyAutoMappingGroupDTO> queryStrategyGroups(List<MdDemandStrategyAutoMappingGroupDTO> queryList) {
        if (CollectionUtils.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        List<MdDemandStrategyAutoMappingGroupDTO> sortedGroups = listAllStrategyGroups().stream()
                .sorted(Comparator.comparing(MdDemandStrategyAutoMappingGroupDTO::allItemSize))
                .collect(Collectors.toList());
        return queryList.stream()
                .filter(item -> {
                    String targetGroupCode = sortedGroups.stream()
                            .filter(group -> group.contains(item))
                            .findFirst()
                            .map(MdDemandStrategyAutoMappingGroupDTO::getStrategyGroupCode)
                            .orElse(null);
                    if (targetGroupCode == null) {
                        return false;
                    }
                    item.setStrategyGroupCode(targetGroupCode);
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建需求策略状态映射规则配置
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean createStatusMapping(List<MdDemandStrategyStatusMappingDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return false;
        }
        List<MdDemandStrategyStatusMappingDTO> targetList = dtoList.stream()
                .filter(dto -> StringUtils.isNotBlank(dto.getDemandTransferCondition())
                        && StringUtils.isNotBlank(dto.getDemandTransferConditionBiz())
                        && StringUtils.isNotBlank(dto.getVal()))
                .collect(Collectors.toList());
        if (targetList.size() != dtoList.size()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD003B001);
        }

        Set<String> keys = targetList.stream()
                .map(dto -> String.format("%s-%s", dto.getDemandTransferCondition(), dto.getDemandTransferConditionBiz()))
                .collect(Collectors.toSet());
        if (keys.size() > 1) {
            throw new ScBizException(MdErrorCodeEnum.SCMD003B003);
        }

        String errorMessage = dtoList.stream()
                .map(dto -> {
                    String errorMsg = "";
                    MdDemandStrategyStatusConditionEnum statusConditionEnum = StandardEnum.codeOf(MdDemandStrategyStatusConditionEnum.class, dto.getDemandTransferCondition());
                    if (statusConditionEnum == null) {
                        errorMsg += String.format("未知的条件key [%s];", dto.getDemandTransferCondition());
                    }
                    MdDemandStrategyBizConditionEnum strategyBizConditionEnum = StandardEnum.codeOf(MdDemandStrategyBizConditionEnum.class, dto.getDemandTransferConditionBiz());
                    if (strategyBizConditionEnum == null) {
                        errorMsg += String.format("未知的业务条件key [%s];", dto.getDemandTransferConditionBiz());
                    }
                    if (StringUtils.isNotBlank(dto.getDemandBillType())) {
                        MdBillDirectionEnum billDirectionEnum = StandardEnum.codeOf(MdBillDirectionEnum.class, dto.getDemandBillType());
                        if (billDirectionEnum == null) {
                            errorMsg += String.format("未知的单据类型key [%s];", dto.getDemandBillType());
                        }
                    }
                    if (dto.getDeptType() != null) {
                        MdDeptTypeEnum deptTypeEnum = StandardEnum.codeOf(MdDeptTypeEnum.class, dto.getDeptType());
                        if (deptTypeEnum == null) {
                            errorMsg += String.format("未知的部门类型 [%s];", dto.getDeptType());
                        }
                    }
                    return errorMsg;
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(";"));
        if (StringUtils.isNotBlank(errorMessage)) {
            throw new ScBizException(MdErrorCodeEnum.SCMD003B004, errorMessage);
        }

        mdDemandStrategyStatusMappingRepositoryService.lambdaUpdate()
                .eq(MdDemandStrategyStatusMappingPO::getDemandTransferCondition, dtoList.get(0).getDemandTransferCondition())
                .eq(MdDemandStrategyStatusMappingPO::getDemandTransferConditionBiz, dtoList.get(0).getDemandTransferConditionBiz())
                .remove();

        List<MdDemandStrategyStatusMappingPO> poList
                = dtoList.stream().map(MdDemandStrategyStatusMappingConvert.INSTANCE::dto2po).collect(Collectors.toList());
        return mdDemandStrategyStatusMappingRepositoryService.saveBatch(poList);
    }

    /**
     * 批量创建需求策略状态流转规则
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean createOrUpdateStatusMapping(MdDemandStrategyStatusMappingBatchReq batchRequest) {
        if (batchRequest == null || CollectionUtils.isEmpty(batchRequest.getItems())) {
            return false;
        }
        for (MdDemandStrategyStatusMapping4DeptDTO item : batchRequest.getItems()) {
            item.setId(null);
            if (!createStatusMapping(MdDemandStrategyStatusMappingConvert.INSTANCE.convertDeptStatusMappingDto2single(item))) {
                throw new ScBizException(MdErrorCodeEnum.SCMD003B004);
            }
        }
        return true;
    }

    // TODO: 2025/5/23 缓存优化
    /**
     * 查询需求策略状态流转规则
     * <AUTHOR>
     */
    @Override
    public List<MdDemandStrategyStatusMapping4DeptDTO> listAllMdDemandStrategyStatusMappingData() {
        return mdDemandStrategyStatusMappingRepositoryService.lambdaQuery().list().stream()
                .map(MdDemandStrategyStatusMappingConvert.INSTANCE::po2dto)
                .collect(Collectors.groupingBy(dto -> String.format("%s-%s", dto.getDemandTransferCondition(), dto.getDemandTransferConditionBiz())))
                .values().stream()
                .map(MdDemandStrategyStatusMappingConvert.INSTANCE::convertStatusMappingDto2DeptGroup)
                .collect(Collectors.toList());
    }

    // TODO: 2025/5/23 缓存优化
    /**
     * 查询需求策略状态流转规则
     * <AUTHOR>
     */
    @Override
    public List<MdDemandStrategyStatusMapping4DeptDTO> queryMdDemandStrategyStatusMappingData(MdDemandStrategyStatusMapping4DeptQueryReq query) {
        return mdDemandStrategyStatusMappingRepositoryService.lambdaQuery()
                .in(!CollectionUtils.isEmpty(query.getDemandTransferConditionList()), MdDemandStrategyStatusMappingPO::getDemandTransferCondition, query.getDemandTransferConditionList())
                .in(!CollectionUtils.isEmpty(query.getDemandTransferConditionBizList()), MdDemandStrategyStatusMappingPO::getDemandTransferConditionBiz, query.getDemandTransferConditionBizList())
                .in(!CollectionUtils.isEmpty(query.getValList()), MdDemandStrategyStatusMappingPO::getVal, query.getValList())
                .in(!CollectionUtils.isEmpty(query.getDemandBillTypeList()), MdDemandStrategyStatusMappingPO::getDemandBillType, query.getDemandBillTypeList())
                .and(!CollectionUtils.isEmpty(query.getDeptCodeList()) || !CollectionUtils.isEmpty(query.getDeptGroupCodeList()), wrapper -> {
                    if (!CollectionUtils.isEmpty(query.getDeptCodeList())) {
                        wrapper.or(w -> {
                            for (String deptCode : query.getDeptCodeList()) {
                                w.or(o -> o.eq(MdDemandStrategyStatusMappingPO::getDeptCode, deptCode).eq(MdDemandStrategyStatusMappingPO::getDeptType, MdDeptTypeEnum.DEPT.getCode()));
                            }
                        });
                    }
                    if (!CollectionUtils.isEmpty(query.getDeptGroupCodeList())) {
                        wrapper.or(w -> {
                            for (String deptGroupCode : query.getDeptGroupCodeList()) {
                                w.or(o -> o.eq(MdDemandStrategyStatusMappingPO::getDeptCode, deptGroupCode).eq(MdDemandStrategyStatusMappingPO::getDeptType, MdDeptTypeEnum.DEPT_GROUP.getCode()));
                            }
                        });
                    }
                })
                .list()
                .stream()
                .map(MdDemandStrategyStatusMappingConvert.INSTANCE::po2dto)
                .collect(Collectors.groupingBy(dto -> String.format("%s-%s", dto.getDemandTransferCondition(), dto.getDemandTransferConditionBiz())))
                .values().stream()
                .map(MdDemandStrategyStatusMappingConvert.INSTANCE::convertStatusMappingDto2DeptGroup)
                .collect(Collectors.toList());
    }
}