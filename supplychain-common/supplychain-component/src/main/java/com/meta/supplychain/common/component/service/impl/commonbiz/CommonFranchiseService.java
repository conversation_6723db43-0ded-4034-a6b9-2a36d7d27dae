package com.meta.supplychain.common.component.service.impl.commonbiz;

import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonFranchiseService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.franline.req.FranLineQueryReq;
import com.meta.supplychain.entity.dto.franline.resp.FranLineAdjustResp;
import com.meta.supplychain.enums.CommonErrorCodeEnum;
import com.meta.supplychain.infrastructure.feign.BaseFranchiseFeignClient;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class CommonFranchiseService implements ICommonFranchiseService {
    @Value("${franLineAdjust_token:''}")
    String token;

    @Resource
    BaseFranchiseFeignClient baseFranchiseFeignClient;

    /**
     * 查询加盟店额度
     */
    public List<FranLineAdjustResp> queryFranLine(String deptCode){
        FranLineQueryReq jmReq = FranLineQueryReq.builder().storeCode(deptCode).build();
        List<FranLineAdjustResp> jmResp = baseFranchiseFeignClient.franLineQuery(jmReq);
        return jmResp;
    }

    /**
     * 校验加盟额度是否满足
     */
    public void checkFranLine(String deptCode,BigDecimal amount){
        List<FranLineAdjustResp> jmResp = queryFranLine(deptCode);
        if (CollectionUtils.isEmpty(jmResp)) {
            BizExceptions.throwWithMsg("门店:" + deptCode + "未维护加盟额度，请先维护");
        } else if (amount.compareTo(BigDecimal.valueOf(jmResp.get(0).getAvailLine())) > 0) {
            BizExceptions.throwWithMsg("订货金额:" + amount + "，超过门店可用余额" + BigDecimal.valueOf(jmResp.get(0).getAvailLine()) + "，请先提高信用额度");
        }
    }

    /**
     * 检查加盟店额度
     * @param deptMoneyMap 部门编码， 需要校验的金额
     */
    public Map<String,String> checkFranLine(Map<String,BigDecimal>  deptMoneyMap){
        Map<String,String> warnMap = new HashMap<>();
        //获取deptMoneyMap的key集合
        List<String> storeCodeList = new ArrayList<>(deptMoneyMap.keySet());
        FranLineQueryReq jmReq = FranLineQueryReq.builder().storeCodeList(storeCodeList).build();
        List<FranLineAdjustResp> jmResp = baseFranchiseFeignClient.franLineQuery(jmReq);
        Map<String,Double> jmFranLineMap = jmResp.stream().collect(Collectors.toMap(FranLineAdjustResp::getStoreCode, FranLineAdjustResp::getAvailLine));
        //遍历deptMoneyMap
        for (Map.Entry<String, BigDecimal> entry : deptMoneyMap.entrySet()){
            String deptCode = entry.getKey();
            BigDecimal needMoney = deptMoneyMap.get(deptCode);
            if(jmFranLineMap.containsKey(deptCode)){
                if(jmFranLineMap.get(deptCode) < needMoney.doubleValue()){
                    warnMap.put(deptCode, "storeCode:" + deptCode + " The available quota is insufficient.");
                }
            }else {
                warnMap.put(deptCode, "storeCode:" + deptCode + " No franchise quota information found");
            }
        }
        return warnMap;
    }

    /**
     * 检查加盟店额度
     * 校验失败抛异常
     * @param deptMoneyMap 部门编码， 需要校验的金额
     */
    public void checkFranLineWithValid(Map<String,BigDecimal> deptMoneyMap){
        if (deptMoneyMap.isEmpty()){
            return;
        }
        Map<String,String> warnMap = checkFranLine(deptMoneyMap);
        if(CollectionUtils.isNotEmpty(warnMap.values())){
            BizExceptions.throwWithCodeAndMsg(CommonErrorCodeEnum.BIZ_ERROR_002B001.getCode(),String.join(",", warnMap.values()));
        }
    }

    /**
     * 组装调整加盟额度入参
     */
    public FranLineAdjustReq buildReq(String billNo, String deptCode, BigDecimal amount, Integer type, OpInfo operatorInfo) {
        long timeMillis = System.currentTimeMillis();
        return FranLineAdjustReq.builder()
                .appCode("supplychain")
                .timeStamp(timeMillis)
                .sign(getSign(timeMillis))
                .createCode(operatorInfo.getOperatorCode())
                .createName(operatorInfo.getOperatorName())
                .type(type)
                .billNumber(billNo)
                .amount(amount.doubleValue())
                .storeCode(deptCode)
                .build();
    }



    /**
     * 调整加盟额度
     * FranLineTypeEnum
     */
    public FranLineAdjustReq adjustFranLine(FranLineAdjustReq req){
        try {
            req.setAppCode("supplychain");
            req.setSign(getSign(req.getTimeStamp()));
            Logs.info("单据号 {} 加盟店额度调整入参 {}", req.getBillNumber(), req);
            FranLineAdjustResp franLineAdjustResp = baseFranchiseFeignClient.franLineAdjust(req);
            Logs.info("单据号 {} 加盟店额度调整出参 {}", req.getBillNumber(), franLineAdjustResp);
            return req;
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            Logs.error("申请单号 {} 单据方向 {} 加盟店额度调整 异常 ", req.getBillNumber(), e);
            BizExceptions.throwWithMsg(e.getMessage());
        }
        return null;
    }

    /**
     * 加盟回滚
     *
     * @param req
     */
    public void franLineAdjustRollback(FranLineAdjustReq req) {
        if (Objects.nonNull(req)) {
            //金额为负
            req.setAmount(-1 * req.getAmount());
            req.setRollBack(1L);
            Logs.info("单据号 {} 加盟店额度调整 回滚入参 {}", req.getBillNumber(), req);
            FranLineAdjustResp franLineAdjustResp = baseFranchiseFeignClient.franLineAdjust(req);
            Logs.info("单据号 {} 加盟店额度调整 回滚出参 {}", req.getBillNumber(), franLineAdjustResp);
        }

    }

    /**
     * 验签规则
     */
    private String getSign(long timeStamp) {
        //测试环境 token
        return DigestUtils.md5DigestAsHex(("key=" + token + "&_platform_num=" + TenantContext.get() + "&timeStamp=" + timeStamp).toUpperCase().getBytes());
    }
}
