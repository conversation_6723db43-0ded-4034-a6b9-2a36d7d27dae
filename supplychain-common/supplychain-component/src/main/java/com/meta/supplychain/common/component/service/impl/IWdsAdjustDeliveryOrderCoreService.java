package com.meta.supplychain.common.component.service.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.google.common.util.concurrent.AtomicDouble;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.dto.wds.WdsDeliveryAdjustDTO;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustReq;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.CommonBillTypeEnum;
import com.meta.supplychain.enums.CommonOperateEnum;
import com.meta.supplychain.enums.DeptOperateModeEnum;
import com.meta.supplychain.enums.FranLineTypeEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.enums.wds.WDDeliveryOrderDirectionEnum;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import com.meta.supplychain.enums.wds.WDSystemParamEnum;
import com.meta.supplychain.infrastructure.repository.service.impl.wds.WdDeliveryBillDetailRepositoryImpl;
import com.meta.supplychain.infrastructure.repository.service.intf.common.IBillAdjustLogRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.BaseStoreUtil;
import com.meta.supplychain.util.BizExceptionUtil;
import com.meta.supplychain.util.MoneyUtil;
import feign.codec.DecodeException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class IWdsAdjustDeliveryOrderCoreService {


    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IWdDeliveryBillRepository wdDeliveryBillRepositoryService;
    private final WdDeliveryBillDetailRepositoryImpl wdsDeliveryBillDetailRepositoryService;
    private final IBillAdjustLogRepositoryService adjustLogRepositoryService;
    private final CommonFranchiseService commonFranchiseService;
    private final ICommonStockService iCommonStockService;
    private final BaseStoreUtil baseStoreUtil;

    /**
     * 组装采购订单调整参数
     */
    public WdsDeliveryAdjustDTO assembleAdjustReq(WdsDeliveryOrderAdjustReq asReq, Map<Long, WdDeliveryBillDetailPO> detailAlreadyMap) {
        List<WdDeliveryBillDetailPO> changeBillDetail = new ArrayList<>();
        asReq.getDetailList().forEach(item -> {
            //按insideId分组，只有一条
            WdDeliveryBillDetailPO existDetail = detailAlreadyMap.get(item.getInsideId());
            WdDeliveryBillDetailPO newDetail = CglibCopier.copy(existDetail, WdDeliveryBillDetailPO.class);
            // 判断是否改价格  没传值或者一致则为不改价
            if (item.getDeliveryPrice() != null && !existDetail.getDeliveryPrice().equals(item.getDeliveryPrice())) {
                newDetail.setDeliveryPrice(item.getDeliveryPrice());
            }
            BigDecimal purchQtyAfter;
            if (item.getAdjustQty() != null) {
                Integer adjustType = item.getAdjustType();
                BigDecimal rate = BigDecimal.ONE;
                if (adjustType != 0) {
                    rate = rate.multiply(BigDecimal.valueOf(-1));
                }
                purchQtyAfter = existDetail.getDeliveryQty().add(item.getAdjustQty().multiply(rate));
                if (purchQtyAfter.compareTo(BigDecimal.ZERO) < 0) { //小于0一定是追减
                    BizExceptions.throwWithMsg("商品:" + existDetail.getSkuCode() + " 追减后,采购数量不能小于0");
                }
            } else {
                purchQtyAfter = existDetail.getDeliveryQty();
            }
            BigDecimal wholeQtyAfter = purchQtyAfter.divide(existDetail.getUnitRate(), 0, RoundingMode.HALF_UP);
            BigDecimal oddQtyAfter = purchQtyAfter.subtract(wholeQtyAfter.multiply(existDetail.getUnitRate()));
            BigDecimal purchMoney = purchQtyAfter.multiply(newDetail.getDeliveryPrice());
            BigDecimal purchTax = MoneyUtil.getTaxMoneyTotal(newDetail.getDeliveryPrice(), existDetail.getInputTaxRate(), purchQtyAfter);
            newDetail.setDeliveryTaxMoney(purchMoney);
            newDetail.setDeliveryTax(purchTax);
            newDetail.setDeliveryQty(purchQtyAfter);
            newDetail.setWholeQty(wholeQtyAfter);
            newDetail.setOddQty(oddQtyAfter);
            changeBillDetail.add(newDetail);
        });
        return WdsDeliveryAdjustDTO.builder()
                .existDetailMap(detailAlreadyMap)
                .req(asReq)
                .changeBillDetailList(changeBillDetail)
                .build();
    }


    /**
     * 组装&校验 调整数据
     *
     * @return
     */
    public List<BillAdjustLogPO> assembleChangeInfo(WdsDeliveryAdjustDTO deliveryAdjustDTO) {
        WdDeliveryBillPO newBillForUpdate = deliveryAdjustDTO.getNewBill();
        WdsDeliveryOrderAdjustReq req = deliveryAdjustDTO.getReq();
        WdDeliveryBillPO oldBill = deliveryAdjustDTO.getOldBill();
        Map<Long, WdDeliveryBillDetailPO> detailCollect = deliveryAdjustDTO.getExistDetailMap();
        List<WdDeliveryBillDetailPO> changeBillDetailList = deliveryAdjustDTO.getChangeBillDetailList();
        List<BillAdjustLogPO> logList = new ArrayList<>();

        BillAdjustLogPO billAdjustLog = BillAdjustLogPO.builder()
                .billNo(deliveryAdjustDTO.getAdjustBillNo())
                .refBillNo(req.getBillNo())
                .billType(1)
                .billSource(req.getBillSource())
                .remark(req.getAdjustRemark())
                .build();
        //组装单据维度变化 联系人 联系地址 联系电话 备注 退货原因 送货方式
        if (Objects.nonNull(req.getContactMan())) {
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.CONTACT_MAN.getDesc());
            copyLog.setBeforeValue(oldBill.getContactMan());
            copyLog.setAfterValue(req.getContactMan());
            newBillForUpdate.setContactMan(req.getContactMan());
            logList.add(copyLog);
        }
        if (Objects.nonNull(req.getContactAddr())) {
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.CONTACT_ADDR.getDesc());
            copyLog.setBeforeValue(oldBill.getContactAddr());
            copyLog.setAfterValue(req.getContactAddr());
            newBillForUpdate.setContactAddr(req.getContactAddr());
            logList.add(copyLog);
        }
        if (Objects.nonNull(req.getContactTel())) {
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.CONTACT_TEL.getDesc());
            copyLog.setBeforeValue(oldBill.getContactTel());
            copyLog.setAfterValue(req.getContactTel());
            newBillForUpdate.setContactTel(req.getContactTel());
            logList.add(copyLog);
        }
        if (Objects.nonNull(req.getPurchRemark())) {
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.PURCH_REMARK.getDesc());
            copyLog.setBeforeValue(oldBill.getApproveRemark());
            copyLog.setAfterValue(req.getPurchRemark());
            newBillForUpdate.setRemark(req.getPurchRemark());
            logList.add(copyLog);
        }
        if (Objects.nonNull(req.getRefundReason())) {
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.REFUND_REASON.getDesc());
            copyLog.setBeforeValue(oldBill.getCauseName());
            copyLog.setAfterValue(req.getRefundReasonDesc());
            newBillForUpdate.setCauseCode(req.getRefundReason());
            newBillForUpdate.setCauseName(req.getRefundReasonDesc());
            logList.add(copyLog);
        }
        if (Objects.nonNull(req.getSendMode())) {
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.SEND_MODE.getDesc());
            copyLog.setBeforeValue(PmsSendModEnum.getEnumByCode(oldBill.getSendMode()).getName());
            copyLog.setAfterValue(PmsSendModEnum.getEnumByCode(req.getSendMode()).getName());
            newBillForUpdate.setSendMode(req.getSendMode());
            logList.add(copyLog);
        }
        LocalDate currentDate = LocalDate.now();

        //送货日期
        if (Objects.nonNull(req.getDeliverDate())) {
            if (req.getDeliverDate().isBefore(currentDate)) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P004);
            }
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.DELIVER_DATE.getDesc());
            copyLog.setBeforeValue(oldBill.getDeliveryDate().toString());
            copyLog.setAfterValue(req.getDeliverDate().toString());
            newBillForUpdate.setDeliveryDate(req.getDeliverDate());
            logList.add(copyLog);
        }

        //有效日期
        if (Objects.nonNull(req.getValidityDate())) {
            if (req.getValidityDate().isBefore(currentDate)) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P005);
            }
            BillAdjustLogPO copyLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            copyLog.setAdjustTarget(0);
            copyLog.setAdjustName(PmsAdjustNameEnum.VALIDITY_DATE.getDesc());
            copyLog.setBeforeValue(oldBill.getValidDate().toString());
            copyLog.setAfterValue(req.getValidityDate().toString());
            newBillForUpdate.setValidDate(req.getValidityDate());
            logList.add(copyLog);
        }

        //组装单据维度变化 采购价格 采购数量 整件数量 零头数量 采购金额 采购税金
        //数量 价格联动金额变化,金额一定会变 只需要记录采购价格或者采购数量是否变更
        AtomicDouble moneyDifference = new AtomicDouble(0);
        AtomicDouble taxDifference = new AtomicDouble(0);
        AtomicDouble qtyDifferenceSum = new AtomicDouble(0);
        List<WdDeliveryBillDetailPO> detailPOListForUpdate = new ArrayList<>();
        changeBillDetailList.forEach(item -> {
            WdDeliveryBillDetailPO originalDetail = detailCollect.get(item.getInsideId());
            BigDecimal qtyDifference = BigDecimal.ZERO;
            //校验金额，数量阀值
            checkThreshold(oldBill.getBillDirection(), oldBill.getInDeptCode(), item.getDeliveryQty(), item.getDeliveryTaxMoney(), item.getSkuCode());

            WdDeliveryBillDetailPO detailForUpdate = WdDeliveryBillDetailPO.builder()
                    .id(originalDetail.getId()).build();
            BillAdjustLogPO detailLog = CglibCopier.copy(billAdjustLog, BillAdjustLogPO.class);
            detailLog.setAdjustTarget(1);
            detailLog.setRefInsideId(item.getInsideId());
            detailLog.setRefSkuCode(item.getSkuCode());
            detailLog.setRefSkuName(item.getSkuName());
            detailLog.setRefBarcode(item.getBarcode());
            //采购价格
            if (Objects.nonNull(item.getDeliveryPrice()) && item.getDeliveryPrice().compareTo(originalDetail.getDeliveryPrice()) != 0) {
                BillAdjustLogPO copyLog = CglibCopier.copy(detailLog, BillAdjustLogPO.class);
                copyLog.setAdjustName(PmsAdjustNameEnum.DELIVERY_PRICE.getDesc());
                copyLog.setBeforeValue(originalDetail.getDeliveryPrice().stripTrailingZeros().toPlainString());
                copyLog.setAfterValue(item.getDeliveryPrice().stripTrailingZeros().toPlainString());
                logList.add(copyLog);
                detailForUpdate.setDeliveryPrice(item.getDeliveryPrice());
            }
            //采购数量
            if (Objects.nonNull(item.getDeliveryQty()) && item.getDeliveryQty().compareTo(originalDetail.getDeliveryQty()) != 0) {
                req.setIsChangeQty(true);
                BillAdjustLogPO copyLog = CglibCopier.copy(detailLog, BillAdjustLogPO.class);
                copyLog.setAdjustName(PmsAdjustNameEnum.DELIVERY_QTY.getDesc());
                copyLog.setBeforeValue(originalDetail.getDeliveryQty().stripTrailingZeros().toPlainString());
                copyLog.setAfterValue(item.getDeliveryQty().stripTrailingZeros().toPlainString());
                logList.add(copyLog);
                detailForUpdate.setDeliveryQty(item.getDeliveryQty());
                detailForUpdate.setWholeQty(item.getWholeQty());
                detailForUpdate.setOddQty(item.getOddQty());
                //计算差值
                qtyDifference = item.getDeliveryQty().subtract(originalDetail.getDeliveryQty());
                qtyDifferenceSum.addAndGet(qtyDifference.doubleValue());
                item.setDiffQty(qtyDifference);
            }
            detailForUpdate.setDeliveryTaxMoney(item.getDeliveryTaxMoney());
            detailForUpdate.setDeliveryTax(item.getDeliveryTax());
            detailPOListForUpdate.add(detailForUpdate);
            BigDecimal subtract = item.getDeliveryTaxMoney().subtract(originalDetail.getDeliveryTaxMoney());
            moneyDifference.addAndGet(subtract.doubleValue());
            BigDecimal subtract1 = item.getDeliveryTax().subtract(originalDetail.getDeliveryTax());
            taxDifference.addAndGet(subtract1.doubleValue());
        });
        newBillForUpdate.setTotalQty(oldBill.getTotalQty().add(BigDecimal.valueOf(qtyDifferenceSum.get())));
        newBillForUpdate.setTotalTaxMoney(oldBill.getTotalTaxMoney().add(BigDecimal.valueOf(moneyDifference.get())));
        newBillForUpdate.setTotalTax(oldBill.getTotalTax().add(BigDecimal.valueOf(taxDifference.get())));

        //加盟店 校验额度变化值
        Boolean isJMDept = DeptOperateModeEnum.JM.getCode().equals(oldBill.getDeptOperateMode())
                && !WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode().equals(oldBill.getBillDirection());
        if (isJMDept) {
            BigDecimal amount = newBillForUpdate.getTotalTaxMoney().subtract(oldBill.getTotalTaxMoney());
            // 调增需要校验额度
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                commonFranchiseService.checkFranLine(oldBill.getInDeptCode(), amount);
            }
        }
        deliveryAdjustDTO.setDetailPOListForUpdate(detailPOListForUpdate);
        return logList;
    }

    /**
     * 校验采购订单
     * 金额异常阀值 数量异常阀值
     */
    private void checkThreshold(Integer billDirection, String deptCode, BigDecimal amount, BigDecimal money, String skuCode) {
        BigDecimal maxMoney;
        BigDecimal maxAmount;
        if (WDDeliveryOrderDirectionEnum.PEI_SONG_TUI.getCode().equals(billDirection)) {
            maxMoney = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.TUI_PEI_DING_DAN_JIN_E_YI_CHANG_FA_ZHI);
            maxAmount = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.TUI_PEI_DING_DAN_SHU_LIANG_YI_CHANG_FA_ZHI);
        } else {
            maxMoney = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.PEI_SONG_DING_DAN_JIN_E_YI_CHANG_FA_ZHI);
            maxAmount = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getBigDecimalValue(WDSystemParamEnum.PEI_SONG_DING_DAN_SHU_LIANG_YI_CHANG_FA_ZHI);
        }

        if (Objects.nonNull(maxAmount) && Objects.nonNull(amount) && amount.compareTo(maxAmount) > 0) {
            BizExceptions.throwWithCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P004.getCode(), "商品：" + skuCode + WDErrorCodeEnum.SC_WDS_001_P004.getDesc());
        }
        if (Objects.nonNull(maxMoney) && Objects.nonNull(money) && money.compareTo(maxMoney) > 0) {
            BizExceptions.throwWithCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P005.getCode(), "商品：" + skuCode + WDErrorCodeEnum.SC_WDS_001_P005.getDesc());
        }
    }


    /**
     * 处理调整采购订单操作
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void doAdjust(WdsDeliveryAdjustDTO deliveryAdjustDTO, OpInfo operatorInfo, List<BillAdjustLogPO> billAdjustLog) {

        WdDeliveryBillPO newBill = deliveryAdjustDTO.getNewBill();
        WdDeliveryBillPO oldBill = deliveryAdjustDTO.getOldBill();
        List<WdDeliveryBillDetailPO> detailPOListForUpdate = deliveryAdjustDTO.getDetailPOListForUpdate();
        Map<Long, WdDeliveryBillDetailPO> existDetailMap = deliveryAdjustDTO.getExistDetailMap();
        List<WdDeliveryBillDetailPO> changeBillDetailList = deliveryAdjustDTO.getChangeBillDetailList();
        //调整信息落DB
        adjustLogRepositoryService.saveBatch(billAdjustLog);

        //更新原单调整信息
        wdDeliveryBillRepositoryService.updateById(newBill);
        if (CollectionUtils.isNotEmpty(detailPOListForUpdate)) {
            wdsDeliveryBillDetailRepositoryService.updateBatchById(detailPOListForUpdate);
        }
        //  加盟调整
        FranLineAdjustReq franLineAdjustReq = buildFranLineAdjustReq(oldBill, operatorInfo);
        //加盟店 校验额度变化值
        if (oldBill.handleJiaMeng()) {
            BigDecimal amount = newBill.getTotalTaxMoney().subtract(oldBill.getTotalTaxMoney());
            franLineAdjustReq.setAmount(amount.doubleValue());
            Logs.info("配送订单 {} 调整加盟店额度：{}", oldBill.getBillNo(), amount);
            if (franLineAdjustReq.getAmount() != 0) {
                // 调增需要校验额度
                commonFranchiseService.adjustFranLine(franLineAdjustReq);
            }
        }
        List<WdDeliveryBillDetailPO> changeQtyList = changeBillDetailList.stream().filter(item -> Objects.nonNull(item.getDiffQty()))
                .collect(Collectors.toList());
        //商品行信息变更，处理相关数据
        if (CollectionUtils.isNotEmpty(changeQtyList)) {
            //上报库存-调增，调减
            try {
                BatchRecordReq batchRecordReq = convertStockReq4Adjust(deliveryAdjustDTO.getAdjustBillNo(), newBill, changeQtyList);
                iCommonStockService.costStockExecute(batchRecordReq);
            } catch (DecodeException | BizException e) {
                if (oldBill.handleJiaMeng()) {
                    if (franLineAdjustReq.getAmount() != 0) {
                        commonFranchiseService.franLineAdjustRollback(franLineAdjustReq);
                    }
                }
                throw e;
            } catch (Exception e) {
                if (oldBill.handleJiaMeng()) {
                    if (franLineAdjustReq.getAmount() != 0) {
                        commonFranchiseService.franLineAdjustRollback(franLineAdjustReq);
                    }
                }
                //上报库存异常，回滚加盟
                Logs.error("配送订单调整同步库存失败，", e);
                BizExceptionUtil.throwWithErrorCodeAndMsg(WDErrorCodeEnum.SC_WDS_001_P010, e.getMessage());
            }
        }

    }

    public FranLineAdjustReq buildFranLineAdjustReq(WdDeliveryBillPO wdDeliveryBillPO, OpInfo operatorInfo) {
        FranLineAdjustReq adjustReq = FranLineAdjustReq.builder()
                .timeStamp(System.currentTimeMillis())
                .createCode(operatorInfo.getOperatorCode())
                .createName(operatorInfo.getOperatorName())
                .type(FranLineTypeEnum.DELIVERY_MODIFY.getCode())
                .billNumber(wdDeliveryBillPO.getBillNo())
                .amount(wdDeliveryBillPO.getTotalTaxMoney().doubleValue())
                .storeCode(wdDeliveryBillPO.getInDeptCode())
                .build();
        return adjustReq;
    }

    /**
     * 采购订单调整
     * 上报库存组装
     */
    public BatchRecordReq convertStockReq4Adjust(String adjustBillNo, WdDeliveryBillPO newBill, List<WdDeliveryBillDetailPO> purchBillDetailList) {
        CommonBillTypeEnum billTypeEnum = WDDeliveryOrderDirectionEnum.PEI_SONG.getCode().equals(newBill.getBillDirection()) ? CommonBillTypeEnum.DO : CommonBillTypeEnum.DR;

        BatchRecordReq batchRecordReq = BatchRecordReq.builder()
                .tenantId(String.valueOf(newBill.getTenantId()))
                .whCode(newBill.getInDeptCode())
                .whName(newBill.getInDeptName())
                .deptCode(newBill.getInDeptCode())
                .deptName(newBill.getInDeptName())
                .billNo(newBill.getBillNo())
                .billType(billTypeEnum.getCode())
                .seqNo(adjustBillNo)
                .billTime(LocalDateTime.now())
                .skuList(purchBillDetailList.stream().map(item -> {
                            BigDecimal qtyDifference = item.getDiffQty();
                            String operateCode = qtyDifference.compareTo(BigDecimal.ZERO) > 0 ? CommonOperateEnum.INCR.getCode() : CommonOperateEnum.DECR.getCode();
                            StkTaskItemExecuteDto executeDto = StkTaskItemExecuteDto.builder()
                                    .billType(billTypeEnum.getCode())
                                    .operateCode(operateCode)
                                    .whCode(newBill.getInDeptCode())
                                    .whName(newBill.getInDeptName())
                                    .deptCode(newBill.getInDeptCode())
                                    .deptName(newBill.getInDeptName())
                                    .outWhCode(newBill.getWhCode())
                                    .insideId(item.getInsideId())
                                    .supplierCode(newBill.getSupplierCode())
                                    .supplierName(newBill.getSupplierName())
                                    .skuCode(item.getSkuCode())
                                    .skuName(item.getSkuName())
                                    .skuType(item.getSkuType())
                                    .barcode(item.getBarcode())
                                    .skuModel(item.getSkuModel())
                                    .uomAttr(item.getUomAttr())
                                    .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                    .outTaxRate(item.getOutputTaxRate())
                                    .realQty(qtyDifference.abs())//发生数量
                                    .salePrice(item.getSalePrice())
                                    .saleTaxMoney(item.getSaleMoney())
                                    .build();

                            Integer negativeAllowedFlag;
                            if (newBill.getBillDirection() < 0) {
                                negativeAllowedFlag = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.DRSTOCKCONTROL, newBill.getInDeptCode());
                            } else {
                                negativeAllowedFlag = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(WDSystemParamEnum.DOSTOCKCONTROL, newBill.getWhCode());
                            }
                            executeDto.setNegativeAllowedFlag(negativeAllowedFlag);
                            return executeDto;
                        }).collect(Collectors.toList())
                ).build();

        return batchRecordReq;
    }


}
