package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.lock.LockManager;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.domain.md.intf.IMdDistributionPriceDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.IMdCommonStatusProcessService;
import com.meta.supplychain.constants.LockConstants;
import com.meta.supplychain.convert.md.MdDistPriceBillConvert;
import com.meta.supplychain.convert.md.MdDistPriceDetailConvert;
import com.meta.supplychain.convert.md.MdDistPriceDtoConvert;
import com.meta.supplychain.convert.md.MdDistPriceGoodsConvert;
import com.meta.supplychain.entity.dto.bds.req.QueryAllDeptDataReq;
import com.meta.supplychain.entity.dto.bds.req.QueryDeptTreeReq;
import com.meta.supplychain.entity.dto.bds.req.QueryUpDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryAllDeptDataResp;
import com.meta.supplychain.entity.dto.bds.resp.QueryDeepDeptGroupListResp;
import com.meta.supplychain.entity.dto.bds.resp.QueryUpDeptListResp;
import com.meta.supplychain.entity.dto.md.commonstatus.MdCommonStatusProcessDTO;
import com.meta.supplychain.entity.dto.md.distprice.*;
import com.meta.supplychain.entity.dto.md.req.distprice.ExistDistPriceDataReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceComplexReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryDistPriceReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.QueryValidDistPriceReq;
import com.meta.supplychain.entity.dto.md.req.goodsstrategy.ValidGoodsInfo;
import com.meta.supplychain.entity.dto.md.resp.MdDistPriceResponseDTO;
import com.meta.supplychain.entity.po.md.MdDistPriceBillPO;
import com.meta.supplychain.entity.po.md.MdDistPriceDetailPO;
import com.meta.supplychain.entity.po.md.MdDistPriceGoodsPO;
import com.meta.supplychain.entity.po.md.MdDistPricePO;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.ValidStatusEnum;
import com.meta.supplychain.enums.md.*;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.mybatis.OperatorInfoHandler;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDistPriceBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDistPriceDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDistPriceGoodsRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDistPriceRepositoryService;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 配送价格单
 * <AUTHOR>
 */
@Service
public class MdDistributionPriceDomainServiceImpl implements IMdDistributionPriceDomainService {

    @Resource
    private IMdDistPriceRepositoryService mdDistPriceRepositoryService;

    @Resource
    private IMdDistPriceBillRepositoryService mdDistPriceBillRepositoryService;

    @Resource
    private IMdDistPriceGoodsRepositoryService mdDistPriceGoodsRepositoryService;

    @Resource
    private IMdDistPriceDetailRepositoryService mdDistPriceDetailRepositoryService;

    @Resource
    private LockManager lockManager;

    @Resource
    private ThreadPoolTaskExecutor distPriceBillTakeEffectThreadPool;

    @Resource
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Resource
    private UserResourceUtil userResourceUtil;

    @Resource
    private IMdCommonStatusProcessService mdCommonStatusProcessService;

    /**
     * 分页查询配送价格
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 配送价格分页结果
     */
    @Override
    public IPage<MdDistPriceResponseDTO> selectDistPricePage(Page<MdDistPriceResponseDTO> page, MdDistPriceQueryDTO queryDTO) {
        return mdDistPriceRepositoryService.selectDistPricePage(page, queryDTO);
    }

    /**
     * 分页查询配送价格单及其明细
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 配送价格单及明细分页结果
     */
    @Override
    public IPage<MdDistPriceBillWithDetailDTO> selectBillJoinDetailPage(Page<MdDistPriceBillWithDetailDTO> page, MdDistPriceBillQueryDTO queryDTO) {
        return mdDistPriceBillRepositoryService.selectBillJoinDetailPage(page, queryDTO);
    }

    /**
     * 批量新增配送价格商品信息
     * @param goodsList 配送价格商品列表
     * @return Map包含错误信息 （map为空：操作成功；map不为空：操作失败；null：操作异常）
     */
    @Transactional
    @Override
    public Map<Integer, String> batchSaveDistPriceGoods(MdDistPriceBillDTO distPriceBillDTO, List<MdDistPriceGoodsDTO> goodsList) {
        if (CollectionUtils.isEmpty(goodsList) || distPriceBillDTO == null) {
            return null;
        }
        if (!StringUtils.hasText(distPriceBillDTO.getBillNo())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B001);
        }
        MdDistPriceDefineTypeEnum distPriceDefineTypeEnum = StandardEnum.codeOf(MdDistPriceDefineTypeEnum.class, distPriceBillDTO.getType());
        if (distPriceDefineTypeEnum == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B002);
        }

        List<MdDistPriceGoodsPO> poList = goodsList.stream()
                .map(MdDistPriceGoodsConvert.INSTANCE::dto2po)
                .collect(Collectors.toList());

        HashMap<Integer, String> result = new HashMap<>();
        for (MdDistPriceGoodsPO mdDistPriceGoodsPO : poList) {
            try {
                this.preCheckForSave(distPriceDefineTypeEnum, mdDistPriceGoodsPO);
            }catch (ScBizException e){
                result.put(mdDistPriceGoodsPO.getInsideId(), e.getMessage());
            }
        }

        if (!result.isEmpty()) {
            return result;
        }
        poList.forEach(po -> po.setBillNo(distPriceBillDTO.getBillNo()));

        return mdDistPriceGoodsRepositoryService.saveBatch(poList) ? Collections.emptyMap() : null;
    }

    private void preCheckForSave(MdDistPriceDefineTypeEnum distPriceDefineTypeEnum, MdDistPriceGoodsPO po) {
        if (po.getInsideId() == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B003);
        }

        if (distPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_PRODUCT) {
            if (!StringUtils.hasText(po.getSkuCode())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B004);
            }
            if (!StringUtils.hasText(po.getSkuName())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B005);
            }
        }

        if (distPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
            if (!StringUtils.hasText(po.getSkuClassCode())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B006);
            }
            if (!StringUtils.hasText(po.getSkuClassName())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B007);
            }
        }
    }


    /**
     * 批量新增配送价格明细信息
     * @param detailsList 配送价格明细列表
     * @return Map包含错误信息 （map为空：操作成功；map不为空：操作失败；null：操作异常）
     */
    @Transactional
    @Override
    public Map<Integer, String> batchSaveDistPriceDetail(MdDistPriceBillDTO distPriceBillDTO, List<MdDistPriceDetailDTO> detailsList) {
        if (CollectionUtils.isEmpty(detailsList) || distPriceBillDTO == null) {
            return null;
        }
        if (!StringUtils.hasText(distPriceBillDTO.getBillNo())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B008);
        }
        MdDistPriceDefineTypeEnum distPriceDefineTypeEnum = StandardEnum.codeOf(MdDistPriceDefineTypeEnum.class, distPriceBillDTO.getType());
        if (distPriceDefineTypeEnum == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B002);
        }

        List<MdDistPriceDetailPO> poList = detailsList.stream()
                .map(MdDistPriceDetailConvert.INSTANCE::dto2po)
                .collect(Collectors.toList());

        HashMap<Integer, String> result = new HashMap<>();
        for (MdDistPriceDetailPO mdDistPriceDetailPO : poList) {
            try {
                this.preCheckForSave(distPriceDefineTypeEnum, mdDistPriceDetailPO);
                // 金额小数四舍五入保留四位
                if (mdDistPriceDetailPO.getDistTaxPrice() != null) {
                    mdDistPriceDetailPO.setDistTaxPrice(mdDistPriceDetailPO.getDistTaxPrice().setScale(4, RoundingMode.HALF_UP));
                }
                // 百分比保留0位小数
                if (mdDistPriceDetailPO.getMarkupRate() != null) {
                    mdDistPriceDetailPO.setMarkupRate(mdDistPriceDetailPO.getMarkupRate().setScale(2, RoundingMode.HALF_UP));
                }
                if (mdDistPriceDetailPO.getDeductionRate() != null) {
                    mdDistPriceDetailPO.setDeductionRate(mdDistPriceDetailPO.getDeductionRate().setScale(2, RoundingMode.HALF_UP));
                }
            }catch (ScBizException e){
                result.put(mdDistPriceDetailPO.getInsideId(), e.getMessage());
            }
        }

        if (!result.isEmpty()) {
            return result;
        }
        poList.forEach(po -> po.setBillNo(distPriceBillDTO.getBillNo()));

        return mdDistPriceDetailRepositoryService.saveBatch(poList) ? Collections.emptyMap() : null;
    }


    private void preCheckForSave(MdDistPriceDefineTypeEnum distPriceDefineTypeEnum, MdDistPriceDetailPO po) {
        if (po.getInsideId() == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B009);
        }
        if (po.getGoodsInsideId() == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B010);
        }
        if (!po.validateDeptName() || !po.validateDeptCode() || !po.validateDeptType()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B011);
        }

        if (distPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_PRODUCT) {
            if (!StringUtils.hasText(po.getSkuCode())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B012);
            }
            if (!StringUtils.hasText(po.getSkuName())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B013);
            }
        }

        if (distPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
            if (!StringUtils.hasText(po.getSkuClassCode())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B014);
            }
            if (!StringUtils.hasText(po.getSkuClassName())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B015);
            }
        }

        MdDistPriceTypeEnum priceTypeEnum = StandardEnum.codeOf(MdDistPriceTypeEnum.class, po.getPriceType());
        if (priceTypeEnum == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B016);
        }

        if (priceTypeEnum == MdDistPriceTypeEnum.FIXED_PRICE && !po.validateDistTaxPrice()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B017);
        }
        if (priceTypeEnum == MdDistPriceTypeEnum.MARKUP_RATE && !po.validateMarkupRate()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B018);
        }
        if (priceTypeEnum == MdDistPriceTypeEnum.DISCOUNT_RATE && !po.validateDeductionRate()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B019);
        }
    }


    /**
     * 创建配送价格单
     * <AUTHOR>
     */
    @Transactional
    @Override
    public String createDistPriceBill(MdDistPriceBillDTO billDTO) {
        if (billDTO == null) {
            return null;
        }

        if (!StringUtils.hasText(billDTO.getBillNo())) {
            ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
            String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.DIST_PRICE_ORDER, billDTO.getWhCode());
            billDTO.setBillNo(billNo);
        }

        // 默认状态
        if (billDTO.getStatus() == null) {
            billDTO.setStatus(MdDistPriceOrderStatusEnum.DRAFT.getCode());
        }else if (!MdDistPriceOrderStatusEnum.DRAFT.verifyByCode(billDTO.getStatus())
                && !MdDistPriceOrderStatusEnum.PENDING_SUBMIT.verifyByCode(billDTO.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B020);
        }

        MdDistPriceBillPO mdDistPriceBillPO = MdDistPriceBillConvert.INSTANCE.dto2po(billDTO);
        if (!mdDistPriceBillPO.validateWhInfo()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B021);
        }
        if (!mdDistPriceBillPO.validateType()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B022);
        }
        if (!mdDistPriceBillPO.validateExecutionType()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B023);
        }

        // 表头
        boolean billSaveResult = mdDistPriceBillRepositoryService.save(mdDistPriceBillPO);
        if (!billSaveResult) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B024);
        }

        // 商品行信息
        if (!CollectionUtils.isEmpty(billDTO.getGoodsList())) {
            for (int i = 0; i < billDTO.getGoodsList().size(); i++) {
                billDTO.getGoodsList().get(i).setBillNo(billDTO.getBillNo());
            }
        }
        Map<Integer, String> goodsSaveResult = batchSaveDistPriceGoods(billDTO, billDTO.getGoodsList());
        if (goodsSaveResult == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B025, new Object[]{"未知错误"});
        }
        if (!goodsSaveResult.isEmpty()) {
            String tips = goodsSaveResult.entrySet().stream()
                    .map(e -> String.format("商品行[%s]%s", e.getKey(), e.getValue()))
                    .collect(Collectors.joining("; "));
            purgeDistPriceData(billDTO.getBillNo());
            throw new ScBizException(MdErrorCodeEnum.SCMD002B025, new Object[]{tips});
        }

        // 商品行明细信息
        billDTO.getGoodsList().forEach(goods -> {
            if (!CollectionUtils.isEmpty(goods.getDetailList())) {
                goods.getDetailList().forEach(detail -> {
                    detail.setBillNo(billDTO.getBillNo());
                    detail.setGoodsInsideId(goods.getInsideId());
                });
            }
            Map<Integer, String> detailSaveResult = batchSaveDistPriceDetail(billDTO, goods.getDetailList());
            if (detailSaveResult == null) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B026, new Object[]{"未知错误"});
            }
            if (!detailSaveResult.isEmpty()) {
                String tips = detailSaveResult.entrySet().stream()
                        .map(e -> String.format("商品明细行[%s]%s", e.getKey(), e.getValue()))
                        .collect(Collectors.joining("; "));
                purgeDistPriceData(billDTO.getBillNo());
                throw new ScBizException(MdErrorCodeEnum.SCMD002B026, new Object[]{tips});
            }
        });

        return mdDistPriceBillPO.getBillNo();
    }

    @Transactional
    @Override
    public String createAndSubmitDistPriceBill(MdDistPriceBillDTO billDTO) {
        String distPriceBillNo = this.createDistPriceBill(billDTO);
        this.submit(distPriceBillNo);

        return distPriceBillNo;
    }

    /**
     * 更新配送价格单
     * <AUTHOR>
     */
    @Transactional
    @Override
    public void updateDistPriceBill(MdDistPriceBillDTO distPriceBillDTO) {
        if (distPriceBillDTO == null || !StringUtils.hasText(distPriceBillDTO.getBillNo())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B027);
        }

        MdDistPriceBillPO billPO = mdDistPriceBillRepositoryService.lambdaQuery()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillDTO.getBillNo())
                .one();
        if (billPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B028, new Object[]{distPriceBillDTO.getBillNo()});
        }

        if (!MdDistPriceOrderStatusEnum.DRAFT.verifyByCode(billPO.getStatus())
                && !MdDistPriceOrderStatusEnum.PENDING_SUBMIT.verifyByCode(billPO.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B029);
        }

        purgeDistPriceData(distPriceBillDTO.getBillNo());

        // 保留原始创建人信息
        OperatorInfoHandler.useCreateUser(billPO);
        createDistPriceBill(distPriceBillDTO);
        OperatorInfoHandler.useDefaultLoginUser();
    }


    /**
     * 清理配送价格单
     * <AUTHOR>
     */
    @Override
    public void purgeDistPriceData(String distPriceBillNo) {
        mdDistPriceBillRepositoryService.purgeByBillNo(distPriceBillNo);
    }

    /**
     * 删除配送价格单及其相关明细数据
     * <AUTHOR>
     */
    @Transactional
    @Override
    public void deleteDistPriceBill(String distPriceBillNo) {
        mdDistPriceBillRepositoryService.remove(Wrappers.lambdaQuery(MdDistPriceBillPO.class).eq(MdDistPriceBillPO::getBillNo, distPriceBillNo));
        mdDistPriceGoodsRepositoryService.remove(Wrappers.lambdaQuery(MdDistPriceGoodsPO.class).eq(MdDistPriceGoodsPO::getBillNo, distPriceBillNo));
        mdDistPriceDetailRepositoryService.remove(Wrappers.lambdaQuery(MdDistPriceDetailPO.class).eq(MdDistPriceDetailPO::getBillNo, distPriceBillNo));
    }

    /**
     * 提交审核
     * <AUTHOR>
     */
    @Override
    @Transactional
    public boolean submit(MdDistPriceBillDTO distPriceBillDTO) {
        if (distPriceBillDTO == null || !StringUtils.hasText(distPriceBillDTO.getBillNo())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B027);
        }
        MdDistPriceBillPO mdDistPriceBillPO = mdDistPriceBillRepositoryService.lambdaQuery()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillDTO.getBillNo())
                .one();
        if (mdDistPriceBillPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B032);
        }
        if (!MdDistPriceOrderStatusEnum.DRAFT.verifyByCode(mdDistPriceBillPO.getStatus())
                && !MdDistPriceOrderStatusEnum.PENDING_SUBMIT.verifyByCode(mdDistPriceBillPO.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B030);
        }

        // 更新配送价格单
        this.updateDistPriceBill(distPriceBillDTO);

        // 更新状态
        return mdDistPriceBillRepositoryService.lambdaUpdate()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillDTO.getBillNo())
                .in(MdDistPriceBillPO::getStatus, Arrays.asList(MdDistPriceOrderStatusEnum.DRAFT.getCode(), MdDistPriceOrderStatusEnum.PENDING_SUBMIT.getCode()))
                .set(MdDistPriceBillPO::getStatus, MdDistPriceOrderStatusEnum.PENDING_REVIEW.getCode())
                .update();
    }

    @Override
    public boolean submit(String distPriceBillNo) {
        MdDistPriceBillPO mdDistPriceBillPO = mdDistPriceBillRepositoryService.lambdaQuery()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillNo)
                .one();
        if (mdDistPriceBillPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B032);
        }
        if (!MdDistPriceOrderStatusEnum.DRAFT.verifyByCode(mdDistPriceBillPO.getStatus())
                && !MdDistPriceOrderStatusEnum.PENDING_SUBMIT.verifyByCode(mdDistPriceBillPO.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B030);
        }
        // 更新状态
        return mdDistPriceBillRepositoryService.lambdaUpdate()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillNo)
                .in(MdDistPriceBillPO::getStatus, Arrays.asList(MdDistPriceOrderStatusEnum.DRAFT.getCode(), MdDistPriceOrderStatusEnum.PENDING_SUBMIT.getCode()))
                .set(MdDistPriceBillPO::getStatus, MdDistPriceOrderStatusEnum.PENDING_REVIEW.getCode())
                .update();
    }

    /**
     * 审核配送价格单
     * <AUTHOR>
     */
    @Transactional
    @Override
    public boolean audit(String distPriceBillNo, MdDistPriceOrderStatusEnum statusEnum, String remark) {
        if (!userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_MD_DIST_PRICE_AUDIT_BUTTON))) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B031);
        }

        if (MdDistPriceOrderStatusEnum.APPROVED != statusEnum && MdDistPriceOrderStatusEnum.REJECTED != statusEnum) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B032);
        }

        MdDistPriceBillPO mdDistPriceBillPO = mdDistPriceBillRepositoryService.lambdaQuery()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillNo)
                .one();
        if (mdDistPriceBillPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B032);
        }
        if (!MdDistPriceOrderStatusEnum.PENDING_REVIEW.verifyByCode(mdDistPriceBillPO.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B033);
        }

        LoginUserDTO operator = ClientIdentUtil.getLoginUser();

        // 目标状态
        MdDistPriceOrderStatusEnum targetStatus = statusEnum == MdDistPriceOrderStatusEnum.APPROVED
                ? MdDistPriceOrderStatusEnum.PENDING_EXECUTION
                : MdDistPriceOrderStatusEnum.PENDING_SUBMIT;

        boolean result = mdDistPriceBillRepositoryService.lambdaUpdate()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillNo)
                .eq(MdDistPriceBillPO::getStatus, MdDistPriceOrderStatusEnum.PENDING_REVIEW.getCode())
                .set(MdDistPriceBillPO::getStatus, targetStatus.getCode())
                .set(MdDistPriceBillPO::getAuditCode, operator.getCode())
                .set(MdDistPriceBillPO::getAuditName, operator.getName())
                .set(MdDistPriceBillPO::getAuditUid, operator.getUid())
                .set(MdDistPriceBillPO::getAuditTime, LocalDateTime.now())
                .set(true, MdDistPriceBillPO::getAuditRemark, remark)
                .update();
        if (!result) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B034);
        }

        // 记录审核流水
        try {
            MdCommonStatusProcessDTO auditRecord = MdCommonStatusProcessDTO.builder()
                    .moduleCode(MdCommonStatusModuleEnum.MD)
                    .moduleName(MdCommonStatusModuleEnum.MD.getDesc())
                    .bizId(mdDistPriceBillPO.getId())
                    .bizCode(distPriceBillNo)
                    .bizType(MdCommonStatusBizTypeEnum.DIST_PRICE_BILL)
                    .bizTime(LocalDateTime.now())
                    .statusCode(statusEnum.getCode().toString())
                    .statusName(statusEnum.getDesc())
                    .remark(remark)
                    .build();
            mdCommonStatusProcessService.uploadCommonStatusProcess(auditRecord);
        }catch (Exception e) {
            Logs.error("配送价格单 {} 记录审核流水失败，错误堆栈 {}", distPriceBillNo, e);
        }

        return targetStatus != MdDistPriceOrderStatusEnum.PENDING_EXECUTION || createNewDistPrice(TenantContext.get(), distPriceBillNo);
    }

    /**
     * 作废配送价格单
     * <AUTHOR>
     */
    @Transactional
    @Override
    public boolean invalidated(String distPriceBillNo) {
        MdDistPriceBillPO mdDistPriceBillPO = mdDistPriceBillRepositoryService.lambdaQuery()
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillNo)
                .one();

        if (mdDistPriceBillPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B035, new Object[]{distPriceBillNo});
        }

        if (!MdDistPriceOrderStatusEnum.DRAFT.verifyByCode(mdDistPriceBillPO.getStatus())
                && !MdDistPriceOrderStatusEnum.PENDING_SUBMIT.verifyByCode(mdDistPriceBillPO.getStatus())
                && !MdDistPriceOrderStatusEnum.PENDING_EXECUTION.verifyByCode(mdDistPriceBillPO.getStatus())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B036);
        }

        LambdaUpdateWrapper<MdDistPriceBillPO> updateWrapper = Wrappers.lambdaUpdate(MdDistPriceBillPO.class)
                .eq(MdDistPriceBillPO::getBillNo, distPriceBillNo)
                .in(MdDistPriceBillPO::getStatus, Arrays.asList(MdDistPriceOrderStatusEnum.DRAFT.getCode(),
                        MdDistPriceOrderStatusEnum.PENDING_SUBMIT.getCode(),
                        MdDistPriceOrderStatusEnum.PENDING_EXECUTION.getCode()))
                .set(MdDistPriceBillPO::getStatus, MdDistPriceOrderStatusEnum.INVALID.getCode());
        OperatorInfoHandler.fillUpdateInfo(updateWrapper);

        return mdDistPriceBillRepositoryService.update(updateWrapper) && invalidatePendingValidDistPrice(TenantContext.get(), distPriceBillNo);
    }


    /**
     * 根据审核通过的配送单据生成对应的配送价格单明细数据
     * <AUTHOR>
     */
    @Override
    public boolean createNewDistPrice(String tenantId, String distBillNo) {
        Preconditions.checkArgument(StringUtils.hasText(tenantId), "租户号不能为空");
        Preconditions.checkArgument(StringUtils.hasText(distBillNo), "配送价格单号不能为空");

        TenantContext.set(tenantId);

        boolean lockResult = lockManager.tryLock(LockConstants.DIST_PRICE_DETAIL_OPERATION_LOCK_PREFIX + distBillNo, 30, TimeUnit.SECONDS);
        if (!lockResult) {
            Logs.error("获取[创建配送价格单明细数据]分布式锁失败");
            return false;
        }

        try {
            MdDistPriceBillPO distPriceBillPO = mdDistPriceBillRepositoryService.lambdaQuery()
                    .eq(MdDistPriceBillPO::getBillNo, distBillNo)
                    .one();
            if (distPriceBillPO == null) {
                Logs.error("未查询到配送价格单 {}", distBillNo);
                return false;
            }
            if (!MdDistPriceOrderStatusEnum.PENDING_EXECUTION.verifyByCode(distPriceBillPO.getStatus())) {
                Logs.error("配送价格单 {} 当前状态为 {} 不允许生成配送价格单明细",
                        distBillNo,
                        StandardEnum.codeOfOptional(MdDistPriceOrderStatusEnum.class, distPriceBillPO.getStatus())
                                .map(MdDistPriceOrderStatusEnum::getDesc)
                                .orElse(String.format("未知状态编码 %s", distPriceBillPO.getStatus())));
                return false;
            }

            // 清理
            Integer purgeCount = mdDistPriceRepositoryService.purgeDistPriceDetail(distBillNo);
            if (purgeCount > 0) {
                Logs.warn("清理已存在的配送价格单 {} 明细数据 {} 条", distBillNo, purgeCount);
            }

            // 生成待生效的配送价格单明细
            mdDistPriceBillRepositoryService.billDetail2priceData(tenantId,
                    distBillNo,
                    ValidStatusEnum.PENDING_VALID.getCode(),
                    ValidStatusEnum.PENDING_VALID.getDesc());

            // 处理立即生效的配送价格单
            if (distPriceBillPO.getExecutionDatetime() != null && LocalDateTime.now().isAfter(distPriceBillPO.getExecutionDatetime())) {
                try {
                    IMdDistributionPriceDomainService currentService = SpringContextUtil.getApplicationContext().getBean(IMdDistributionPriceDomainService.class);
                    currentService.validatePendingValidDistPrice(tenantId, distBillNo);
                }catch (Exception e) {
                    Logs.info("处理配送价格单 {} 生效任务失败，错误堆栈 {}", distBillNo, e);
                }
            }
        }catch (Exception e) {
            Logs.error("处理[创建配送价格单明细数据]失败，单据号：{} 异常信息：{}", distBillNo, e);
            return false;
        }finally {
            lockManager.unlock(LockConstants.DIST_PRICE_DETAIL_OPERATION_LOCK_PREFIX + distBillNo);
            Logs.info("处理[创建配送价格单明细数据]完成，单据号：{}", distBillNo);
        }
        return true;
    }

    /**
     * 生效前作废指定单据的配送价格单明细
     * <AUTHOR>
     */
    @Override
    public boolean invalidatePendingValidDistPrice(String tenantId, String distBillNo) {
        Preconditions.checkArgument(StringUtils.hasText(tenantId), "租户号不能为空");
        Preconditions.checkArgument(StringUtils.hasText(distBillNo), "配送价格单号不能为空");

        TenantContext.set(tenantId);

        boolean lockResult = lockManager.tryLock(LockConstants.DIST_PRICE_DETAIL_OPERATION_LOCK_PREFIX + distBillNo, 30, TimeUnit.SECONDS);
        if (!lockResult) {
            Logs.error("获取[作废配送价格单明细数据]分布式锁失败");
            return false;
        }

        try {
            String operator = Optional.ofNullable(ClientIdentUtil.getLoginUser())
                    .map(LoginUserDTO::getName)
                    .orElse("SYSTEM");

            Integer invalidatedCount = mdDistPriceRepositoryService.updateDistPriceDetailValidStatus(distBillNo,
                    ValidStatusEnum.PENDING_VALID.getCode(),
                    ValidStatusEnum.INVALID.getCode(),
                    String.format("配送价格单已被 %s 作废", operator),
                    null,
                    LocalDateTime.now());

            Logs.info("配送价格单 {} 明细行作废条数 {}", distBillNo, invalidatedCount);
            return invalidatedCount > 0;
        }catch (Exception e) {
            Logs.error("处理[作废配送价格单明细数据]失败，单据号：{} 异常信息：{}", distBillNo, e);
            return false;
        }finally {
            lockManager.unlock(LockConstants.DIST_PRICE_DETAIL_OPERATION_LOCK_PREFIX + distBillNo);
            Logs.info("处理[作废配送价格单明细数据]完成，单据号：{}", distBillNo);
        }
    }

    /**
     * 生效指定单据的配送价格单明细
     * <AUTHOR>
     */
    @Transactional
    @Override
    public boolean validatePendingValidDistPrice(String tenantId, String distBillNo) {
        Preconditions.checkArgument(StringUtils.hasText(tenantId), "租户号不能为空");
        Preconditions.checkArgument(StringUtils.hasText(distBillNo), "配送价格单号不能为空");

        TenantContext.clear();
        TenantContext.set(tenantId);

        boolean lockResult = lockManager.tryLock(LockConstants.DIST_PRICE_DETAIL_OPERATION_LOCK_PREFIX + distBillNo, 30, 10, TimeUnit.SECONDS);
        if (!lockResult) {
            Logs.error("获取[生效配送价格单明细数据]分布式锁失败");
            return false;
        }

        try {
            MdDistPriceBillPO distPriceBillPO = mdDistPriceBillRepositoryService.lambdaQuery()
                    .eq(MdDistPriceBillPO::getBillNo, distBillNo)
                    .one();
            if (distPriceBillPO == null) {
                Logs.error("未查询到配送价格单 {}", distBillNo);
                return false;
            }

            MdDistPriceExecutionTypeEnum mdDistPriceExecutionTypeEnum = StandardEnum.codeOf(MdDistPriceExecutionTypeEnum.class, distPriceBillPO.getExecutionType());
            if (mdDistPriceExecutionTypeEnum != MdDistPriceExecutionTypeEnum.NO_COVERAGE) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B037);
            }

            if (!MdDistPriceOrderStatusEnum.PENDING_EXECUTION.verifyByCode(distPriceBillPO.getStatus())) {
                Logs.error("配送价格单 {} 当前状态为 {} 不允许对应配送价格单明细生效",
                        distBillNo,
                        StandardEnum.codeOfOptional(MdDistPriceOrderStatusEnum.class, distPriceBillPO.getStatus())
                                .map(MdDistPriceOrderStatusEnum::getDesc)
                                .orElse(String.format("未知状态编码 %s", distPriceBillPO.getStatus())));
                return false;
            }
            List<MdDistPricePO> distPricePOList = mdDistPriceRepositoryService.lambdaQuery()
                    .eq(MdDistPricePO::getBillNo, distBillNo)
                    .eq(MdDistPricePO::getValid, ValidStatusEnum.PENDING_VALID.getCode())
                    .list();
            if (CollectionUtils.isEmpty(distPricePOList)) {
                Logs.error("配送价格单 {} 不存在待生效的明细数据", distBillNo);
                return false;
            }

            MdDistPriceDefineTypeEnum mdDistPriceDefineTypeEnum = StandardEnum.codeOf(MdDistPriceDefineTypeEnum.class, distPriceBillPO.getType());

            // 失效旧的配送价格单数据
            if (mdDistPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
                mdDistPriceBillRepositoryService.invalidateSkuClassPriceData(distBillNo, String.format("新配送价格单 %s 生效", distBillNo));
            }
            if (mdDistPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_PRODUCT) {
                mdDistPriceBillRepositoryService.invalidateSkuPriceData(distBillNo, String.format("新配送价格单 %s 生效", distBillNo));
            }

            // 生效新的配送价格单数据
            Integer updateCount = mdDistPriceRepositoryService.updateDistPriceDetailValidStatus(distBillNo,
                    ValidStatusEnum.PENDING_VALID.getCode(),
                    ValidStatusEnum.VALID.getCode(),
                    String.format("配送价格单据 %s 于 %s 生效", distBillNo, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))),
                    LocalDateTime.now(),
                    null);
            if (updateCount <= 0) {
                throw new ScBizException(MdErrorCodeEnum.SCMD002B038, new Object[]{distBillNo});
            }

            // 回写单据状态
            mdDistPriceBillRepositoryService.lambdaUpdate()
                    .eq(MdDistPriceBillPO::getBillNo, distBillNo)
                    .eq(MdDistPriceBillPO::getStatus, MdDistPriceOrderStatusEnum.PENDING_EXECUTION.getCode())
                    .set(MdDistPriceBillPO::getStatus, MdDistPriceOrderStatusEnum.EFFECTIVE.getCode())
                    .set(MdDistPriceBillPO::getExecutionDatetime, LocalDateTime.now())
                    .update();

            /*
            // 部门覆盖
            if (mdDistPriceExecutionTypeEnum == MdDistPriceExecutionTypeEnum.DEPARTMENT_COVERAGE) {
                // TODO: 2025/5/7 前缀树优化
                List<MdDistPricePO> deptGroupItemList = distPricePOList.stream()
                        .filter(mdDistPricePO -> MdDeptTypeEnum.DEPT_GROUP.verifyByCode(mdDistPricePO.getDeptType()))
                        .collect(Collectors.toList());
                List<String> existDeptCodeList = distPricePOList.stream()
                        .filter(mdDistPricePO -> MdDeptTypeEnum.DEPT.verifyByCode(mdDistPricePO.getDeptType()))
                        .map(MdDistPricePO::getDeptCode)
                        .collect(Collectors.toList());

                QueryAllDeptDataReq queryAllDeptDataReq = QueryAllDeptDataReq.builder()
                        .classCode("0000")
                        .deepSearch(true)
                        .groupCodeList(deptGroupItemList.stream().map(MdDistPricePO::getDeptCode).collect(Collectors.toSet()))
                        .build();
                List<QueryAllDeptDataResp.DeptInfo> deptInfoList = baseDataSystemFeignClient.queryAllDeptData(queryAllDeptDataReq).getRows();

                if (mdDistPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_PRODUCT) {
                    for (MdDistPricePO mdDistPricePO : deptGroupItemList) {
                        // 获取该店组群下面的门店
                        List<String> targetDeptCodeList = deptInfoList.stream()
                                .filter(deptInfo -> deptInfo.getGroupCode().startsWith(mdDistPricePO.getDeptCode()))
                                .map(QueryAllDeptDataResp.DeptInfo::getDeptCode)
                                .collect(Collectors.toList());
                        mdDistPriceRepositoryService.restoreDistSkuPriceForDept(mdDistPricePO,
                                String.format("应用单据 %s 覆盖规则待生效", distBillNo),
                                // 排除存在单独配置的门店避免重复
                                targetDeptCodeList.stream().filter(deptCode -> !existDeptCodeList.contains(deptCode)).collect(Collectors.toList()),
                                TenantContext.get());
                        mdDistPriceRepositoryService.invalidateDistSkuPriceForDept(String.format("应用单据 %s 覆盖规则失效", distBillNo),
                                mdDistPricePO.getWhCode(),
                                mdDistPricePO.getSkuCode(),
                                targetDeptCodeList);
                    }
                    mdDistPriceRepositoryService.invalidateDistSkuPriceForAllDept(String.format("新单据 %s 生效", distBillNo), distPricePOList);
                }
                if (mdDistPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
                    for (MdDistPricePO mdDistPricePO : deptGroupItemList) {
                        // 获取该店组群下面的门店
                        List<String> targetDeptCodeList = deptInfoList.stream()
                                .filter(deptInfo -> deptInfo.getGroupCode().startsWith(mdDistPricePO.getDeptCode()))
                                .map(QueryAllDeptDataResp.DeptInfo::getDeptCode)
                                .collect(Collectors.toList());
                        mdDistPriceRepositoryService.restoreDistSkuClassPriceForDept(mdDistPricePO,
                                String.format("应用单据 %s 覆盖规则待生效", distBillNo),
                                // 排除存在单独配置的门店避免重复
                                targetDeptCodeList.stream().filter(deptCode -> !existDeptCodeList.contains(deptCode)).collect(Collectors.toList()),
                                TenantContext.get());
                        mdDistPriceRepositoryService.invalidateDistSkuClassPriceForDept(String.format("应用单据 %s 覆盖规则失效", distBillNo),
                                mdDistPricePO.getWhCode(),
                                mdDistPricePO.getSkuCode(),
                                targetDeptCodeList);
                    }
                    mdDistPriceRepositoryService.invalidateDistSkuClassPriceForSkuClass(String.format("新单据 %s 生效", distBillNo), distPricePOList);
                }
            }
            // 商品覆盖
            if (mdDistPriceExecutionTypeEnum == MdDistPriceExecutionTypeEnum.DEPARTMENT_COVERAGE
                    && mdDistPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
                for (MdDistPricePO mdDistPricePO : distPricePOList) {
                    mdDistPriceRepositoryService.restoreDistSkuClassPriceForSku(mdDistPricePO,
                            String.format("应用单据 %s 覆盖规则待生效", distBillNo),
                            mdDistPricePO.getSkuClassCode(),
                            TenantContext.get());
                    // 失效归属到该品类的商品
                    mdDistPriceRepositoryService.invalidateDistSkuClassPriceForSku(String.format("应用单据 %s 覆盖规则失效", distBillNo),
                            mdDistPricePO.getWhCode(),
                            mdDistPricePO.getDeptCode(),
                            mdDistPricePO.getSkuClassCode());
                }
                // 失效品类数据
                mdDistPriceRepositoryService.invalidateDistSkuClassPriceForSkuClass(String.format("新单据 %s 生效", distBillNo), distPricePOList);
            }
            // 全覆盖
            if (mdDistPriceExecutionTypeEnum == MdDistPriceExecutionTypeEnum.FULL_COVERAGE
                    && mdDistPriceDefineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
                // TODO: 2025/5/7 前缀树优化
                List<MdDistPricePO> deptGroupItemList = distPricePOList.stream()
                        .filter(mdDistPricePO -> MdDeptTypeEnum.DEPT_GROUP.verifyByCode(mdDistPricePO.getDeptType()))
                        .collect(Collectors.toList());
                List<String> existDeptCodeList = distPricePOList.stream()
                        .filter(mdDistPricePO -> MdDeptTypeEnum.DEPT.verifyByCode(mdDistPricePO.getDeptType()))
                        .map(MdDistPricePO::getDeptCode)
                        .collect(Collectors.toList());

                QueryAllDeptDataReq queryAllDeptDataReq = QueryAllDeptDataReq.builder()
                        .classCode("0000")
                        .deepSearch(true)
                        .groupCodeList(deptGroupItemList.stream().map(MdDistPricePO::getDeptCode).collect(Collectors.toSet()))
                        .build();
                List<QueryAllDeptDataResp.DeptInfo> deptInfoList = baseDataSystemFeignClient.queryAllDeptData(queryAllDeptDataReq).getRows();

                for (MdDistPricePO mdDistPricePO : distPricePOList) {
                    if (MdDeptTypeEnum.DEPT_GROUP.verifyByCode(mdDistPricePO.getDeptType())) {
                        // 获取该店组群下面的门店
                        List<String> targetDeptCodeList = deptInfoList.stream()
                                .filter(deptInfo -> deptInfo.getGroupCode().startsWith(mdDistPricePO.getDeptCode()))
                                .map(QueryAllDeptDataResp.DeptInfo::getDeptCode)
                                .collect(Collectors.toList());
                        mdDistPriceRepositoryService.restoreDistSkuClassPriceForAll(mdDistPricePO,
                                String.format("应用单据 %s 覆盖规则待生效", distBillNo),
                                targetDeptCodeList.stream().filter(deptCode -> !existDeptCodeList.contains(deptCode)).collect(Collectors.toList()),
                                mdDistPricePO.getSkuClassCode(),
                                TenantContext.get());
                        mdDistPriceRepositoryService.invalidateDistSkuClassPriceForAll(String.format("应用单据 %s 覆盖规则失效", distBillNo),
                                mdDistPricePO.getWhCode(),
                                targetDeptCodeList,
                                mdDistPricePO.getSkuClassCode());
                    }
                    if (MdDeptTypeEnum.DEPT.verifyByCode(mdDistPricePO.getDeptType())) {
                        mdDistPriceRepositoryService.restoreDistSkuClassPriceForSku(mdDistPricePO,
                                String.format("应用单据 %s 覆盖规则待生效", distBillNo),
                                mdDistPricePO.getSkuClassCode(),
                                TenantContext.get());
                        mdDistPriceRepositoryService.invalidateDistSkuClassPriceForSku(String.format("应用单据 %s 覆盖规则失效", distBillNo),
                                mdDistPricePO.getWhCode(),
                                mdDistPricePO.getDeptCode(),
                                mdDistPricePO.getSkuClassCode());
                    }
                }
                // 失效品类数据
                mdDistPriceRepositoryService.invalidateDistSkuClassPriceForSkuClass(String.format("新单据 %s 生效", distBillNo), distPricePOList);
            }

            // 刷新单据状态
            mdDistPriceRepositoryService.lambdaUpdate()
                    .eq(MdDistPricePO::getBillNo, distBillNo)
                    .eq(MdDistPricePO::getValid, ValidStatusEnum.PENDING_VALID.getCode())
                    .set(MdDistPricePO::getValid, ValidStatusEnum.VALID.getCode())
                    .set(MdDistPricePO::getActivationDatetime, LocalDateTime.now());
             */
        }catch (Exception e) {
            Logs.error("处理[生效配送价格单明细数据]失败，单据号：{} 异常信息：{}", distBillNo, e);
            return false;
        }finally {
            lockManager.unlock(LockConstants.DIST_PRICE_DETAIL_OPERATION_LOCK_PREFIX + distBillNo);
            Logs.info("处理[生效配送价格单明细数据]完成，单据号：{}", distBillNo);
        }
        return true;
    }

    /**
     * 查询已存在的配送价格相关数据
     * <AUTHOR>
     */
    @Override
    public List<MdDistPriceDTO> listExistDistPriceData(ExistDistPriceDataReq existDistPriceDataReq) {
        MdDistPriceDefineTypeEnum defineTypeEnum = StandardEnum.codeOf(MdDistPriceDefineTypeEnum.class, existDistPriceDataReq.getStatementType());
        if (defineTypeEnum == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD002B039);
        }

        // 店组群数据
        Map<String, String> mappingDeptGroupToSkuInfo = existDistPriceDataReq.getDeptParamList().stream()
                .filter(deptParam -> MdDeptTypeEnum.DEPT_GROUP.verifyByCode(deptParam.getDeptType()))
                .collect(Collectors.toMap(
                        ExistDistPriceDataReq.DeptParam::getDeptCode,
                        ExistDistPriceDataReq.DeptParam::getSkuOrSkuClassCode,
                        (p1, p2) -> p1));
        // 查询店组群下级店组群数据
        QueryDeptTreeReq.QueryFilter build = QueryDeptTreeReq.QueryFilter.builder()
                .classCode("0000")
                .codeList(new ArrayList<>(mappingDeptGroupToSkuInfo.keySet()))
                .build();
        QueryDeptTreeReq queryDeptTreeReq = QueryDeptTreeReq.builder().queryFilter(build).build();
        List<QueryDeepDeptGroupListResp.DeptGroupItem> deptGroupItemList = baseDataSystemFeignClient.queryDeepDeptGroupList(queryDeptTreeReq).getRows();

        Map<String, List<QueryDeepDeptGroupListResp.DeptGroupItem>> mappingParentCodeToDeptGroupList
                = deptGroupItemList.stream()
                .filter(deptGroupItem -> deptGroupItem.getParentCode() != null)
                .collect(Collectors.groupingBy(QueryDeepDeptGroupListResp.DeptGroupItem::getParentCode));
        // 按照层级倒叙排列
        deptGroupItemList.sort((o1, o2) -> o2.getLevel() - o1.getLevel());
        // 构建父子层级包含结构
        deptGroupItemList.forEach(deptGroupItem -> {
            Set<QueryDeepDeptGroupListResp.DeptGroupItem> subDeptGroupItems
                    = new HashSet<>(mappingParentCodeToDeptGroupList.getOrDefault(deptGroupItem.getCode(), Collections.emptyList()));
            deptGroupItem.getSubDeptGroupSet().addAll(subDeptGroupItems);
            subDeptGroupItems.forEach(deptGroupItemSubDept -> processMappingDeptGroupCodeToSub(deptGroupItem, deptGroupItemSubDept));
        });
        Map<String, Set<String>> mappingDeptGroupCodeToSub = deptGroupItemList.stream().collect(Collectors.toMap(
                QueryDeepDeptGroupListResp.DeptGroupItem::getCode,
                deptGroupItem -> deptGroupItem.getSubDeptGroupSet().stream().map(QueryDeepDeptGroupListResp.DeptGroupItem::getCode).collect(Collectors.toSet()),
                (g1, g2) -> g1));

        // 处理店组群查询条件
        if (!mappingDeptGroupToSkuInfo.isEmpty()) {
            QueryAllDeptDataReq queryAllDeptDataReq = QueryAllDeptDataReq.builder()
                    .classCode("0000")
                    .deepSearch(true)
                    .groupCodeList(mappingDeptGroupToSkuInfo.keySet())
                    .build();
            List<QueryAllDeptDataResp.DeptInfo> deptInfoList = baseDataSystemFeignClient.queryAllDeptData(queryAllDeptDataReq).getRows();

            mappingDeptGroupToSkuInfo.forEach((deptGroupCode, skuOrSkuClassCode) -> {
                // 当前店组群的下级店组群
                Set<String> subDeptGroupSet = mappingDeptGroupCodeToSub.getOrDefault(deptGroupCode, new HashSet<>());
                // 处理具体门店查询条件
                deptInfoList.forEach(deptInfo -> {
                    if (!Objects.equals(deptGroupCode, deptInfo.getGroupCode()) && !subDeptGroupSet.contains(deptInfo.getGroupCode())) {
                        return;
                    }
                    ExistDistPriceDataReq.DeptParam deptParam = ExistDistPriceDataReq.DeptParam.builder()
                            .deptType(MdDeptTypeEnum.DEPT.getCode())
                            .deptCode(deptInfo.getDeptCode())
                            .skuOrSkuClassCode(skuOrSkuClassCode)
                            .build();
                    existDistPriceDataReq.getDeptParamList().add(deptParam);
                });
                // 处理下级店组群查询条件
                subDeptGroupSet.forEach(subDeptGroupCode -> {
                    ExistDistPriceDataReq.DeptParam deptParam = ExistDistPriceDataReq.DeptParam.builder()
                            .deptType(MdDeptTypeEnum.DEPT_GROUP.getCode())
                            .deptCode(subDeptGroupCode)
                            .skuOrSkuClassCode(skuOrSkuClassCode)
                            .build();
                    existDistPriceDataReq.getDeptParamList().add(deptParam);
                });
            });
        }

        if (defineTypeEnum == MdDistPriceDefineTypeEnum.BY_PRODUCT) {
            return mdDistPriceRepositoryService.listValidDistPriceForSku(existDistPriceDataReq).stream()
                    .map(MdDistPriceDtoConvert.INSTANCE::po2dto)
                    .collect(Collectors.toList());
        }

        if (defineTypeEnum == MdDistPriceDefineTypeEnum.BY_CATEGORY) {
            return mdDistPriceRepositoryService.listValidDistPriceForSkuClass(existDistPriceDataReq).stream()
                    .map(MdDistPriceDtoConvert.INSTANCE::po2dto)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 递归处理，将子节点中包含的店组群添加到父节点
     * <AUTHOR>
     */
    private void processMappingDeptGroupCodeToSub(QueryDeepDeptGroupListResp.DeptGroupItem target, QueryDeepDeptGroupListResp.DeptGroupItem sub) {
        if (sub == null || CollectionUtils.isEmpty(sub.getSubDeptGroupSet())) {
            return;
        }
        target.getSubDeptGroupSet().addAll(sub.getSubDeptGroupSet());
        sub.getSubDeptGroupSet().forEach(deptGroupItem -> processMappingDeptGroupCodeToSub(target, deptGroupItem));
    }


    @Override
    public MdDistPriceBillDTO queryBillDetail(String billNo) {
        if (!StringUtils.hasText(billNo)) {
            return null;
        }

        MdDistPriceBillPO billPO = mdDistPriceBillRepositoryService.lambdaQuery()
                .eq(MdDistPriceBillPO::getBillNo, billNo)
                .one();
        if (billPO == null) {
            return null;
        }

        MdDistPriceBillDTO bill = MdDistPriceBillConvert.INSTANCE.po2dto(billPO);

        List<MdDistPriceGoodsDTO> goodsList = mdDistPriceGoodsRepositoryService.lambdaQuery()
                .eq(MdDistPriceGoodsPO::getBillNo, billNo)
                .list()
                .stream()
                .map(MdDistPriceGoodsConvert.INSTANCE::po2dto)
                .collect(Collectors.toList());

        Map<Integer, List<MdDistPriceDetailDTO>> mappingGoodsInlineIdToDetails = mdDistPriceDetailRepositoryService.lambdaQuery()
                .eq(MdDistPriceDetailPO::getBillNo, billNo)
                .list()
                .stream()
                .map(MdDistPriceDetailConvert.INSTANCE::po2dto)
                .collect(Collectors.groupingBy(MdDistPriceDetailDTO::getGoodsInsideId));

        goodsList.forEach(goods -> goods.setDetailList(mappingGoodsInlineIdToDetails.get(goods.getInsideId())));

        bill.setGoodsList(goodsList);
        return bill;
    }

    @Override
    public void handleTakeEffectTask(LocalDateTime time) {
        boolean lockResult = lockManager.tryLock(LockConstants.DIST_PRICE_BILL_TAKE_EFFECT_LOCK, 10, TimeUnit.MINUTES);
        if (!lockResult) {
            Logs.error("处理配送价格单生效任务正在执行");
            return;
        }

        try {
            List<MdDistPriceBillPO> mdDistPriceBillList = mdDistPriceBillRepositoryService.listPendingTakeEffectBillNo(time);

            ArrayList<CompletableFuture<Void>> taskList = new ArrayList<>();

            // distPriceBillTakeEffectThreadPool 拒绝策略为Caller Run Policy，
            // 核心线程为0 最大线程为 AVAILABLE_PROCESSORS_COUNT * 4
            // 整体配置相当于一个Cached Thread Pool，避免同时处理太多数据导致OOM
            mdDistPriceBillList.forEach(mdDistPriceBillPO -> {
                CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                    Logs.info("开始处理配送价格单 {} 生效任务", mdDistPriceBillPO.getBillNo());
                    try {
                        IMdDistributionPriceDomainService currentService = SpringContextUtil.getApplicationContext().getBean(IMdDistributionPriceDomainService.class);
                        currentService.validatePendingValidDistPrice(String.valueOf(mdDistPriceBillPO.getTenantId()), mdDistPriceBillPO.getBillNo());
                    } catch (Exception e) {
                        Logs.info("处理配送价格单 {} 生效任务失败，错误堆栈 {}", mdDistPriceBillPO.getBillNo(), e);
                    }
                    Logs.info("处理配送价格单 {} 生效任务结束", mdDistPriceBillPO.getBillNo());
                }, distPriceBillTakeEffectThreadPool);

                taskList.add(task);
            });

            // 避免锁提前释放
            CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).get();
        }catch (Exception e) {
            Logs.error("处理配送价格单生效任务失败，异常信息：{}", e);
        }finally {
            lockManager.unlock(LockConstants.DIST_PRICE_BILL_TAKE_EFFECT_LOCK);
            Logs.info("处理配送价格单生效任务完成");
        }
    }

    /**
     * 部门商品配送中心查询有效价格配置
     * @param queryValidDistPriceReq 部门商品配送中心
     * @return
     */
    @Override
    public List<MdDistPriceDTO> queryValidDistPrice(QueryValidDistPriceReq queryValidDistPriceReq) {

        List<MdDistPriceDTO> result = Lists.newArrayList();

        QueryValidDistPriceReq distPriceReq = CglibCopier.copy(queryValidDistPriceReq, QueryValidDistPriceReq.class);
        List<MdDistPricePO> mdDistPricePOS = mdDistPriceRepositoryService.listValidDistPriceByDept(distPriceReq);
        if(!CollectionUtils.isEmpty(mdDistPricePOS)) {
            result.addAll(MdDistPriceDtoConvert.INSTANCE.poList2dto(mdDistPricePOS));
        }

        List<String> skuCodes = mdDistPricePOS.stream().map(MdDistPricePO::getSkuCode).collect(Collectors.toList());

        List<ValidGoodsInfo> validGoodsInfos = queryValidDistPriceReq.getGoodsInfos().stream().filter(e -> !skuCodes.contains(e.getSkuCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(validGoodsInfos)) {
            return result;
        }

        QueryUpDeptListReq queryUpDeptListReq = new QueryUpDeptListReq();
        queryUpDeptListReq.setDeptCode(queryValidDistPriceReq.getDeptCode());
        queryUpDeptListReq.setClassCode(GroupDeptEnum.FIX_PRICE_GROUP.getCode());
        queryUpDeptListReq.setOpenStatus(1);
        //查出当前部门下店组群 用于下面批量查询
        QueryUpDeptListResp resp = baseDataSystemFeignClient.queryUpDeptList(queryUpDeptListReq);
        List<String> codes = Lists.newArrayList();
        if (resp != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(resp.getRows())) {
            codes = resp.getRows().stream().map(QueryUpDeptListResp.UpDeptInfo::getCode).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(codes)) {
            return result;
        }

        distPriceReq.setDeptGroupCodes(codes);
        distPriceReq.setGoodsInfos(validGoodsInfos);

        List<MdDistPricePO> mdDistPricePOSByDeptGroup = mdDistPriceRepositoryService.listValidDistPriceByDeptGroup(distPriceReq);
        if(!CollectionUtils.isEmpty(mdDistPricePOSByDeptGroup)) {
            result.addAll(MdDistPriceDtoConvert.INSTANCE.poList2dto(mdDistPricePOSByDeptGroup));
        }

        return result;
    }


    /**
     * 配送价格单取价逻辑
     */
    @Override
    public List<MdDistPriceDTO> queryValidDistPrice(QueryDistPriceReq condition) {
        if (condition == null || !StringUtils.hasText(condition.getDeptCode()) || CollectionUtils.isEmpty(condition.getSkuList())) {
            return Collections.emptyList();
        }

        Set<QueryDistPriceComplexReq.SkuCodeAndWhCode> legalSkuCondition = condition.getSkuList().stream()
                .filter(sku -> StringUtils.hasText(sku.getSkuCode()) && StringUtils.hasText(sku.getWhCode()))
                .collect(Collectors.toSet());
        condition.setSkuList(legalSkuCondition);

        // 具体商品 & 具体部门
        QueryDistPriceComplexReq.DeptCodeAndType singleDeptCondition = QueryDistPriceComplexReq.DeptCodeAndType.builder()
                .deptCode(condition.getDeptCode())
                .deptType(MdDeptTypeEnum.DEPT.getCode())
                .build();
        QueryDistPriceComplexReq multipleCondition = QueryDistPriceComplexReq.builder()
                .statementType(MdDistPriceDefineTypeEnum.BY_PRODUCT.getCode())
                .deptCodeAndTypes(Collections.singleton(singleDeptCondition))
                .skuCodeAndWhCodes(condition.getSkuList())
                .build();

        Map<QueryDistPriceComplexReq.SkuCodeAndWhCode, MdDistPriceDTO> mappingSkuToPrice = mdDistPriceRepositoryService.selectDistPriceByComplexConditions(multipleCondition).stream()
                .map(MdDistPriceDtoConvert.INSTANCE::po2dto)
                .collect(Collectors.toMap(dto -> QueryDistPriceComplexReq.SkuCodeAndWhCode.builder()
                                .whCode(dto.getWhCode())
                                .skuCode(dto.getSkuCode())
                                .build(),
                        Function.identity(),
                        (s1, s2) -> s1));

        condition.getSkuList().removeAll(mappingSkuToPrice.keySet());
        if (CollectionUtils.isEmpty(condition.getSkuList())) {
            return Lists.newArrayList(mappingSkuToPrice.values());
        }

        // 具体商品 & 多级店组群
        QueryUpDeptListReq queryGroupReq = QueryUpDeptListReq.builder()
                .deptCode(condition.getDeptCode())
                .classCode(GroupDeptEnum.FIX_PRICE_GROUP.getCode())
                .openStatus(1)
                .build();
        List<QueryUpDeptListResp.UpDeptInfo> deptGroupList = Optional.ofNullable(baseDataSystemFeignClient.queryUpDeptList(queryGroupReq))
                .map(QueryUpDeptListResp::getRows)
                .orElse(Collections.emptyList());
        // 店组群级别
        Map<String, Integer> mappingDeptGroupToLevel = deptGroupList.stream()
                .collect(Collectors.toMap(QueryUpDeptListResp.UpDeptInfo::getCode, QueryUpDeptListResp.UpDeptInfo::getLevel));
        // 店组群查询条件
        Set<QueryDistPriceComplexReq.DeptCodeAndType> deptCondition = deptGroupList.stream()
                .map(QueryUpDeptListResp.UpDeptInfo::getCode)
                .map(groupCode -> QueryDistPriceComplexReq.DeptCodeAndType.builder()
                        .deptCode(groupCode)
                        .deptType(MdDeptTypeEnum.DEPT_GROUP.getCode())
                        .build())
                .collect(Collectors.toSet());

        if (!deptGroupList.isEmpty()) {
            multipleCondition = QueryDistPriceComplexReq.builder()
                    .statementType(MdDistPriceDefineTypeEnum.BY_PRODUCT.getCode())
                    .deptCodeAndTypes(deptCondition)
                    .skuCodeAndWhCodes(condition.getSkuList())
                    .build();

            mdDistPriceRepositoryService.selectDistPriceByComplexConditions(multipleCondition).stream()
                    .map(MdDistPriceDtoConvert.INSTANCE::po2dto)
                    .sorted((d1, d2) -> mappingDeptGroupToLevel.get(d2.getDeptCode()) - mappingDeptGroupToLevel.get(d1.getDeptCode()))
                    .forEach(dto -> {
                        QueryDistPriceComplexReq.SkuCodeAndWhCode skuKey = QueryDistPriceComplexReq.SkuCodeAndWhCode.builder()
                                .skuCode(dto.getSkuCode())
                                .whCode(dto.getWhCode())
                                .build();
                        mappingSkuToPrice.putIfAbsent(skuKey, dto);
                    });

            condition.getSkuList().removeAll(mappingSkuToPrice.keySet());
            if (CollectionUtils.isEmpty(condition.getSkuList())) {
                return Lists.newArrayList(mappingSkuToPrice.values());
            }
        }


        legalSkuCondition = condition.getSkuList().stream()
                .filter(sku -> StringUtils.hasText(sku.getSkuClassCode()))
                .collect(Collectors.toSet());
        condition.setSkuList(legalSkuCondition);

        // 商品多级品类 & 具体部门
        Set<String> skuClassCodeList = condition.getSkuList().stream()
                .map(QueryDistPriceComplexReq.SkuCodeAndWhCode::getSkuClassCode)
                .collect(Collectors.toSet());
        Set<String> skuWhCodeList = condition.getSkuList().stream()
                .map(QueryDistPriceComplexReq.SkuCodeAndWhCode::getWhCode)
                .collect(Collectors.toSet());

        multipleCondition = QueryDistPriceComplexReq.builder()
                .statementType(MdDistPriceDefineTypeEnum.BY_CATEGORY.getCode())
                .deptCodeAndTypes(Collections.singleton(singleDeptCondition))
                .skuClassCodeList(skuClassCodeList)
                .whCodes(skuWhCodeList)
                .build();
        mdDistPriceRepositoryService.selectDistPriceByComplexConditions(multipleCondition).stream()
                .map(MdDistPriceDtoConvert.INSTANCE::po2dto)
                .sorted((s1, s2) -> s2.getSkuClassCode().length() - s1.getSkuClassCode().length())
                .forEach(dto -> {
                    condition.getSkuList().forEach(sku -> {
                        if (!dto.getWhCode().equals(sku.getWhCode()) || !sku.getSkuClassCode().startsWith(dto.getSkuClassCode())) {
                            return;
                        }
                        dto.setSkuCode(sku.getSkuCode());
                        mappingSkuToPrice.putIfAbsent(sku, dto);
                    });
                    condition.getSkuList().removeAll(mappingSkuToPrice.keySet());
                });

        if (CollectionUtils.isEmpty(condition.getSkuList()) || CollectionUtils.isEmpty(deptGroupList)) {
            return Lists.newArrayList(mappingSkuToPrice.values());
        }

        // 商品多级品类 & 多级店组群
        skuClassCodeList = condition.getSkuList().stream()
                .map(QueryDistPriceComplexReq.SkuCodeAndWhCode::getSkuClassCode)
                .collect(Collectors.toSet());
        skuWhCodeList = condition.getSkuList().stream()
                .map(QueryDistPriceComplexReq.SkuCodeAndWhCode::getWhCode)
                .collect(Collectors.toSet());

        multipleCondition = QueryDistPriceComplexReq.builder()
                .statementType(MdDistPriceDefineTypeEnum.BY_CATEGORY.getCode())
                .deptCodeAndTypes(deptCondition)
                .skuClassCodeList(skuClassCodeList)
                .whCodes(skuWhCodeList)
                .build();
        mdDistPriceRepositoryService.selectDistPriceByComplexConditions(multipleCondition).stream()
                .map(MdDistPriceDtoConvert.INSTANCE::po2dto)
                .sorted((d1, d2) -> mappingDeptGroupToLevel.get(d2.getDeptCode()) - mappingDeptGroupToLevel.get(d1.getDeptCode()))
                .sorted((s1, s2) -> s2.getSkuClassCode().length() - s1.getSkuClassCode().length())
                .forEach(dto -> {
                    condition.getSkuList().forEach(sku -> {
                        if (!dto.getWhCode().equals(sku.getWhCode()) || !sku.getSkuClassCode().startsWith(dto.getSkuClassCode())) {
                            return;
                        }
                        dto.setSkuCode(sku.getSkuCode());
                        mappingSkuToPrice.putIfAbsent(sku, dto);
                    });
                    condition.getSkuList().removeAll(mappingSkuToPrice.keySet());
                });

        return Lists.newArrayList(mappingSkuToPrice.values());
    }

}
