package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdDeliveryAppointmentStrategyDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.convert.md.MdDeliveryAppointmentCloseDateConvert;
import com.meta.supplychain.convert.md.MdDeliveryAppointmentStrategyConvert;
import com.meta.supplychain.convert.md.MdDeliveryDockStrategyConvert;
import com.meta.supplychain.entity.dto.bds.req.QueryBatchDeptListReq;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.bds.resp.StoreDetailResp;
import com.meta.supplychain.entity.dto.md.deliveryappointment.*;
import com.meta.supplychain.entity.dto.md.req.delivery.MdDeliveryAppointmentCloseDateBatchCreateReq;
import com.meta.supplychain.entity.dto.md.req.delivery.MdDeliveryAppointmentStrategyPageQueryReq;
import com.meta.supplychain.entity.dto.md.req.demandstrategyexclude.MdDeliveryDockStrategyQueryReq;
import com.meta.supplychain.entity.dto.md.resp.deliveryappointment.DeliveryAppointmentStrategyResultDTO;
import com.meta.supplychain.entity.po.md.*;
import com.meta.supplychain.enums.GroupDeptEnum;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.enums.md.MdAppointmentStrategyEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.md.MdDeliveryDockDefineTypeEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import com.meta.supplychain.infrastructure.mybatis.OperatorInfoHandler;
import com.meta.supplychain.infrastructure.repository.service.intf.md.*;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 供应商预约策略
 * <AUTHOR>
 */
@Service
public class MdDeliveryAppointmentStrategyDomainServiceImpl implements IMdDeliveryAppointmentStrategyDomainService {

    @Resource
    private IMdDeliveryAppointmentStrategyRepositoryService repositoryService;
    
    @Resource
    private IMdDeliveryAppointmentCloseDateRepositoryService closeDateRepositoryService;
    
    @Resource
    private IMdDeliveryDockStrategyRepositoryService dockStrategyRepositoryService;
    
    @Resource
    private IMdDeliveryDockDeptRepositoryService dockDeptRepositoryService;
    
    @Resource
    private IMdDeliveryDockGoodsCategoryRepositoryService dockGoodsCategoryRepositoryService;
    
    @Resource
    private IMdDeliveryDockLimitRuleRepositoryService dockLimitRuleRepositoryService;

    @Resource
    private BaseDataSystemFeignClient baseDataSystemFeignClient;

    /**
     * 供应商预约策略单据查询
     */
    @Override
    public PageResult<MdDeliveryAppointmentStrategyDTO> pageQuery(MdDeliveryAppointmentStrategyPageQueryReq req) {
        LambdaQueryWrapper<MdDeliveryAppointmentStrategyPO> condition = Wrappers.lambdaQuery(MdDeliveryAppointmentStrategyPO.class)
                .eq(StandardEnum.codeOf(MdAppointmentStrategyEnum.class, req.getStatus()) != null, MdDeliveryAppointmentStrategyPO::getStatus, req.getStatus())
                .and(StringUtils.isNotBlank(req.getDeptKeyword()), wrapper -> wrapper
                        .like(MdDeliveryAppointmentStrategyPO::getDeptCode, req.getDeptKeyword())
                        .or()
                        .like(MdDeliveryAppointmentStrategyPO::getDeptName, req.getDeptKeyword())
                )
                .orderByDesc(MdDeliveryAppointmentStrategyPO::getCreateTime);

        IPage<MdDeliveryAppointmentStrategyDTO> page = repositoryService.page(new Page<>(req.getCurrent(), req.getPageSize()), condition)
                .convert(MdDeliveryAppointmentStrategyConvert.INSTANCE::po2dto);

        return PageResult.of(page.getTotal(), page.getRecords());
    }

    /**
     * 新增供应商预约策略
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String create(MdDeliveryAppointmentStrategyDTO dto) {
        if (dto == null) {
            return null;
        }

        if (dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B001);
        }

        // 补充配送部门名称
        StoreDetailResp deptInfo = baseDataSystemFeignClient.getSingleDeptInfo(dto.getDeptCode());
        if (deptInfo == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B002, new Object[]{dto.getDeptCode()});
        }
        dto.setDeptName(deptInfo.getName());

        // 一个部门只能存在一条配置
        Integer exist = repositoryService.lambdaQuery()
                .eq(MdDeliveryAppointmentStrategyPO::getDeptCode, dto.getDeptCode())
                .count();
        if (exist > 0) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B003, new Object[]{dto.getDeptCode()});
        }

        if (StringUtils.isBlank(dto.getBillNo())) {
            ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
            String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.DELIVERY_APPOINTMENT_STRATEGY_ORDER, dto.getDeptCode());
            dto.setBillNo(billNo);
        }

        MdDeliveryAppointmentStrategyPO po = MdDeliveryAppointmentStrategyConvert.INSTANCE.dto2po(dto);

        try {
            repositoryService.save(po);
        }catch (DuplicateKeyException e) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B004);
        }
        
        return po.getBillNo();
    }
    
    /**
     * 根据单号查询供应商预约策略详情
     */
    @Override
    public MdDeliveryAppointmentStrategyDTO getDetailByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }
        MdDeliveryAppointmentStrategyPO po = repositoryService.lambdaQuery()
                .eq(MdDeliveryAppointmentStrategyPO::getBillNo, billNo)
                .one();
        return MdDeliveryAppointmentStrategyConvert.INSTANCE.po2dto(po);
    }
    
    /**
     * 根据单号删除供应商预约策略
     */
    @Transactional
    @Override
    public boolean deleteByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return false;
        }

        List<MdDeliveryDockStrategyPO> dockCodeList = dockStrategyRepositoryService.lambdaQuery()
                .eq(MdDeliveryDockStrategyPO::getBillNo, billNo)
                .select(MdDeliveryDockStrategyPO::getDockCode)
                .list();

        repositoryService.lambdaUpdate().eq(MdDeliveryAppointmentStrategyPO::getBillNo, billNo).remove();
        closeDateRepositoryService.lambdaUpdate().eq(MdDeliveryAppointmentCloseDatePO::getBillNo, billNo).remove();
        dockStrategyRepositoryService.lambdaUpdate().eq(MdDeliveryDockStrategyPO::getBillNo, billNo).remove();
        if (CollectionUtils.isNotEmpty(dockCodeList)) {
            dockLimitRuleRepositoryService.lambdaUpdate().in(MdDeliveryDockLimitRulePO::getDockCode, dockCodeList).remove();
            dockGoodsCategoryRepositoryService.lambdaUpdate().in(MdDeliveryDockGoodsCategoryPO::getDockCode, dockCodeList).remove();
            dockDeptRepositoryService.lambdaUpdate().in(MdDeliveryDockDeptPO::getDockCode, dockCodeList).remove();
        }

        return true;
    }
    
    /**
     * 更新供应商预约策略状态
     */
    @Override
    public boolean updateStatus(String billNo, Integer status) {
        if (StringUtils.isBlank(billNo)) {
            return false;
        }
        
        if (StandardEnum.codeOf(MdAppointmentStrategyEnum.class, status) == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B005);
        }

        LambdaUpdateWrapper<MdDeliveryAppointmentStrategyPO> condition = Wrappers.lambdaUpdate(MdDeliveryAppointmentStrategyPO.class)
                .eq(MdDeliveryAppointmentStrategyPO::getBillNo, billNo)
                .set(MdDeliveryAppointmentStrategyPO::getStatus, status);
        OperatorInfoHandler.fillUpdateInfo(condition);
        return repositoryService.update(condition);
    }

    /**
     * 更新供应商预约策略
     */
    @Override
    @Transactional
    public boolean update(MdDeliveryAppointmentStrategyDTO dto) {
        if (dto == null || StringUtils.isBlank(dto.getBillNo())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B006);
        }

        if (dto.getEndTime().isBefore(dto.getStartTime())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B001);
        }

        MdDeliveryAppointmentStrategyPO existStrategy = repositoryService.lambdaQuery()
                .eq(MdDeliveryAppointmentStrategyPO::getBillNo, dto.getBillNo())
                .one();

        if (existStrategy == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B007, new Object[]{dto.getBillNo()});
        }

        // 更新配送部门名称
        StoreDetailResp deptInfo = baseDataSystemFeignClient.getSingleDeptInfo(existStrategy.getDeptCode());
        if (deptInfo == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B002, new Object[]{dto.getDeptCode()});
        }

        LambdaUpdateWrapper<MdDeliveryAppointmentStrategyPO> condition = Wrappers.lambdaUpdate(MdDeliveryAppointmentStrategyPO.class)
                .eq(MdDeliveryAppointmentStrategyPO::getBillNo, dto.getBillNo())
                .set(true, MdDeliveryAppointmentStrategyPO::getDeptName, deptInfo.getName())
                .set(true, MdDeliveryAppointmentStrategyPO::getStatus, dto.getStatus())
                .set(true, MdDeliveryAppointmentStrategyPO::getStartTime, dto.getStartTime())
                .set(true, MdDeliveryAppointmentStrategyPO::getEndTime, dto.getEndTime())
                .set(true, MdDeliveryAppointmentStrategyPO::getRemark, dto.getRemark());
        OperatorInfoHandler.fillUpdateInfo(condition);

        return repositoryService.update(condition);
    }

    /**
     * 批量创建/更新停止预约时间段数据
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchCreateAppointmentCloseDate(MdDeliveryAppointmentCloseDateBatchCreateReq batchCreateReq) {
        // 清理现有数据
        closeDateRepositoryService.lambdaUpdate()
                .eq(MdDeliveryAppointmentCloseDatePO::getBillNo, batchCreateReq.getBillNo())
                .remove();
        if (CollectionUtils.isEmpty(batchCreateReq.getCloseDateList())) {
            return true;
        }

        // 合并区间必要条件校验
        MdDeliveryAppointmentCloseDateDTO illegalItem = batchCreateReq.getCloseDateList().stream()
                .filter(dto -> dto.getEndTime().isBefore(dto.getStartTime()))
                .findFirst()
                .orElse(null);
        if (illegalItem != null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B008);
        }

        Integer exist = repositoryService.lambdaQuery()
                .eq(MdDeliveryAppointmentStrategyPO::getBillNo, batchCreateReq.getBillNo())
                .count();
        if (exist <= 0) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B007, new Object[]{batchCreateReq.getBillNo()});
        }

        // 合并时间区间
        batchCreateReq.getCloseDateList().sort(Comparator.comparing(MdDeliveryAppointmentCloseDateDTO::getStartTime));
        ArrayList<MdDeliveryAppointmentCloseDateDTO> dateTimeList = new ArrayList<>();

        batchCreateReq.getCloseDateList().forEach(dateTime -> {
            // 回写单号
            dateTime.setBillNo(batchCreateReq.getBillNo());

            if (CollectionUtils.isEmpty(dateTimeList)) {
                dateTimeList.add(dateTime);
                return;
            }
            MdDeliveryAppointmentCloseDateDTO last = dateTimeList.get(dateTimeList.size() - 1);
            if (!last.getStartTime().isAfter(dateTime.getStartTime()) && !last.getEndTime().isBefore(dateTime.getStartTime())) {
                last.setEndTime(last.getEndTime().isBefore(dateTime.getEndTime()) ? dateTime.getEndTime() : last.getEndTime());
            }else {
                dateTimeList.add(dateTime);
            }
        });

        List<MdDeliveryAppointmentCloseDatePO> closeDatePOList = MdDeliveryAppointmentCloseDateConvert.INSTANCE.dto2poList(dateTimeList);
        return closeDateRepositoryService.saveBatch(closeDatePOList);
    }

    /**
     * 停止预约时间数据查询
     */
    @Override
    public List<MdDeliveryAppointmentCloseDateDTO> getAppointmentCloseDateList(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return Collections.emptyList();
        }
        
        List<MdDeliveryAppointmentCloseDatePO> poList = closeDateRepositoryService.lambdaQuery()
                .eq(MdDeliveryAppointmentCloseDatePO::getBillNo, billNo)
                .orderByAsc(MdDeliveryAppointmentCloseDatePO::getStartTime)
                .list();
        
        return MdDeliveryAppointmentCloseDateConvert.INSTANCE.po2dtoList(poList);
    }

    /**
     * 停止预约时间数据查询
     */
    @Override
    public List<MdDeliveryAppointmentCloseDateDTO> getAppointmentCloseDateList(List<String> billNoList) {
        if (CollectionUtils.isEmpty(billNoList)) {
            return Collections.emptyList();
        }

        List<MdDeliveryAppointmentCloseDatePO> poList = closeDateRepositoryService.lambdaQuery()
                .in(MdDeliveryAppointmentCloseDatePO::getBillNo, billNoList)
                .list();

        return MdDeliveryAppointmentCloseDateConvert.INSTANCE.po2dtoList(poList);
    }

    /**
     * 查询停靠点策略列表
     */
    @Override
    public List<MdDeliveryDockStrategyDTO> queryDockStrategyList(MdDeliveryDockStrategyQueryReq req) {
        if (req == null) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<MdDeliveryDockStrategyPO> condition = Wrappers.lambdaQuery(MdDeliveryDockStrategyPO.class)
                .in(!CollectionUtils.isEmpty(req.getDeptCodeList()), MdDeliveryDockStrategyPO::getDeptCode, req.getDeptCodeList())
                .eq(req.getStatus() != null, MdDeliveryDockStrategyPO::getStatus, req.getStatus())
                .and(StringUtils.isNotBlank(req.getDockKeyword()), wrapper -> wrapper
                        .like(MdDeliveryDockStrategyPO::getDockCode, req.getDockKeyword())
                        .or()
                        .like(MdDeliveryDockStrategyPO::getDockName, req.getDockKeyword())
                )
                .orderByDesc(MdDeliveryDockStrategyPO::getCreateTime);
        
        List<MdDeliveryDockStrategyPO> dockStrategyList = dockStrategyRepositoryService.list(condition);
        if (CollectionUtils.isEmpty(dockStrategyList)) {
            return Collections.emptyList();
        }

        List<MdDeliveryDockStrategyDTO> dockStrategyDTOList = MdDeliveryDockStrategyConvert.INSTANCE.po2dtoList(dockStrategyList);

        List<String> dockCodeList = dockStrategyList.stream()
                .map(MdDeliveryDockStrategyPO::getDockCode)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isNotEmpty(dockCodeList)) {
            List<MdDeliveryDockDeptPO> dockDeptList = dockDeptRepositoryService.lambdaQuery()
                    .in(MdDeliveryDockDeptPO::getDockCode, dockCodeList)
                    .list();

            List<MdDeliveryDockGoodsCategoryPO> dockGoodsCategoryList = dockGoodsCategoryRepositoryService.lambdaQuery()
                    .in(MdDeliveryDockGoodsCategoryPO::getDockCode, dockCodeList)
                    .list();

            List<MdDeliveryDockLimitRulePO> dockLimitRuleList = dockLimitRuleRepositoryService.lambdaQuery()
                    .in(MdDeliveryDockLimitRulePO::getDockCode, dockCodeList)
                    .list();

            Map<String, List<MdDeliveryDockDeptPO>> dockDeptMap = dockDeptList.stream()
                    .collect(Collectors.groupingBy(MdDeliveryDockDeptPO::getDockCode));
            
            Map<String, List<MdDeliveryDockGoodsCategoryPO>> dockGoodsCategoryMap = dockGoodsCategoryList.stream()
                    .collect(Collectors.groupingBy(MdDeliveryDockGoodsCategoryPO::getDockCode));
            
            Map<String, List<MdDeliveryDockLimitRulePO>> dockLimitRuleMap = dockLimitRuleList.stream()
                    .collect(Collectors.groupingBy(MdDeliveryDockLimitRulePO::getDockCode));

            dockStrategyDTOList.forEach(dto -> {
                String dockCode = dto.getDockCode();
                dto.setDockDeptList(MdDeliveryDockStrategyConvert.INSTANCE.deptPo2dtoList(dockDeptMap.getOrDefault(dockCode, Collections.emptyList())));
                dto.setDockCategoryList(MdDeliveryDockStrategyConvert.INSTANCE.goodsCategoryPo2dtoList(dockGoodsCategoryMap.getOrDefault(dockCode, Collections.emptyList())));
                dto.setDockLimitRuleList(MdDeliveryDockStrategyConvert.INSTANCE.limitRulePo2dtoList(dockLimitRuleMap.getOrDefault(dockCode, Collections.emptyList())));
            });
        }
        
        return dockStrategyDTOList;
    }

    /**
     * 根据停靠点编码查询停靠点策略详情
     */
    @Override
    public MdDeliveryDockStrategyDTO getDockStrategyDetailByDockCode(String dockCode) {
        if (StringUtils.isBlank(dockCode)) {
            return null;
        }

        MdDeliveryDockStrategyPO dockStrategyPO = dockStrategyRepositoryService.lambdaQuery()
                .eq(MdDeliveryDockStrategyPO::getDockCode, dockCode)
                .one();
        
        if (dockStrategyPO == null) {
            return null;
        }

        MdDeliveryDockStrategyDTO dockStrategyDTO = MdDeliveryDockStrategyConvert.INSTANCE.po2dto(dockStrategyPO);
        
        List<MdDeliveryDockDeptPO> dockDeptList = dockDeptRepositoryService.lambdaQuery()
                .eq(MdDeliveryDockDeptPO::getDockCode, dockCode)
                .list();
        
        List<MdDeliveryDockGoodsCategoryPO> dockGoodsCategoryList = dockGoodsCategoryRepositoryService.lambdaQuery()
                .eq(MdDeliveryDockGoodsCategoryPO::getDockCode, dockCode)
                .list();
        
        List<MdDeliveryDockLimitRulePO> dockLimitRuleList = dockLimitRuleRepositoryService.lambdaQuery()
                .eq(MdDeliveryDockLimitRulePO::getDockCode, dockCode)
                .list();
        
        dockStrategyDTO.setDockDeptList(MdDeliveryDockStrategyConvert.INSTANCE.deptPo2dtoList(dockDeptList));
        dockStrategyDTO.setDockCategoryList(MdDeliveryDockStrategyConvert.INSTANCE.goodsCategoryPo2dtoList(dockGoodsCategoryList));
        dockStrategyDTO.setDockLimitRuleList(MdDeliveryDockStrategyConvert.INSTANCE.limitRulePo2dtoList(dockLimitRuleList));
        
        return dockStrategyDTO;
    }

    /**
     * 新增或更新停靠点策略
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateDockStrategy(MdDeliveryDockStrategyDTO dockStrategyDTO) {
        MdDeliveryAppointmentStrategyPO deliveryAppointmentStrategyPO = repositoryService.lambdaQuery()
                .eq(MdDeliveryAppointmentStrategyPO::getBillNo, dockStrategyDTO.getBillNo())
                .one();
        if (deliveryAppointmentStrategyPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B009, new Object[]{dockStrategyDTO.getBillNo()});
        }
        if (!Objects.equals(deliveryAppointmentStrategyPO.getDeptCode(), dockStrategyDTO.getDeptCode())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B010);
        }

        // 补充配送部门名称
        StoreDetailResp deptInfo = baseDataSystemFeignClient.getSingleDeptInfo(dockStrategyDTO.getDeptCode());
        if (deptInfo == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B002, new Object[]{dockStrategyDTO.getDeptCode()});
        }
        dockStrategyDTO.setDeptName(deptInfo.getName());

        String dockCode = dockStrategyDTO.getDockCode();
        MdDeliveryDockStrategyPO existDockStrategy = dockStrategyRepositoryService.lambdaQuery()
                .eq(MdDeliveryDockStrategyPO::getDockCode, dockCode)
                .one();

        if (dockStrategyDTO.getIsCreateOpt()) {
            if (existDockStrategy != null) {
                throw new ScBizException(MdErrorCodeEnum.SCMD006B011, new Object[]{dockStrategyDTO.getDockCode()});
            }

            dockStrategyDTO.setDeptCode(deliveryAppointmentStrategyPO.getDeptCode());
            try {
                dockStrategyRepositoryService.save(MdDeliveryDockStrategyConvert.INSTANCE.dto2po(dockStrategyDTO));
            }catch (DuplicateKeyException e) {
                throw new ScBizException(MdErrorCodeEnum.SCMD006B012, new Object[]{dockStrategyDTO.getDockCode()});
            }

        } else {
            if (existDockStrategy == null) {
                throw new ScBizException(MdErrorCodeEnum.SCMD006B013, new Object[]{dockStrategyDTO.getDockCode()});
            }
            if (!Objects.equals(existDockStrategy.getDeptCode(), dockStrategyDTO.getDeptCode())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD006B010);
            }

            LambdaUpdateWrapper<MdDeliveryDockStrategyPO> condition = Wrappers.lambdaUpdate(MdDeliveryDockStrategyPO.class)
                    .eq(MdDeliveryDockStrategyPO::getDockCode, dockCode)
                    .set(true, MdDeliveryDockStrategyPO::getDeptName, dockStrategyDTO.getDeptName())
                    .set(true, MdDeliveryDockStrategyPO::getDockName, dockStrategyDTO.getDockName())
                    .set(true, MdDeliveryDockStrategyPO::getDockType, dockStrategyDTO.getDockType())
                    .set(true, MdDeliveryDockStrategyPO::getStatus, dockStrategyDTO.getStatus())
                    .set(true, MdDeliveryDockStrategyPO::getConstraintRule, dockStrategyDTO.getConstraintRule())
                    .set(true, MdDeliveryDockStrategyPO::getRemark, dockStrategyDTO.getRemark());
            OperatorInfoHandler.fillUpdateInfo(condition);
            dockStrategyRepositoryService.update(condition);
        }

        deleteRelatedDockData(dockCode);
        saveRelatedDockDept(dockCode, dockStrategyDTO.getDockDeptList());
        saveRelatedDockGoodsCategory(dockCode, dockStrategyDTO.getDockCategoryList());
        saveRelatedDockLimitRule(dockCode, dockStrategyDTO.getDockLimitRuleList());
        
        return dockCode;
    }
    
    /**
     * 删除停靠点关联数据
     */
    private void deleteRelatedDockData(String dockCode) {
        // 删除部门关联数据
        dockDeptRepositoryService.lambdaUpdate()
                .eq(MdDeliveryDockDeptPO::getDockCode, dockCode)
                .remove();
        
        // 删除商品类别关联数据
        dockGoodsCategoryRepositoryService.lambdaUpdate()
                .eq(MdDeliveryDockGoodsCategoryPO::getDockCode, dockCode)
                .remove();
        
        // 删除限制规则关联数据
        dockLimitRuleRepositoryService.lambdaUpdate()
                .eq(MdDeliveryDockLimitRulePO::getDockCode, dockCode)
                .remove();
    }
    
    /**
     * 保存停靠点部门关联数据
     */
    private void saveRelatedDockDept(String dockCode, List<MdDeliveryDockDeptDTO> dockDeptList) {
        if (CollectionUtils.isEmpty(dockDeptList)) {
            return;
        }
        
        List<MdDeliveryDockDeptPO> deptPOList = dockDeptList.stream()
                .distinct()
                .map(dto -> {
                    MdDeliveryDockDeptPO po = MdDeliveryDockStrategyConvert.INSTANCE.deptDto2po(dto);
                    po.setDockCode(dockCode);
                    return po;
                })
                .collect(Collectors.toList());
        
        dockDeptRepositoryService.saveBatch(deptPOList);
    }
    
    /**
     * 保存停靠点商品类别关联数据
     */
    private void saveRelatedDockGoodsCategory(String dockCode, List<MdDeliveryDockGoodsCategoryDTO> dockGoodsCategoryList) {
        if (CollectionUtils.isEmpty(dockGoodsCategoryList)) {
            return;
        }

        StringBuffer errorTips = new StringBuffer();
        List<MdDeliveryDockGoodsCategoryPO> goodsCategoryPOList = dockGoodsCategoryList.stream()
                .distinct()
                .map(dto -> {
                    MdDeliveryDockGoodsCategoryPO po = MdDeliveryDockStrategyConvert.INSTANCE.goodsCategoryDto2po(dto);
                    if (MdDeliveryDockDefineTypeEnum.BY_CATEGORY.verifyByCode(po.getType())
                            && !po.validateCategory()) {
                        errorTips.append(String.format("行号 [%s] 非法的品类信息；", dto.getInsideId()));
                        return null;
                    }
                    if (MdDeliveryDockDefineTypeEnum.BY_PRODUCT.verifyByCode(po.getType())
                            && !po.validateSku()) {
                        errorTips.append(String.format("行号 [%s] 非法的商品信息；", dto.getInsideId()));
                        return null;
                    }
                    po.setDockCode(dockCode);
                    return po;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (errorTips.length() > 0) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B014, new Object[]{errorTips.toString()});
        }
        
        dockGoodsCategoryRepositoryService.saveBatch(goodsCategoryPOList);
    }
    
    /**
     * 保存停靠点限制规则关联数据
     */
    private void saveRelatedDockLimitRule(String dockCode, List<MdDeliveryDockLimitRuleDTO> dockLimitRuleList) {
        if (CollectionUtils.isEmpty(dockLimitRuleList)) {
            return;
        }

        MdDeliveryDockLimitRuleDTO illegalItem = dockLimitRuleList.stream()
                .filter(dto -> dto.getEndTime().isBefore(dto.getStartTime()))
                .findFirst()
                .orElse(null);
        if (illegalItem != null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD006B015);
        }

        dockLimitRuleList.sort(Comparator.comparing(MdDeliveryDockLimitRuleDTO::getStartTime));
        MdDeliveryDockLimitRuleDTO lastDate = null;

        for (MdDeliveryDockLimitRuleDTO dateTime : dockLimitRuleList) {
            if (lastDate == null) {
                lastDate = dateTime;
                continue;
            }
            if (!lastDate.getStartTime().isAfter(dateTime.getStartTime()) && lastDate.getEndTime().isAfter(dateTime.getStartTime())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD006B016);
            }else {
                lastDate = dateTime;
            }
        }
        
        List<MdDeliveryDockLimitRulePO> limitRulePOList = dockLimitRuleList.stream()
                .map(dto -> {
                    MdDeliveryDockLimitRulePO po = MdDeliveryDockStrategyConvert.INSTANCE.limitRuleDto2po(dto);
                    po.setDockCode(dockCode);
                    return po;
                })
                .collect(Collectors.toList());
        
        dockLimitRuleRepositoryService.saveBatch(limitRulePOList);
    }

    /**
     * 查询供应商预约策略信息
     */
    @Override
    public DeliveryAppointmentStrategyResultDTO listDeliveryAppointmentStrategy(ListDeliveryAppointmentStrategyDTO param){
        DeliveryAppointmentStrategyResultDTO deliveryAppointmentStrategyResultDTO = new DeliveryAppointmentStrategyResultDTO();
        //1.根据配送部门查询停靠点信息  编码 + 名称
        LambdaQueryWrapper<MdDeliveryDockStrategyPO> dockWrapper = new LambdaQueryWrapper<>();
        dockWrapper.in(MdDeliveryDockStrategyPO::getDeptCode,param.getDeliveryDeptCode());
        dockWrapper.eq(MdDeliveryDockStrategyPO::getStatus,1);

        List<MdDeliveryDockStrategyPO> mdDeliveryDockStrategyPOList = dockStrategyRepositoryService.getBaseMapper().selectList(dockWrapper);
        deliveryAppointmentStrategyResultDTO.setDockStrategyPOList(mdDeliveryDockStrategyPOList);
        param.setDockList(mdDeliveryDockStrategyPOList);

        if(CollectionUtils.isEmpty(mdDeliveryDockStrategyPOList)){
            return deliveryAppointmentStrategyResultDTO;
        }

        //2.根据配送部门列表查询维护部门的预约信息
        List<MdDeliveryDockDeptPO> mdDeliveryDockDeptPOList = dockDeptRepositoryService.getMdDeliveryDockDeptMapper().listDockDept(param);
        deliveryAppointmentStrategyResultDTO.setDockDeptPOList(mdDeliveryDockDeptPOList);

        //3.基于2查询商品、分类信息
        List<MdDeliveryDockGoodsCategoryPO> mdDeliveryDockGoodsCategoryPOS = dockGoodsCategoryRepositoryService.getMdDeliveryDockGoodsCategoryMapper().listDockGoods(param);
        deliveryAppointmentStrategyResultDTO.setDockGoodsCategoryPOList(mdDeliveryDockGoodsCategoryPOS);

        return deliveryAppointmentStrategyResultDTO;
    }

    @Override
    public DeliveryAppointmentStrategyResultDTO handleDock(Map<String, QueryBatchDeptListResp.Rows> upDeptMap,
                                                            Map<String, List<String>> deptGoodsMap, Set<String> categoryCodeSet, Set<String> skuCodeSet, Set<String> deliveryDeptCodeSet) {
        // 获取部门上级店组群
        QueryBatchDeptListReq req = QueryBatchDeptListReq.builder()
                .classCode(GroupDeptEnum.CONTROL_GROUP.getCode())
                .deptCodeList(new ArrayList<>(deptGoodsMap.keySet()))
                .build();
        List<QueryBatchDeptListResp.Rows> groupList = baseDataSystemFeignClient.queryUpDeptListBatch(req).getRows();
        upDeptMap = groupList.stream()
                .collect(Collectors.toMap(QueryBatchDeptListResp.Rows::getCode, obj -> obj));

        ListDeliveryAppointmentStrategyDTO dockParam = new ListDeliveryAppointmentStrategyDTO();

        dockParam.setDeliveryDeptCode(new ArrayList<>(deliveryDeptCodeSet));

        //部门编码列表
        List<ListDeliveryAppointmentStrategyDTO.DeptInfo> deptList = new ArrayList<>();
        dockParam.setDeptList(deptList);

        for (String s : deptGoodsMap.keySet()) {
            ListDeliveryAppointmentStrategyDTO.DeptInfo dept = new ListDeliveryAppointmentStrategyDTO.DeptInfo();
            dept.setDeptType(2);
            dept.setDeptCode(s);
            deptList.add(dept);
        }
        Set<String> deptGroupSet = new HashSet<>();
        Iterator<QueryBatchDeptListResp.Rows> iterator = upDeptMap.values().iterator();
        while (iterator.hasNext()){
            QueryBatchDeptListResp.Rows rows = iterator.next();
            for (QueryBatchDeptListResp.DeptGroup deptGroup : rows.getDeptGroupList()) {
                deptGroupSet.add(deptGroup.getCode());
            }
        }
        for (String s : deptGroupSet) {
            ListDeliveryAppointmentStrategyDTO.DeptInfo dept = new ListDeliveryAppointmentStrategyDTO.DeptInfo();
            dept.setDeptType(1);
            dept.setDeptCode(s);
            deptList.add(dept);
        }
        //商品编码列表
        dockParam.setSkuCodeList(new ArrayList<>(skuCodeSet));
        //品类编码列表
        dockParam.setCategoryCodeList(new ArrayList<>(categoryCodeSet));

        DeliveryAppointmentStrategyResultDTO deliveryAppointmentStrategyResultDTO = listDeliveryAppointmentStrategy(dockParam);

        return deliveryAppointmentStrategyResultDTO;
    }
}
