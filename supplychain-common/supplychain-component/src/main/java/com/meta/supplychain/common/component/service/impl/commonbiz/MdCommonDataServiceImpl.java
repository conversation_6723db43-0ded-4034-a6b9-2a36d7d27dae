package com.meta.supplychain.common.component.service.impl.commonbiz;

import com.meta.supplychain.common.component.service.intf.commonbiz.IMdCommonDataService;
import com.meta.supplychain.entity.dto.md.base.EnumDTO;
import com.meta.supplychain.enums.EnumContainer;
import com.meta.supplychain.enums.StandardEnum;
import com.meta.supplychain.validation.annotation.EnumMark;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 供应链主数据通用数据管理
 * <AUTHOR>
 */
@Service
public class MdCommonDataServiceImpl implements IMdCommonDataService {

    /**
     * 根据enumCode查询枚举值
     */
    @Override
    public List<EnumDTO<?>> queryByEnumCode(String enumCode) {
        return EnumContainer.getEnumsByCode(enumCode).stream()
                .filter(StandardEnum::export2Api)
                .map(this::enum2dto)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String,List<EnumDTO<?>>> queryByEnumCodes(List<String> enumCodes) {
        Map<String,List<EnumDTO<?>>> map = new HashMap<>();
        for(String enumCode : enumCodes){
            map.put(enumCode,EnumContainer.getEnumsByCode(enumCode).stream().map(this::enum2dto).collect(Collectors.toList()));
        }

        return map;
    }

    /**
     * 根据enumName查询枚举值
     */
    @Override
    public List<EnumDTO<?>> queryByEnumName(String enumName) {
        return EnumContainer.getEnumsByName(enumName).stream()
                .filter(StandardEnum::export2Api)
                .map(this::enum2dto)
                .collect(Collectors.toList());
    }

    private EnumDTO<?> enum2dto(StandardEnum<?> standardEnum) {
        EnumMark enumMark = standardEnum.getClass().getAnnotation(EnumMark.class);
        if (enumMark == null) {
            return EnumDTO.builder().code(standardEnum.getCode()).desc(standardEnum.getDesc()).build();
        }
        return EnumDTO.builder()
                .code(standardEnum.getCode())
                .desc(standardEnum.getDesc())
                .enumName(enumMark.name())
                .enumCode(enumMark.code())
                .build();
    }
}
