package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdExpenseItemDomainService;
import com.meta.supplychain.convert.md.MdExpenseItemCategoryConvert;
import com.meta.supplychain.convert.md.MdExpenseItemConvert;
import com.meta.supplychain.entity.dto.md.req.expense.*;
import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemCategoryResponseDTO;
import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemResponseDTO;
import com.meta.supplychain.entity.po.md.MdExpenseItemCategoryPO;
import com.meta.supplychain.entity.po.md.MdExpenseItemPO;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.enums.md.MdExpenseStatusEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.mybatis.OperatorInfoHandler;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdExpenseItemCategoryRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdExpenseItemRepositoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 费用项目 domain
 * <AUTHOR>
 */
@Service
public class MdExpenseItemDomainServiceImpl implements IMdExpenseItemDomainService {

    @Resource
    private IMdExpenseItemRepositoryService mdExpenseItemRepositoryService;

    @Resource
    private IMdExpenseItemCategoryRepositoryService mdExpenseItemCategoryRepositoryService;

    @Override
    public PageResult<MdExpenseItemResponseDTO> pageQuery(MdExpenseItemPageQueryReq request) {
        LambdaQueryWrapper<MdExpenseItemPO> condition = Wrappers.lambdaQuery(MdExpenseItemPO.class)
                .in(!CollectionUtils.isEmpty(request.getExpenseItemCategoryCode()), MdExpenseItemPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode())
                .eq(request.getExpenseType() != null, MdExpenseItemPO::getExpenseType, request.getExpenseType())
                .in(!CollectionUtils.isEmpty(request.getStatus()), MdExpenseItemPO::getStatus, request.getStatus())
                .and(StringUtils.isNotBlank(request.getExpenseItemCodeOrName()), wrapper ->
                        wrapper.like(MdExpenseItemPO::getExpenseItemCode, request.getExpenseItemCodeOrName())
                                .or()
                                .like(MdExpenseItemPO::getExpenseItemName, request.getExpenseItemCodeOrName())
                )
                .orderByDesc(MdExpenseItemPO::getCreateTime);

        IPage<MdExpenseItemResponseDTO> pageResult = mdExpenseItemRepositoryService
                .page(new Page<>(request.getCurrent(), request.getPageSize()), condition)
                .convert(MdExpenseItemConvert.INSTANCE::po2dto);

        // 补充费用项目分类名称
        Set<Integer> categoryCodeSet = pageResult.getRecords().stream().map(MdExpenseItemResponseDTO::getExpenseItemCategoryCode).collect(Collectors.toSet());
        if (!categoryCodeSet.isEmpty()) {
            LambdaQueryWrapper<MdExpenseItemCategoryPO> conditionForSearchCategory = Wrappers.lambdaQuery(MdExpenseItemCategoryPO.class)
                    .in(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, categoryCodeSet);
            Map<Integer, String> mappingCategoryCodeToName = mdExpenseItemCategoryRepositoryService.list(conditionForSearchCategory)
                    .stream()
                    .collect(Collectors.toMap(MdExpenseItemCategoryPO::getExpenseItemCategoryCode,
                            MdExpenseItemCategoryPO::getExpenseItemCategoryName,
                            (c1, c2) -> c1));

            pageResult.getRecords().forEach(item -> item.setExpenseItemCategoryName(mappingCategoryCodeToName.get(item.getExpenseItemCategoryCode())));
        }

        return new PageResult<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public MdExpenseItemResponseDTO getByCode(String code) {
        LambdaQueryWrapper<MdExpenseItemPO> condition = Wrappers.lambdaQuery(MdExpenseItemPO.class)
                .eq(MdExpenseItemPO::getExpenseItemCode, code);

        MdExpenseItemPO po = mdExpenseItemRepositoryService.getOne(condition);
        return MdExpenseItemConvert.INSTANCE.po2dto(po);
    }

    @Override
    public Boolean create(MdExpenseItemCreateReq request) {
        LambdaQueryWrapper<MdExpenseItemPO> condition = Wrappers.lambdaQuery(MdExpenseItemPO.class)
                .eq(MdExpenseItemPO::getExpenseItemCode, request.getExpenseItemCode());
        if (mdExpenseItemRepositoryService.count(condition) > 0) {
            throw new ScBizException(MdErrorCodeEnum.SCMD091B001, new String[]{request.getExpenseItemCode()});
        }

        LambdaUpdateWrapper<MdExpenseItemCategoryPO> categoryCondition = Wrappers.lambdaUpdate(MdExpenseItemCategoryPO.class)
                .eq(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode());
        if (mdExpenseItemCategoryRepositoryService.count(categoryCondition) <= 0) {
            throw new ScBizException(MdErrorCodeEnum.SCMD091B002, new Object[]{request.getExpenseItemCategoryCode()});
        }

        MdExpenseItemPO mdExpenseItemPO = MdExpenseItemPO.builder()
                .expenseItemCode(request.getExpenseItemCode())
                .expenseItemName(request.getExpenseItemName())
                .expenseItemCategoryCode(request.getExpenseItemCategoryCode())
                .chargeRate(request.getChargeRate())
                .taxRate(request.getTaxRate())
                .expenseType(request.getExpenseType())
                .billingItem(request.getBillingItem())
                .expenseDesc(request.getExpenseDesc())
                .status(MdExpenseStatusEnum.ENABLE.getCode())
                .build();

        return mdExpenseItemRepositoryService.save(mdExpenseItemPO);
    }

    @Override
    public Boolean update(MdExpenseItemUpdateReq request) {
        LambdaUpdateWrapper<MdExpenseItemPO> updateWrapper = Wrappers.lambdaUpdate(MdExpenseItemPO.class)
                .eq(MdExpenseItemPO::getExpenseItemCode, request.getExpenseItemCode());

        // 校验分类是否存在
        if (request.getExpenseItemCategoryCode() != null) {
            LambdaUpdateWrapper<MdExpenseItemCategoryPO> condition = Wrappers.lambdaUpdate(MdExpenseItemCategoryPO.class).eq(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode());
            if (mdExpenseItemCategoryRepositoryService.count(condition) <= 0) {
                throw new ScBizException(MdErrorCodeEnum.SCMD091B002, new Object[]{request.getExpenseItemCategoryCode()});
            }
        }

        updateWrapper.set(StringUtils.isNotBlank(request.getExpenseItemName()), MdExpenseItemPO::getExpenseItemName, request.getExpenseItemName())
                .set(request.getExpenseItemCategoryCode() != null, MdExpenseItemPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode())
                .set(request.getChargeRate() != null, MdExpenseItemPO::getChargeRate, request.getChargeRate())
                .set(request.getTaxRate() != null, MdExpenseItemPO::getTaxRate, request.getTaxRate())
                .set(request.getExpenseType() != null, MdExpenseItemPO::getExpenseType, request.getExpenseType())
                .set(true, MdExpenseItemPO::getBillingItem, request.getBillingItem())
                .set(true, MdExpenseItemPO::getExpenseDesc, request.getExpenseDesc());

        OperatorInfoHandler.fillUpdateInfo(updateWrapper);

        return mdExpenseItemRepositoryService.update(updateWrapper);
    }

    @Override
    public Boolean delete(String code) {
        LambdaUpdateWrapper<MdExpenseItemPO> condition = Wrappers.lambdaUpdate(MdExpenseItemPO.class)
                .eq(MdExpenseItemPO::getExpenseItemCode, code);
        return mdExpenseItemRepositoryService.remove(condition);
    }

    @Override
    public Boolean enable(String code) {
        MdExpenseItemPO mdExpenseItemPO = MdExpenseItemPO.builder().status(MdExpenseStatusEnum.ENABLE.getCode()).build();
        LambdaUpdateWrapper<MdExpenseItemPO> condition = Wrappers.lambdaUpdate(MdExpenseItemPO.class)
                .eq(MdExpenseItemPO::getExpenseItemCode, code);

        return mdExpenseItemRepositoryService.update(mdExpenseItemPO, condition);
    }

    @Override
    public Boolean disable(String code) {
        MdExpenseItemPO mdExpenseItemPO = MdExpenseItemPO.builder().status(MdExpenseStatusEnum.DISABLE.getCode()).build();
        LambdaUpdateWrapper<MdExpenseItemPO> condition = Wrappers.lambdaUpdate(MdExpenseItemPO.class)
                .eq(MdExpenseItemPO::getExpenseItemCode, code);

        return mdExpenseItemRepositoryService.update(mdExpenseItemPO, condition);
    }

    @Override
    public Map<Integer, Long> countByCategoryCode() {
        return mdExpenseItemRepositoryService.list().stream().collect(Collectors.groupingBy(MdExpenseItemPO::getExpenseItemCategoryCode, Collectors.counting()));
    }

    @Override
    public PageResult<MdExpenseItemCategoryResponseDTO> categoryPageQuery(MdExpenseItemCategoryPageQueryReq request) {
        LambdaQueryWrapper<MdExpenseItemCategoryPO> condition = Wrappers.lambdaQuery(MdExpenseItemCategoryPO.class)
                .eq(request.getExpenseItemCategoryCode() != null, MdExpenseItemCategoryPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode())
                .like(StringUtils.isNotBlank(request.getExpenseItemCategoryName()), MdExpenseItemCategoryPO::getExpenseItemCategoryName, request.getExpenseItemCategoryName())
                .orderByAsc(MdExpenseItemCategoryPO::getExpenseItemCategoryCode);

        IPage<MdExpenseItemCategoryResponseDTO> pageResult = mdExpenseItemCategoryRepositoryService
                .page(new Page<>(request.getCurrent(), request.getPageSize()), condition)
                .convert(MdExpenseItemCategoryConvert.INSTANCE::po2dto);

        return new PageResult<>(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public MdExpenseItemCategoryResponseDTO getCategoryByCode(Integer code) {
        LambdaQueryWrapper<MdExpenseItemCategoryPO> condition
                = Wrappers.lambdaQuery(MdExpenseItemCategoryPO.class).eq(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, code);

        MdExpenseItemCategoryPO po = mdExpenseItemCategoryRepositoryService.getOne(condition);
        return MdExpenseItemCategoryConvert.INSTANCE.po2dto(po);
    }

    @Override
    public Boolean createCategory(MdExpenseItemCategoryCreateReq request) {
        if (request.validateCategoryCode()) {
            LambdaUpdateWrapper<MdExpenseItemCategoryPO> condition
                    = Wrappers.lambdaUpdate(MdExpenseItemCategoryPO.class).eq(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode());
            if (mdExpenseItemCategoryRepositoryService.count(condition) > 0) {
                throw new ScBizException(MdErrorCodeEnum.SCMD091B003);
            }
        }else {
            int firstGapNumber = mdExpenseItemCategoryRepositoryService.getFirstGapNumber();
            if (firstGapNumber >= 9999) {
                throw new ScBizException(MdErrorCodeEnum.SCMD091B004);
            }

            request.setExpenseItemCategoryCode(firstGapNumber + 1);
        }

        MdExpenseItemCategoryPO mdExpenseItemCategoryPO = MdExpenseItemCategoryPO.builder()
                .expenseItemCategoryCode(request.getExpenseItemCategoryCode())
                .expenseItemCategoryName(request.getExpenseItemCategoryName())
                .build();
        mdExpenseItemCategoryPO.setTenantId(TenantContext.getLong());

        return mdExpenseItemCategoryRepositoryService.insertIfNotExists(mdExpenseItemCategoryPO) == 1;
    }

    @Override
    public Boolean updateCategory(MdExpenseItemCategoryUpdateReq request) {
        MdExpenseItemCategoryPO mdExpenseItemCategoryPO = MdExpenseItemCategoryPO.builder()
                .expenseItemCategoryName(request.getExpenseItemCategoryName())
                .build();

        LambdaUpdateWrapper<MdExpenseItemCategoryPO> condition
                = Wrappers.lambdaUpdate(MdExpenseItemCategoryPO.class).eq(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, request.getExpenseItemCategoryCode());

        return mdExpenseItemCategoryRepositoryService.update(mdExpenseItemCategoryPO, condition);
    }

    @Override
    public Boolean deleteCategory(Integer code) {
        Integer exists = mdExpenseItemRepositoryService.lambdaQuery()
                .eq(MdExpenseItemPO::getExpenseItemCategoryCode, code)
                .count();
        if (exists > 0) {
            throw new ScBizException(MdErrorCodeEnum.SCMD091B005);
        }

        LambdaUpdateWrapper<MdExpenseItemCategoryPO> condition
                = Wrappers.lambdaUpdate(MdExpenseItemCategoryPO.class).eq(MdExpenseItemCategoryPO::getExpenseItemCategoryCode, code);
        return mdExpenseItemCategoryRepositoryService.remove(condition);
    }
}
