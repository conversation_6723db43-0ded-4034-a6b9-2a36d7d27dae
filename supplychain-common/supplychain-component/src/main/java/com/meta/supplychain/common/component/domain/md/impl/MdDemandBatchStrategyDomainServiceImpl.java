package com.meta.supplychain.common.component.domain.md.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.common.uuid.IdWorker;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meta.supplychain.common.component.domain.md.intf.IMdDemandBatchStrategyDomainService;
import com.meta.supplychain.common.component.service.impl.commonbiz.XxlJobService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.convert.md.MdDemandBatchStrategyConvert;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.goods.req.GoodsCategoryQueryReq;
import com.meta.supplychain.entity.dto.goods.resp.GoodsCategoryQueryResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DemandBatchRecordGoodsDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GeneratePurchBatchDTO;
import com.meta.supplychain.entity.dto.md.demandbatchstraegy.*;
import com.meta.supplychain.entity.dto.md.req.demandbatchstraegy.*;
import com.meta.supplychain.entity.dto.md.resp.demandbatchstraegy.MdDemandBatchRecordDeptSkuResp;
import com.meta.supplychain.entity.dto.md.resp.demandbatchstraegy.MdDemandBatchRecordSkuResp;
import com.meta.supplychain.entity.dto.xxjob.XxlJobReq;
import com.meta.supplychain.entity.po.md.MdDemandBatchCateGoodsPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchRecordPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchStrategyPO;
import com.meta.supplychain.entity.po.md.MdDemandBatchTimeSegmentPO;
import com.meta.supplychain.enums.md.MdDemandBatchCateGoodsTypeEnum;
import com.meta.supplychain.enums.md.MdDemandBatchGenerateMethodEnum;
import com.meta.supplychain.enums.md.MdErrorCodeEnum;
import com.meta.supplychain.enums.md.MdSystemParamEnum;
import com.meta.supplychain.exceptions.ScBizException;
import com.meta.supplychain.infrastructure.mybatis.OperatorInfoHandler;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandBatchCateGoodsRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandBatchRecordRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandBatchStrategyRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdDemandBatchTimeSegmentRepositoryService;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.UserUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 需求批次策略
 * <AUTHOR>
 * @date 2025/03/30 01:56
 **/
@Service
public class MdDemandBatchStrategyDomainServiceImpl implements IMdDemandBatchStrategyDomainService {

    @Resource
    private IMdDemandBatchStrategyRepositoryService strategyRepositoryService;

    @Resource
    private IMdDemandBatchCateGoodsRepositoryService cateGoodsRepositoryService;

    @Resource
    private IMdDemandBatchTimeSegmentRepositoryService timeSegmentRepository;

    @Resource
    private IMdDemandBatchStrategyRepositoryService demandBatchStrategyRepositoryService;

    @Resource
    private IMdDemandBatchTimeSegmentRepositoryService timeSegmentRepositoryService;

    @Resource
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Resource
    private IMdDemandBatchRecordRepositoryService mdDemandBatchRecordRepositoryService;

    @Resource
    private UserUtil userUtil;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public static final String TIME_SEGMENT_INSIDE_ID_KEY = "time_segment_inside_id_key";

    public static final long TIME_SEGMENT_INSIDE_ID_BASE = LocalDateTime.of(2025, Month.JUNE, 1, 0, 0, 0).toInstant(ZoneOffset.of("+08:00")).getEpochSecond();

    @Autowired
    private ICommonGoodsService commonGoodsService;

    private XxlJobService getXxlJobService(){
        XxlJobService xxlJobService = SpringContextUtil.getApplicationContext().getBean(XxlJobService.class);
        return xxlJobService;
    }

    /**
     * 时段行号生成
     */
    @Override
    public Result<Integer> generateTimeSegmentInsideId() {
        long cache = Optional.ofNullable(stringRedisTemplate.opsForValue().get(TIME_SEGMENT_INSIDE_ID_KEY)).map(Long::parseLong).orElse(0L);
        long db = Optional.ofNullable(timeSegmentRepository.maxInsideId()).orElse(0L);

        // 兜底初始化
        if (cache == 0 || cache < db) {
            // 兜底使用当前时间
            // 行号字段为 unsigned int 取相对于 2025.6.1 的时间差
            long cur = LocalDateTime.now().toInstant(ZoneOffset.of("+08:00")).getEpochSecond() - TIME_SEGMENT_INSIDE_ID_BASE;
            // 只有 cache 和 系统时间 都异常时 才有可能会出现重复的行号
            stringRedisTemplate.opsForValue().set(TIME_SEGMENT_INSIDE_ID_KEY, String.valueOf(Math.max(db, cur)));
        }
        return Results.ofSuccess(stringRedisTemplate.opsForValue().increment(TIME_SEGMENT_INSIDE_ID_KEY).intValue());
    }

    /**
     * 分页查询需求批次策略
     *
     * @param request 查询请求参数
     * @return 分页查询结果
     */
    @Override
    public PageResult<MdDemandBatchStrategyDTO> pageQuery(QueryMdDemandBatchStrategyReq request) {
        // 用户分管权限
        OpInfo deptOpInfo = userUtil.getDeptOpInfoWithThrow();

        LambdaQueryWrapper<MdDemandBatchStrategyPO> queryWrapper = Wrappers.lambdaQuery(MdDemandBatchStrategyPO.class)
                .in(CollectionUtils.isNotEmpty(request.getDeptCodes()), MdDemandBatchStrategyPO::getDeptCode, request.getDeptCodes())
                .eq(StringUtils.isNotBlank(request.getWhCode()), MdDemandBatchStrategyPO::getWhCode, request.getWhCode())
                .eq(request.getGenerateMethod() != null, MdDemandBatchStrategyPO::getGenerateMethod, request.getGenerateMethod())
                // 分管条件
                .in(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdDemandBatchStrategyPO::getDeptCode, deptOpInfo.getManageDeptCodeList())
                .in(!CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), MdDemandBatchStrategyPO::getWhCode, deptOpInfo.getManageDeptCodeList())
                .apply(CollectionUtils.isEmpty(deptOpInfo.getManageDeptCodeList()), "1 = 2")
                .orderByDesc(MdDemandBatchStrategyPO::getCreateTime);

        IPage<MdDemandBatchStrategyDTO> pageResult = strategyRepositoryService
                .page(new Page<>(request.getCurrent(), request.getPageSize()), queryWrapper)
                .convert(MdDemandBatchStrategyConvert.INSTANCE::strategyPo2dto);

        return new PageResult<>(pageResult.getTotal(), pageResult.getRecords());
    }

    /**
     * 根据ID查询需求批次策略详情
     *
     * @param billNo 需求批次策略单据号
     * @return 需求批次策略详情，包含时间段和品类商品信息
     */
    @Override
    public MdDemandBatchStrategyDTO detail(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            return null;
        }
        MdDemandBatchStrategyPO strategyPO = strategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getBillNo, billNo)
                .one();
        if (strategyPO == null) {
            return null;
        }

        OpInfo deptOpInfo = userUtil.getDeptOpInfoWithThrow();
        if (!deptOpInfo.getManageDeptCodeList().contains(strategyPO.getDeptCode())
                || !deptOpInfo.getManageDeptCodeList().contains(strategyPO.getWhCode()) ) {
            return null;
        }

        List<MdDemandBatchTimeSegmentDTO> timeSegmentDTOList = queryTimeSegmentByStrategyNo(billNo);
        if (CollectionUtils.isEmpty(timeSegmentDTOList)) {
            return MdDemandBatchStrategyConvert.INSTANCE.strategyPo2dto(strategyPO);
        }

        Map<Long, List<MdDemandBatchCateGoodsDTO>> mappingTimeSegmentIdToCateGoodsList = queryCateGoodsByStrategyNo(billNo)
                        .stream()
                        .collect(Collectors.groupingBy(MdDemandBatchCateGoodsDTO::getBatchTimeSegmentId));

        timeSegmentDTOList.forEach(timeSegmentDTO -> timeSegmentDTO.setCateGoods(mappingTimeSegmentIdToCateGoodsList.get(timeSegmentDTO.getId())));

        MdDemandBatchStrategyDTO mdDemandBatchStrategyDTO = MdDemandBatchStrategyConvert.INSTANCE.strategyPo2dto(strategyPO);
        mdDemandBatchStrategyDTO.setTimeSegments(timeSegmentDTOList);

        return mdDemandBatchStrategyDTO;
    }

    /**
     * 删除需求批次策略
     * 同时删除关联的时间段和品类商品信息
     *
     * @param billNo 需求批次策略单号
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public Boolean delete(String billNo) {
        MdDemandBatchStrategyPO strategyPO = strategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getBillNo, billNo)
                .one();
        if (strategyPO == null) {
            return false;
        }

        OpInfo deptOpInfo = userUtil.getDeptOpInfoWithThrow();
        if (!deptOpInfo.getManageDeptCodeList().contains(strategyPO.getDeptCode())
                || !deptOpInfo.getManageDeptCodeList().contains(strategyPO.getWhCode()) ) {
            return false;
        }

        strategyRepositoryService.lambdaUpdate().eq(MdDemandBatchStrategyPO::getBillNo, billNo).remove();
        timeSegmentRepository.lambdaUpdate().eq(MdDemandBatchTimeSegmentPO::getDemandBatchStrategyNo, billNo).remove();
        cateGoodsRepositoryService.lambdaUpdate().eq(MdDemandBatchCateGoodsPO::getDemandBatchStrategyNo, billNo).remove();
        return true;
    }

    /**
     * 创建需求批次策略前的参数校验
     *
     * @param po 需求批次策略PO对象
     * @throws ScBizException 业务异常
     */
    private void createPreCheck(MdDemandBatchStrategyPO po) {
        if (!po.validateBillNo()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B001);
        }
        if (!po.validateDeptCode()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B002);
        }
        if (!po.validateDeptName()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B003);
        }
        if (!po.validateWhCode()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B004);
        }
        if (!po.validateWhName()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B005);
        }
        if (!po.validateGenerateMethod()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B006);
        }
        if (!po.validateDateInterval()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B007);
        }
        if (!po.validateInitialDate()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B008);
        }
        if (!po.validateDelayTime()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B009);
        }
        if (!po.validateCategoryLevel()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B010);
        }
        String cateCodeLen = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(MdSystemParamEnum.GOODS_CATE_CODE_LENGTH_CONFIG);
        if (!org.springframework.util.StringUtils.hasText(cateCodeLen)) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B011);
        }
        int maxLevel = cateCodeLen.split(",").length;
        if (maxLevel < po.getCategoryLevel()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B012, new Object[]{maxLevel});
        }
        OpInfo deptOpInfo = userUtil.getDeptOpInfoWithThrow();
        if (!deptOpInfo.getManageDeptCodeList().contains(po.getDeptCode())
                || !deptOpInfo.getManageDeptCodeList().contains(po.getWhCode()) ) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B013);
        }
    }

    /**
     * 创建需求批次策略
     * 同时创建关联的时间段和品类商品信息
     *
     * @param createRequest 创建请求参数
     * @return 创建成功的需求批次策略ID
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public Long create(CreateMdDemandBatchStrategyReq createRequest) {
        MdDemandBatchStrategyPO strategyPO = MdDemandBatchStrategyConvert.INSTANCE.strategyRequest2po(createRequest);

        // 生成需求批次单据号
        if (StringUtils.isEmpty(strategyPO.getBillNo())) {
            strategyPO.setBillNo(IdWorker.getIdStr());
        }

        createPreCheck(strategyPO);
        strategyRepositoryService.save(strategyPO);

        if (CollectionUtils.isEmpty(createRequest.getTimeSegments())) {
            return strategyPO.getId();
        }

        Map<MdDemandBatchTimeSegmentPO, List<CreateMdDemandBatchCateGoods>> mappingTimeSegmentPoToCateGoodsDTOList = new HashMap<>();

        List<MdDemandBatchTimeSegmentPO> timeSegmentPOList = createRequest.getTimeSegments().stream()
                .map(timeSegment -> {
                    MdDemandBatchTimeSegmentPO timeSegmentPO = MdDemandBatchStrategyConvert.INSTANCE.timeSegmentRequest2po(timeSegment);
                    timeSegmentPO.setId(IdWorker.getId());
                    timeSegmentPO.setDemandBatchStrategyNo(strategyPO.getBillNo());
                    timeSegmentPO.setDeptCode(strategyPO.getDeptCode());
                    timeSegmentPO.setDeptName(strategyPO.getDeptName());
                    timeSegmentPO.setWhCode(strategyPO.getWhCode());
                    timeSegmentPO.setWhName(strategyPO.getWhName());

                    mappingTimeSegmentPoToCateGoodsDTOList.put(timeSegmentPO, timeSegment.getCateGoods());
                    return timeSegmentPO;
                })
                .collect(Collectors.toList());
        
        createBatchTimeSegment(timeSegmentPOList);

        List<MdDemandBatchCateGoodsPO> mdDemandBatchCateGoodsPOList = mappingTimeSegmentPoToCateGoodsDTOList.entrySet().stream()
                .flatMap(e -> e.getValue().stream().map(cateGoods -> {
                    MdDemandBatchCateGoodsPO cateGoodsPO = MdDemandBatchStrategyConvert.INSTANCE.cateGoodsRequest2po(cateGoods);
                    cateGoodsPO.setDemandBatchStrategyNo(strategyPO.getBillNo());
                    cateGoodsPO.setBatchTimeSegmentId(e.getKey().getId());
                    cateGoodsPO.setDeptCode(strategyPO.getDeptCode());
                    cateGoodsPO.setDeptName(strategyPO.getDeptName());
                    cateGoodsPO.setWhCode(strategyPO.getWhCode());
                    cateGoodsPO.setWhName(strategyPO.getWhName());
                    return cateGoodsPO;
                })).collect(Collectors.toList());

        createBatchCateGoods(timeSegmentPOList, mdDemandBatchCateGoodsPOList);

        return strategyPO.getId();
    }

    @Transactional
    @Override
    public Boolean modify(CreateMdDemandBatchStrategyReq request) {
        if (StringUtils.isEmpty(request.getBillNo())) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B014);
        }
        MdDemandBatchStrategyPO strategyPO = strategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getBillNo, request.getBillNo())
                .one();
        if (strategyPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B015, new Object[]{request.getBillNo()});
        }

        OpInfo deptOpInfo = userUtil.getDeptOpInfoWithThrow();
        if (!deptOpInfo.getManageDeptCodeList().contains(strategyPO.getDeptCode())
                || !deptOpInfo.getManageDeptCodeList().contains(strategyPO.getWhCode()) ) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B013);
        }

        // 删除原数据
        strategyRepositoryService.purgeByStrategyNo(request.getBillNo());
        // 重新创建
        request.setBillNo(strategyPO.getBillNo());
        OperatorInfoHandler.useCreateUser(strategyPO);
        this.create(request);
        OperatorInfoHandler.useDefaultLoginUser();
        return true;
    }

    /**
     * 根据策略ID查询时间段列表
     *
     * @param strategyBillNo 需求批次策略单据号
     * @return 时间段列表
     */
    @Override
    public List<MdDemandBatchTimeSegmentDTO> queryTimeSegmentByStrategyNo(String strategyBillNo) {
        return timeSegmentRepository.lambdaQuery()
                .eq(MdDemandBatchTimeSegmentPO::getDemandBatchStrategyNo, strategyBillNo)
                .list()
                .stream()
                .map(MdDemandBatchStrategyConvert.INSTANCE::timeSegmentPo2dto)
                .collect(Collectors.toList());
    }

    /**
     * 创建时间段前的参数校验
     *
     * @param po 时间段PO对象
     * @throws ScBizException 业务异常
     */
    private void createPreCheck(MdDemandBatchTimeSegmentPO po) {
        if (!po.validateInsideId()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B016);
        }
        if (!po.validateDemandBatchTimeSegmentNo()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B017);
        }
        if (!po.validateStartTime() || !po.validateEndTime()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B018);
        }
        if (!po.validateDemandBatchName()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B019);
        }
        if (!po.validateDeliveryDays()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B020);
        }
        if (!po.validateAppendDelayDays()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B021);
        }
    }

    /**
     * 创建时间段
     *
     * @param po 时间段PO对象
     * @return 创建成功的时间段ID
     */
    @Override
    public Long createTimeSegment(MdDemandBatchTimeSegmentPO po) {
        if (po == null) {
            return -1L;
        }
        createPreCheck(po);

        MdDemandBatchStrategyPO strategyPO = demandBatchStrategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getId, po.getDemandBatchStrategyNo())
                .one();
        if (strategyPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B022, new Object[]{po.getDemandBatchStrategyNo()});
        }

        // 回写表头数据
        po.setWhCode(strategyPO.getWhCode());
        po.setWhName(strategyPO.getWhName());
        po.setDeptCode(strategyPO.getDeptCode());
        po.setDeptName(strategyPO.getDeptName());
        if (MdDemandBatchGenerateMethodEnum.BY_DATETIME_INTERVAL.verifyByCode(strategyPO.getGenerateMethod())) {
            timeSegmentRepository.save(po);
            return po.getId();
        }

        // 按日期的需求批次策略只允许一条时段信息
        if (MdDemandBatchGenerateMethodEnum.BY_DATE.verifyByCode(strategyPO.getGenerateMethod())) {
            Integer existCount = timeSegmentRepository.lambdaQuery()
                    .eq(MdDemandBatchTimeSegmentPO::getDemandBatchStrategyNo, po.getDemandBatchStrategyNo())
                    .count();
            if (existCount > 0) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B023);
            }
            if (!po.getStartTime().equals(po.getEndTime())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B024);
            }
            timeSegmentRepository.save(po);
            return po.getId();
        }
        throw new ScBizException(MdErrorCodeEnum.SCMD005B025);
    }

    /**
     * 批量创建时间段
     *
     * @param timeSegmentPOList 时间段PO对象列表
     * @return 是否创建成功
     */
    @Override
    public Boolean createBatchTimeSegment(List<MdDemandBatchTimeSegmentPO> timeSegmentPOList) {
        if (org.springframework.util.CollectionUtils.isEmpty(timeSegmentPOList)) {
            return true;
        }
        timeSegmentPOList.forEach(this::createPreCheck);

        if (timeSegmentPOList.stream().map(MdDemandBatchTimeSegmentPO::getDemandBatchStrategyNo).distinct().count() > 1) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B026);
        }

        MdDemandBatchStrategyPO strategyPO = demandBatchStrategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getBillNo, timeSegmentPOList.get(0).getDemandBatchStrategyNo())
                .one();
        if (strategyPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B022, new Object[]{timeSegmentPOList.get(0).getDemandBatchStrategyNo()});
        }

        // 回写表头数据
        timeSegmentPOList.forEach(po -> {
            po.setWhCode(strategyPO.getWhCode());
            po.setWhName(strategyPO.getWhName());
            po.setDeptCode(strategyPO.getDeptCode());
            po.setDeptName(strategyPO.getDeptName());
        });

        if (MdDemandBatchGenerateMethodEnum.BY_DATETIME_INTERVAL.verifyByCode(strategyPO.getGenerateMethod())) {
            return timeSegmentRepository.saveBatch(timeSegmentPOList);
        }

        // 按日期的需求批次策略只允许一条时段信息
        if (MdDemandBatchGenerateMethodEnum.BY_DATE.verifyByCode(strategyPO.getGenerateMethod())) {
            if (timeSegmentPOList.size() > 1) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B023);
            }
            if (!timeSegmentPOList.get(0).getStartTime().equals(timeSegmentPOList.get(0).getEndTime())) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B024);
            }
            Integer existCount = timeSegmentRepository.lambdaQuery()
                    .eq(MdDemandBatchTimeSegmentPO::getDemandBatchStrategyNo, timeSegmentPOList.get(0).getDemandBatchStrategyNo())
                    .count();
            if (existCount > 0) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B023);
            }
            return timeSegmentRepository.saveBatch(timeSegmentPOList);
        }

        throw new ScBizException(MdErrorCodeEnum.SCMD005B025);
    }

    /**
     * 根据时间段ID查询品类商品列表
     *
     * @param timeSegmentId 时间段ID
     * @return 品类商品列表
     */
    @Override
    public List<MdDemandBatchCateGoodsDTO> queryCateGoodsByTimeSegmentId(Long timeSegmentId) {
        return cateGoodsRepositoryService.lambdaQuery()
                .eq(MdDemandBatchCateGoodsPO::getBatchTimeSegmentId, timeSegmentId)
                .list()
                .stream()
                .map(MdDemandBatchStrategyConvert.INSTANCE::cateGoodsPo2dto)
                .collect(Collectors.toList());
    }

    /**
     * 根据策略单据号查询品类商品列表
     *
     * @param strategyBillNo 策略单据号
     * @return 品类商品列表
     */
    @Override
    public List<MdDemandBatchCateGoodsDTO> queryCateGoodsByStrategyNo(String strategyBillNo) {
        return cateGoodsRepositoryService.lambdaQuery()
                .eq(MdDemandBatchCateGoodsPO::getDemandBatchStrategyNo, strategyBillNo)
                .list()
                .stream()
                .map(MdDemandBatchStrategyConvert.INSTANCE::cateGoodsPo2dto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID删除品类商品
     *
     * @param id 品类商品ID列表
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteCateGoodsById(List<Long> id) {
        return cateGoodsRepositoryService.removeByIds(id);
    }

    /**
     * 根据时间段ID删除品类商品
     *
     * @param timeSegmentIdList 时间段ID列表
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteCateGoodsByTimeSegmentId(List<Long> timeSegmentIdList) {
        return cateGoodsRepositoryService.remove(Wrappers.lambdaQuery(MdDemandBatchCateGoodsPO.class).in(MdDemandBatchCateGoodsPO::getBatchTimeSegmentId, timeSegmentIdList));
    }

    /**
     * 创建品类商品前的参数校验
     *
     * @param po 品类商品PO对象
     * @throws ScBizException 业务异常
     */
    private void createPreCheck(MdDemandBatchCateGoodsPO po) {
        if (!po.validateInsideId()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B027);
        }
        if (!po.validateDemandBatchStrategyNo()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B028);
        }
        if (!po.validateBatchTimeSegmentId()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B029);
        }
        if (!po.validateType()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B030);
        }
        if (!po.validateCode()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B031);
        }
        if (!po.validateName()) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B032);
        }
    }

    /**
     * 创建品类商品
     *
     * @param po 品类商品PO对象
     * @return 创建成功的品类商品ID
     */
    @Override
    public Long createCateGoods(MdDemandBatchCateGoodsPO po) {
        if (po == null) {
            return -1L;
        }
        createPreCheck(po);

        MdDemandBatchStrategyPO strategyPO = strategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getBillNo, po.getDemandBatchStrategyNo())
                .one();
        if (strategyPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B022, new Object[]{po.getDemandBatchStrategyNo()});
        }

        MdDemandBatchTimeSegmentPO timeSegmentPO = timeSegmentRepositoryService.lambdaQuery()
                .eq(MdDemandBatchTimeSegmentPO::getDemandBatchStrategyNo, po.getDemandBatchStrategyNo())
                .eq(MdDemandBatchTimeSegmentPO::getId, po.getBatchTimeSegmentId())
                .one();
        if (timeSegmentPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B033, new Object[]{po.getDemandBatchStrategyNo(), po.getBatchTimeSegmentId()});
        }

        // 校验品类级别
        if (MdDemandBatchCateGoodsTypeEnum.CATEGORY.verifyByCode(po.getType())) {
            String cateCodeLen = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(MdSystemParamEnum.GOODS_CATE_CODE_LENGTH_CONFIG);
            if (!org.springframework.util.StringUtils.hasText(cateCodeLen)) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B011);
            }

            String[] cateCodeLenArray = cateCodeLen.split(",");
            if (cateCodeLenArray.length < strategyPO.getCategoryLevel()) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B012, new Object[]{cateCodeLenArray.length});
            }

            int len = 0;
            for (int i = 0; i < strategyPO.getCategoryLevel(); i++) {
                len += Integer.parseInt(cateCodeLenArray[i]);
            }

            if (po.getCode().length() != len) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B034, new Object[]{strategyPO.getCategoryLevel(), len});
            }
        }

        // 回写表头数据
        po.setDeptCode(strategyPO.getDeptCode());
        po.setDeptName(strategyPO.getDeptName());
        po.setWhCode(strategyPO.getWhCode());
        po.setWhName(strategyPO.getWhName());
        cateGoodsRepositoryService.save(po);
        return po.getId();
    }

    /**
     * 批量创建品类商品
     *
     * @param cateGoodsPOList 品类商品PO对象列表
     * @return 是否创建成功
     */
    @Override
    public Boolean createBatchCateGoods(List<MdDemandBatchTimeSegmentPO> timeSegmentPOList, List<MdDemandBatchCateGoodsPO> cateGoodsPOList) {
        if (org.springframework.util.CollectionUtils.isEmpty(cateGoodsPOList)) {
            return true;
        }
        if (cateGoodsPOList.stream().map(MdDemandBatchCateGoodsPO::getDemandBatchStrategyNo).distinct().count() > 1) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B035);
        }
        cateGoodsPOList.forEach(this::createPreCheck);

        MdDemandBatchStrategyPO strategyPO = strategyRepositoryService.lambdaQuery()
                .eq(MdDemandBatchStrategyPO::getBillNo, cateGoodsPOList.get(0).getDemandBatchStrategyNo())
                .one();
        if (strategyPO == null) {
            throw new ScBizException(MdErrorCodeEnum.SCMD005B022, new Object[]{cateGoodsPOList.get(0).getDemandBatchStrategyNo()});
        }

        // 品类编码校验
        boolean definedByCategory = cateGoodsPOList.stream()
                .map(MdDemandBatchCateGoodsPO::getType)
                .anyMatch(MdDemandBatchCateGoodsTypeEnum.CATEGORY::verifyByCode);

        // 最大编码级别
        int maxCategoryLevel = 0;
        // 目标编码长度
        int targetCategoryLen = 0;

        if (definedByCategory) {
            String cateCodeLen = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getValue(MdSystemParamEnum.GOODS_CATE_CODE_LENGTH_CONFIG);
            if (!org.springframework.util.StringUtils.hasText(cateCodeLen)) {
                throw new ScBizException(MdErrorCodeEnum.SCMD005B011);
            }
            String[] cateCodeLenArray = cateCodeLen.split(",");

            maxCategoryLevel = cateCodeLenArray.length;
            for (int i = 0; i < strategyPO.getCategoryLevel(); i++) {
                targetCategoryLen += Integer.parseInt(cateCodeLenArray[i]);
            }
        }

        for (MdDemandBatchCateGoodsPO po : cateGoodsPOList) {
            // 回写表头数据
            po.setWhCode(strategyPO.getWhCode());
            po.setWhName(strategyPO.getWhName());
            po.setDeptCode(strategyPO.getDeptCode());
            po.setDeptName(strategyPO.getDeptName());
            // 校验品类级别
            if (MdDemandBatchCateGoodsTypeEnum.CATEGORY.verifyByCode(po.getType())) {
                if (maxCategoryLevel < strategyPO.getCategoryLevel()) {
                    throw new ScBizException(MdErrorCodeEnum.SCMD005B012, new Object[]{maxCategoryLevel});
                }
                if (po.getCode().length() != targetCategoryLen) {
                    throw new ScBizException(MdErrorCodeEnum.SCMD005B034, new Object[]{strategyPO.getCategoryLevel(), targetCategoryLen});
                }
            }
        }

        // 配送仓 + 同一时段内的 商品/品类数据不能重复 （时段不考虑交叉）
        Map<Long, List<MdDemandBatchCateGoodsPO>> mappingTimeIdToCateGoodsList
                = cateGoodsPOList.stream().collect(Collectors.groupingBy(MdDemandBatchCateGoodsPO::getBatchTimeSegmentId));
        List<ExistGoodsInfoQueryDTO> existGoodsQueryList = timeSegmentPOList.stream()
                .map(timeSegmentPO -> {
                    if (CollectionUtils.isEmpty(mappingTimeIdToCateGoodsList.get(timeSegmentPO.getId()))) {
                        return null;
                    }

                    // 入参校验
                    List<String> duplicateGoodsList = mappingTimeIdToCateGoodsList.get(timeSegmentPO.getId()).stream()
                            .collect(Collectors.groupingBy(goodsPo -> String.format("%s-%s", goodsPo.getType(), goodsPo.getCode())))
                            .values().stream()
                            .filter(list -> list.size() > 1)
                            .map(list -> list.get(0).getCode())
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(duplicateGoodsList)) {
                        throw new ScBizException(MdErrorCodeEnum.SCMD005B036, new Object[]{
                                String.format("%s-%s", timeSegmentPO.getStartTime(), timeSegmentPO.getEndTime()),
                                String.join(",", duplicateGoodsList)});
                    }

                    return mappingTimeIdToCateGoodsList.get(timeSegmentPO.getId()).stream()
                            .map(goods -> ExistGoodsInfoQueryDTO.builder()
                                    .whCode(goods.getWhCode())
                                    .startTime(timeSegmentPO.getStartTime())
                                    .endTime(timeSegmentPO.getEndTime())
                                    .code(goods.getCode())
                                    .type(goods.getType())
                                    .build())
                            .collect(Collectors.toList());
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());


        if (!CollectionUtils.isEmpty(existGoodsQueryList)
                && (!CollectionUtils.isEmpty(existGoodsQueryList = cateGoodsRepositoryService.listExistGoodsInfoByTimeSegmentAndWhCode(existGoodsQueryList)))) {
            String errorMessage = existGoodsQueryList.stream()
                    .map(exist -> String.format("配送仓 %s 时段 %s-%s 下已存在商品/品类数据 %s", exist.getWhCode(), exist.getStartTime(), exist.getEndTime(), exist.getCode()))
                    .collect(Collectors.joining(";"));
            throw new ScBizException(MdErrorCodeEnum.SCMD005B037, new Object[]{errorMessage});
        }

        cateGoodsPOList.forEach(this::createPreCheck);
        return cateGoodsRepositoryService.saveBatch(cateGoodsPOList);
    }

    @Override
    public PageResult<MdDemandBatchRecordDTO> pageMdDemandBatchRecord(MdDemandBatchRecordReq request) {
        LambdaQueryWrapper<MdDemandBatchRecordPO> wrapper = new LambdaQueryWrapper<>();

        if(StringUtils.isNotEmpty(request.getStartAddEndTime())){
            wrapper.gt(MdDemandBatchRecordPO::getAddEndTime, request.getStartAddEndTime());
        }
        else if(StringUtils.isNotEmpty(request.getAddEndTime())){
            wrapper.le(MdDemandBatchRecordPO::getAddEndTime, request.getAddEndTime());
        }
        else{
            String curDate = DateUtil.ymdHms(new Date());
            wrapper.gt(MdDemandBatchRecordPO::getAddEndTime, curDate);
        }

        wrapper.eq(Objects.nonNull(request.getDeptCode()),MdDemandBatchRecordPO::getDeptCode,request.getDeptCode());
        if("purchBatchNo".equals(request.getOrderColumn())){
            if(ObjectUtils.equals(0,request.getOrderType())){
                wrapper.orderByDesc(MdDemandBatchRecordPO::getPurchBatchNo);
            }else{
                wrapper.orderByAsc(MdDemandBatchRecordPO::getPurchBatchNo);
            }

        }
        else{
            wrapper.orderByAsc(MdDemandBatchRecordPO::getWhCode,MdDemandBatchRecordPO::getDeliverDate,MdDemandBatchRecordPO::getPurchBatchNo);
        }


        IPage<MdDemandBatchRecordDTO> pageResult = mdDemandBatchRecordRepositoryService
                .page(new Page<>(request.getCurrent(), request.getPageSize()), wrapper)
                .convert(MdDemandBatchStrategyConvert.INSTANCE::strategyRecordPo2dto);
        return new PageResult<>(pageResult.getTotal(), pageResult.getRecords());
    }

    /**
     * 根据ID删除时间段
     * 同时删除关联的品类商品信息
     *
     * @param idList 时间段ID列表
     * @return 是否删除成功
     */
    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public Boolean deleteTimeSegmentById(List<Long> idList) {
        if (org.springframework.util.CollectionUtils.isEmpty(idList)) {
            return true;
        }
        timeSegmentRepository.removeByIds(idList);
        deleteCateGoodsByTimeSegmentId(idList);
        return true;
    }

    @Override
    public void generateProcurementBatch(GeneratePurchBatchDTO generatePurchBatchDTO){
        int intervalDays = generatePurchBatchDTO.getDeliverDays();
        Date date = generatePurchBatchDTO.getDate();
        List<String> shipperCodes = generatePurchBatchDTO.getDemandBatchRecordGoodsList().stream().map(DemandBatchRecordGoodsDTO::getDeptCode).distinct().collect(Collectors.toList());

        LambdaQueryWrapper<MdDemandBatchStrategyPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(MdDemandBatchStrategyPO::getDeptCode, shipperCodes);
        lambdaQueryWrapper.eq(MdDemandBatchStrategyPO::getGenerateMethod, 0);//按时间段
        List<MdDemandBatchStrategyPO> mdDemandBatchStrategyPOList = strategyRepositoryService.getBaseMapper().selectList(lambdaQueryWrapper);
        Map<String, List<MdDemandBatchStrategyPO>> devisionSetMap = mdDemandBatchStrategyPOList.stream().collect(Collectors.groupingBy(MdDemandBatchStrategyPO::getWhCode));

        Map<String, MdDemandBatchStrategyPO> mdDemandBatchStrategyPOMap = mdDemandBatchStrategyPOList.stream().collect(Collectors.toMap(MdDemandBatchStrategyPO::getBillNo, Function.identity()));

        Map<String, List<GoodsCategoryQueryResp>> goodsCategoryMap = getCategoryMap(generatePurchBatchDTO);

        Map<String, List<DemandBatchRecordGoodsDTO>> shipperCodeGroupMap = generatePurchBatchDTO.getDemandBatchRecordGoodsList().stream().collect(Collectors.groupingBy(DemandBatchRecordGoodsDTO::getDeptCode));

        Map<String, MdDemandBatchRecordPO> logMap = new HashMap<>();
        shipperCodeGroupMap.forEach((key, value) -> {
            Set<String> curCategory = new HashSet<>();
            for (DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO : value) {
                if(!goodsCategoryMap.containsKey(demandBatchRecordGoodsDTO.getCategoryCode())){
                    continue;
                }
                Set<String> categoryCodes = goodsCategoryMap.get(demandBatchRecordGoodsDTO.getCategoryCode()).stream()
                        .map(GoodsCategoryQueryResp::getCategoryCode).collect(Collectors.toSet());
                curCategory.addAll(categoryCodes);
            }

            Set<String> curSkuCode = new HashSet<>();
            for (DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO : value) {
                curSkuCode.add(demandBatchRecordGoodsDTO.getSkuCode());
            }

            List<MdDemandBatchCateGoodsPO> mdDemandBatchCateGoodsPOList = getMdDemandBatchCateGoodsPOList(key, curCategory,1);

            List<MdDemandBatchCateGoodsPO> mdDemandBatchCateGoods4SkuList = getMdDemandBatchCateGoodsPOList(key, curSkuCode,2);

            if (CollectionUtils.isEmpty(mdDemandBatchCateGoodsPOList) && CollectionUtils.isEmpty(mdDemandBatchCateGoods4SkuList)) {
                return;
            }
            Map<String, List<MdDemandBatchCateGoodsPO>> categorySetMap = mdDemandBatchCateGoodsPOList.stream().collect(Collectors.groupingBy(MdDemandBatchCateGoodsPO::getCode));
            Map<String, List<MdDemandBatchCateGoodsPO>> skuSetMap = mdDemandBatchCateGoods4SkuList.stream().collect(Collectors.groupingBy(MdDemandBatchCateGoodsPO::getCode));
            for (DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO : value) {
                // 1.查询批次分割方式 = 时段的批次分割设置信息
                if (!devisionSetMap.containsKey(demandBatchRecordGoodsDTO.getDeptCode())) {
                    continue;
                }

                //先判断商品
                Long serialNumber = null;
                MdDemandBatchCateGoodsPO mdDemandBatchCateGoods = null;

                if(skuSetMap.containsKey(demandBatchRecordGoodsDTO.getSkuCode())){
                    List<MdDemandBatchCateGoodsPO> mdDemandBatchCateGoodsPOS = skuSetMap.get(demandBatchRecordGoodsDTO.getSkuCode());
                    serialNumber = mdDemandBatchCateGoodsPOS.get(0).getBatchTimeSegmentId();//获取时间表的id
                    mdDemandBatchCateGoods = mdDemandBatchCateGoodsPOS.get(0);
                }
                else{
                    // 2. 取品类设置 按品类级别末级往上筛选 筛选后有多个时按序号从小到大筛选 取到采购批次时段设置
                    List<GoodsCategoryQueryResp> goodsCategoryQueryResp = goodsCategoryMap.get(demandBatchRecordGoodsDTO.getCategoryCode());
                    if (goodsCategoryQueryResp == null) {
                        continue;
                    }

                    //按优先级从最底层品类级别向上筛选（级别从大到小）
                    List<GoodsCategoryQueryResp> sortedList = goodsCategoryQueryResp.stream()
                            .sorted(Comparator.comparing(GoodsCategoryQueryResp::getLevel, Comparator.reverseOrder()))
                            .collect(Collectors.toList());


                    for (GoodsCategoryQueryResp categoryInfo : sortedList) {
                        if (categorySetMap.containsKey(categoryInfo.getCategoryCode())) {
                            List<MdDemandBatchCateGoodsPO> stPurchBatchCategorySets1 = categorySetMap.get(categoryInfo.getCategoryCode());
                            List<MdDemandBatchCateGoodsPO> serialNumbers = stPurchBatchCategorySets1.stream()
                                    .sorted(Comparator.comparing(MdDemandBatchCateGoodsPO::getId))
                                    .collect(Collectors.toList());
                            serialNumber = serialNumbers.get(0).getBatchTimeSegmentId();//获取时间表的id
                            mdDemandBatchCateGoods = serialNumbers.get(0);
                            break;
                        }
                    }
                }

                if (serialNumber == null || null == mdDemandBatchCateGoods) { //没有则不限制
                    continue;
                }

                LocalTime auditTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalTime();
                Calendar instance = Calendar.getInstance();
                instance.setTime(date);

                MdDemandBatchTimeSegmentPO mdDemandBatchTimeSegmentPO  = queryByWhCodeAndId(serialNumber,demandBatchRecordGoodsDTO.getDeptCode());
                if (mdDemandBatchTimeSegmentPO == null) {
                    continue;
                }

                LocalTime startTime = LocalTime.parse(mdDemandBatchTimeSegmentPO.getStartTime().replace(":",""), DateTimeFormatter.ofPattern("HHmm")); // 开始时间
                LocalTime endTime = LocalTime.parse(mdDemandBatchTimeSegmentPO.getEndTime().replace(":",""), DateTimeFormatter.ofPattern("HHmm")); // 结束时间时间
                // 3.生成批次号 ps: 生成批次号时开始时间日期都为第一天的日期
                Logs.info("查询时间设置：{}", JSON.toJSONString(mdDemandBatchTimeSegmentPO));

                int result = isWithinTimeRange(auditTime, startTime, endTime); //校验是否在时间内
                // 采购批次号 = 第一天日期 + 采购批次时段序号 + 表中起始时间
                // 起始时间 = 第一天日期 + 表中起始时间
                // 结束时间 = 第二天日期 + 表中结束时间
                // 追加截止时间 = 结束时间 加 采购批次分割设置表-批次延迟时间
                //送货日期 = 第二天日期+送货期
                Date time1 = null;
                Date time2 = null;
                if (result > 10) {  //
                    instance.add(Calendar.DAY_OF_YEAR, 1);
                    time1 = instance.getTime();
                    time2 = instance.getTime();
                } else if (result >= 0) {
                    time1 = instance.getTime();
                    instance.add(Calendar.DAY_OF_YEAR, result);
                    time2 = instance.getTime();
                } else {
                    time2 = instance.getTime();
                    instance.add(Calendar.DAY_OF_YEAR, result);
                    time1 = instance.getTime();
                }

                String ymdTime1 = DateUtil.yyyyMMdd(time1);
                String ymdTime2 = DateUtil.yyyyMMdd(time2);
                String procurementBatch = ymdTime1 + String.format("%02d", mdDemandBatchTimeSegmentPO.getInsideId()) + mdDemandBatchTimeSegmentPO.getStartTime().replace(":",""); //批次号

                String start = DateUtil.yyyyMMddSlash(time1) + " " + mdDemandBatchTimeSegmentPO.getStartTime() + ":00"; //开始时间
                String end = DateUtil.yyyyMMddSlash(time2) + " " + mdDemandBatchTimeSegmentPO.getEndTime() + ":00"; //开始时间
//                String start = ymdTime1 + " " + mdDemandBatchTimeSegmentPO.getStartTime().replace(":","") + "00"; //开始时间
//                String end = ymdTime2 + " " + mdDemandBatchTimeSegmentPO.getEndTime().replace(":","") + "59"; //结束时间

                String addEndTime = mdDemandBatchTimeSegmentPO.getAppendDelayDays().replace(":","");

                LocalTime addEndTimeLocal = LocalTime.parse(addEndTime, DateTimeFormatter.ofPattern("HHmm"));
                String format = "yyyy-MM-dd HH:mm:ss";
                Date endDate = DateUtil.str2Date(end, format);
                Calendar endCal = Calendar.getInstance();
                endCal.setTime(endDate);
                endCal.add(Calendar.HOUR_OF_DAY, addEndTimeLocal.getHour());
                endCal.add(Calendar.MINUTE, addEndTimeLocal.getMinute());
                endCal.set(Calendar.SECOND, 59);
                String deadLine = DateUtil.ymdHms(endCal.getTime());//第二天加上延迟时间 追加截止时间
                Calendar calendar = Calendar.getInstance();
                Calendar directCalendar = Calendar.getInstance();
                calendar.setTime(time2);
                directCalendar.setTime(time2);

                //intervalDays 页面填写日期和创建日期的时间差
                Integer deliveryTime = mdDemandBatchTimeSegmentPO.getDeliveryDays() == null ? intervalDays : mdDemandBatchTimeSegmentPO.getDeliveryDays();

                calendar.add(Calendar.DAY_OF_MONTH, deliveryTime);
                directCalendar.add(Calendar.DAY_OF_MONTH, deliveryTime);
                String sendTime = DateUtil.yyyyMMddSlash(calendar.getTime()); //送货日期
                String directSendTime = DateUtil.yyyyMMddSlash(directCalendar.getTime()); //直流送货日期
                MdDemandBatchRecordPO mdDemandBatchRecordPO = new MdDemandBatchRecordPO();
                mdDemandBatchRecordPO.setPurchBatchNo(procurementBatch);
                mdDemandBatchRecordPO.setDeptCode(mdDemandBatchCateGoods.getDeptCode());
                mdDemandBatchRecordPO.setDeptName(mdDemandBatchCateGoods.getDeptName());
                mdDemandBatchRecordPO.setWhCode(mdDemandBatchCateGoods.getWhCode());
                mdDemandBatchRecordPO.setWhName(mdDemandBatchCateGoods.getWhName());
                mdDemandBatchRecordPO.setSynDate(LocalDateTime.now());
                mdDemandBatchRecordPO.setPurchBatchName(mdDemandBatchTimeSegmentPO.getDemandBatchName());
                mdDemandBatchRecordPO.setBeginTime(DateUtil.date2LocalDateTime(start));
                mdDemandBatchRecordPO.setEndTime(DateUtil.date2LocalDateTime(end));//格式yyyyMMdd HHmmss
                mdDemandBatchRecordPO.setAddEndTime(DateUtil.date2LocalDateTime(deadLine));
                mdDemandBatchRecordPO.setDeliverDate(DateUtil.date2LocalDateTime(sendTime + " 23:59:59"));
                mdDemandBatchRecordPO.setDirectDeliverDate(DateUtil.date2LocalDateTime(directSendTime + " 23:59:59"));
                mdDemandBatchRecordPO.setCode(mdDemandBatchCateGoods.getCode());
                mdDemandBatchRecordPO.setType(mdDemandBatchCateGoods.getType());

                mdDemandBatchRecordPO.setDemandBatchCateGoodsId(mdDemandBatchCateGoods.getId());
                mdDemandBatchRecordPO.setDemandBatchTimeSegmentId(serialNumber);
                mdDemandBatchRecordPO.setBillNo(mdDemandBatchCateGoods.getDemandBatchStrategyNo());

                MdDemandBatchStrategyPO mdDemandBatchStrategyPO = mdDemandBatchStrategyPOMap.get(mdDemandBatchCateGoods.getDemandBatchStrategyNo());
                if(null != mdDemandBatchStrategyPO){
                    mdDemandBatchRecordPO.setDelayTime(mdDemandBatchStrategyPO.getDelayTime());
                }
                else{
                    mdDemandBatchRecordPO.setDelayTime("");
                }

                logMap.put(mdDemandBatchRecordPO.getExistsKey(), mdDemandBatchRecordPO);

                demandBatchRecordGoodsDTO.setPurchBatchNo(mdDemandBatchRecordPO.getPurchBatchNo());
                demandBatchRecordGoodsDTO.setPurchBatchName(mdDemandBatchRecordPO.getPurchBatchName());
            }
        });
        if (!logMap.isEmpty()) {
            List<MdDemandBatchRecordPO> mdDemandBatchRecordPOList = Lists.newArrayList(logMap.values());

            List<MdDemandBatchRecordPO> mdDemandBatchRecordPOS = mdDemandBatchRecordRepositoryService.getMdDemandBatchRecordMapper().listExistsBatchRecord(mdDemandBatchRecordPOList);

            //存在的不用生成任务
            Map<String, MdDemandBatchRecordPO> demandBatchRecordPOMap = mdDemandBatchRecordPOS.stream().collect(Collectors.toMap(MdDemandBatchRecordPO::getGroupKey, Function.identity(),(value1,value2)->value2));
            for (MdDemandBatchRecordPO mdDemandBatchRecordPO : mdDemandBatchRecordPOList) {
                if(!demandBatchRecordPOMap.containsKey(mdDemandBatchRecordPO.getGroupKey())){
                    //创建定时任务
                    try{
                        XxlJobReq xxlJob = new XxlJobReq();
                        xxlJob.setJobDesc("需求批次:" + mdDemandBatchRecordPO.getPurchBatchNo() + ",配送部门:" + mdDemandBatchRecordPO.getDeptCode());
                        xxlJob.setAuthor("system");
                        JSONObject json = new JSONObject();
                        json.put("tenantId", TenantContext.get());
                        json.put("purchBatchNo",mdDemandBatchRecordPO.getPurchBatchNo());
                        json.put("deptCode",mdDemandBatchRecordPO.getDeptCode());

                        xxlJob.setExecutorParam(json.toJSONString());//执行器，任务参数

                        xxlJob.setStartTime(mdDemandBatchRecordPO.getEndTime());//计划时间
                        xxlJob.setMin(DateUtil.getMinuteByLocalTime(mdDemandBatchRecordPO.getDelayTime() + ":00"));//计划时间前 min分钟后执行
                        xxlJob.setExecutorHandler("execDemandJob");//JobHandler
                        xxlJob.setIsStart(true);
                        Integer jobId = getXxlJobService().addJob(xxlJob);
                        mdDemandBatchRecordPO.setJobId(jobId + "");
                        demandBatchRecordPOMap.put(mdDemandBatchRecordPO.getGroupKey(),mdDemandBatchRecordPO);
                    }catch (BizException e){
                        Logs.error("MdDemandBatchStrategyDomainServiceImpl.generateProcurementBatch.BizException:",e);
                        e.printStackTrace();
                    }catch (Exception e){
                        Logs.error("MdDemandBatchStrategyDomainServiceImpl.generateProcurementBatch.Exception:",e);
                        e.printStackTrace();
                    }
                }
                else{
                    mdDemandBatchRecordPO.setJobId(demandBatchRecordPOMap.get(mdDemandBatchRecordPO.getGroupKey()).getJobId());
                }
            }

            mdDemandBatchRecordRepositoryService.getMdDemandBatchRecordMapper().insertIgnoreBatch(mdDemandBatchRecordPOList);
        }
    }

    @Override
    public List<MdDemandBatchRecordDeptSkuResp> listMdDemandBatchRecordDeptSku(MdDemandBatchRecordByDeptSkuReq params) {
        //先根据到店日期 与 配送部门 查询列表
        Map<String, String> collect = params.getGoodsList().stream().collect(Collectors.toMap(MdDemandBatchRecordByDeptSkuReq.GoodsQuery::getDeptCode, MdDemandBatchRecordByDeptSkuReq.GoodsQuery::getDeptCode));
        List<String> deptCodeList = new ArrayList<>(collect.keySet());
        LambdaQueryWrapper<MdDemandBatchRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MdDemandBatchRecordPO::getDeliverDate,params.getArrivalDate() + " 23:59:59");
        lambdaQueryWrapper.in(MdDemandBatchRecordPO::getDeptCode,deptCodeList);

        List<MdDemandBatchRecordPO> mdDemandBatchRecordPOS = mdDemandBatchRecordRepositoryService.getMdDemandBatchRecordMapper().selectList(lambdaQueryWrapper);
        Map<String,MdDemandBatchRecordPO> demandBatchRecordMap = new HashMap<>();
        for(MdDemandBatchRecordPO mdDemandBatchRecordPO : mdDemandBatchRecordPOS){
            String key = mdDemandBatchRecordPO.getType().intValue() + "_" + mdDemandBatchRecordPO.getCode() + "_" + mdDemandBatchRecordPO.getDeptCode();
            demandBatchRecordMap.put(key,mdDemandBatchRecordPO);
        }

        List<MdDemandBatchRecordDeptSkuResp> list = new ArrayList<>();
        for (MdDemandBatchRecordByDeptSkuReq.GoodsQuery goodsQuery : params.getGoodsList()) {
            MdDemandBatchRecordDeptSkuResp demandBatchRecordDeptSkuDTO = new MdDemandBatchRecordDeptSkuResp();
            demandBatchRecordDeptSkuDTO.setSkuCode(goodsQuery.getSkuCode());
            demandBatchRecordDeptSkuDTO.setDeptCode(goodsQuery.getDeptCode());
            List<MdDemandBatchRecordDTO> mdDemandBatchRecordList = new ArrayList<>();

            demandBatchRecordDeptSkuDTO.setMdDemandBatchRecordList(mdDemandBatchRecordList);

            //类型，1品类,2商品
            int type = 2;

            String key = type + "_" + goodsQuery.getSkuCode() + "_" + goodsQuery.getDeptCode();

            if(demandBatchRecordMap.containsKey(key)){
                MdDemandBatchRecordDTO copy = CglibCopier.copy(demandBatchRecordMap.get(key), MdDemandBatchRecordDTO.class);
                mdDemandBatchRecordList.add(copy);
            }

            type = 1;
            for (String s : goodsQuery.getCategoryCodeList()) {
                key = type + "_" + s + "_" + goodsQuery.getDeptCode();

                if(demandBatchRecordMap.containsKey(key)){
                    MdDemandBatchRecordDTO copy = CglibCopier.copy(demandBatchRecordMap.get(key), MdDemandBatchRecordDTO.class);
                    mdDemandBatchRecordList.add(copy);
                }
            }

            list.add(demandBatchRecordDeptSkuDTO);
        }

        return list;
    }

    @Override
    public List<MdDemandBatchRecordSkuResp> listMdDemandBatchRecordSku(MdDemandBatchRecordBySkuReq params) {
        //先根据到店日期 与 配送部门 查询列表
        LambdaQueryWrapper<MdDemandBatchRecordPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MdDemandBatchRecordPO::getDeliverDate,params.getArrivalDate() + " 23:59:59");

        List<MdDemandBatchRecordPO> mdDemandBatchRecordPOS = mdDemandBatchRecordRepositoryService.getMdDemandBatchRecordMapper().selectList(lambdaQueryWrapper);
        Map<String,List<MdDemandBatchRecordPO>> demandBatchRecordMap = new HashMap<>();
        for(MdDemandBatchRecordPO mdDemandBatchRecordPO : mdDemandBatchRecordPOS){
            String key = mdDemandBatchRecordPO.getType().intValue() + "_" + mdDemandBatchRecordPO.getCode();
            if(StringUtils.isNotEmpty(params.getDeptCode())){
                key = key + "_" + mdDemandBatchRecordPO.getDeptCode();
            }

            if(demandBatchRecordMap.containsKey(key)){
                demandBatchRecordMap.get(key).add(mdDemandBatchRecordPO);
            }
            else{
                List<MdDemandBatchRecordPO> list = new ArrayList<>();
                list.add(mdDemandBatchRecordPO);
                demandBatchRecordMap.put(key,list);
            }

        }

        List<MdDemandBatchRecordSkuResp> list = new ArrayList<>();
        for (MdDemandBatchRecordBySkuReq.DemandBatchRecordGoodsQuery goodsQuery : params.getGoodsList()) {
            MdDemandBatchRecordSkuResp demandBatchRecordSkuDTO = new MdDemandBatchRecordSkuResp();
            demandBatchRecordSkuDTO.setSkuCode(goodsQuery.getSkuCode());
            List<MdDemandBatchRecordDTO> mdDemandBatchRecordList = new ArrayList<>();

            demandBatchRecordSkuDTO.setMdDemandBatchRecordList(mdDemandBatchRecordList);

            //类型，1品类,2商品
            int type = 2;

            String key = type + "_" + goodsQuery.getSkuCode();

            if(StringUtils.isNotEmpty(params.getDeptCode())){
                key = key + "_" + params.getDeptCode();
            }

            if(demandBatchRecordMap.containsKey(key)){
                List<MdDemandBatchRecordDTO> copy = CglibCopier.copy(demandBatchRecordMap.get(key), MdDemandBatchRecordDTO.class);
                mdDemandBatchRecordList.addAll(copy);
            }

            type = 1;
            for (String s : goodsQuery.getCategoryCodeList()) {
                key = type + "_" + s;

                if(StringUtils.isNotEmpty(params.getDeptCode())){
                    key = key + "_" + params.getDeptCode();
                }

                if(demandBatchRecordMap.containsKey(key)){
                    List<MdDemandBatchRecordDTO> copy = CglibCopier.copy(demandBatchRecordMap.get(key), MdDemandBatchRecordDTO.class);
                    mdDemandBatchRecordList.addAll(copy);
                }
            }

            list.add(demandBatchRecordSkuDTO);
        }

        return list;
    }

    public MdDemandBatchTimeSegmentPO queryByWhCodeAndId(Long id, String deptCode) {
        LambdaQueryWrapper<MdDemandBatchTimeSegmentPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MdDemandBatchTimeSegmentPO::getDeptCode, deptCode);
        lambdaQueryWrapper.eq(MdDemandBatchTimeSegmentPO::getId, id);
        return timeSegmentRepositoryService.getBaseMapper().selectOne(lambdaQueryWrapper);
    }

    private List<MdDemandBatchCateGoodsPO> getMdDemandBatchCateGoodsPOList(String whCode, Set<String> curCategory,Integer type) {
        LambdaQueryWrapper<MdDemandBatchCateGoodsPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MdDemandBatchCateGoodsPO::getWhCode, whCode);
        lambdaQueryWrapper.eq(MdDemandBatchCateGoodsPO::getType, type);
        lambdaQueryWrapper.in(MdDemandBatchCateGoodsPO::getCode, Lists.newArrayList(curCategory));

        List<MdDemandBatchCateGoodsPO> mdDemandBatchCateGoodsPOList = cateGoodsRepositoryService.getBaseMapper().selectList(lambdaQueryWrapper);

        return mdDemandBatchCateGoodsPOList;
    }

    private Map<String, List<GoodsCategoryQueryResp>> getCategoryMap(GeneratePurchBatchDTO generatePurchBatchDTO) {
        List<String> categoryList = generatePurchBatchDTO.getDemandBatchRecordGoodsList().stream().map(DemandBatchRecordGoodsDTO::getCategoryCode).distinct().collect(Collectors.toList());

        GoodsCategoryQueryReq categoryQueryReq = GoodsCategoryQueryReq.builder().categoryCodes(categoryList).build();
        List<GoodsCategoryQueryResp> goodsCategory = commonGoodsService.getGoodsCategory(categoryQueryReq);
        Map<String, List<GoodsCategoryQueryResp>> categoryQueryRespMap = new HashMap<>();
        for (GoodsCategoryQueryResp e : goodsCategory) {
            List<GoodsCategoryQueryResp> respList = new ArrayList<>();
            respList.add(GoodsCategoryQueryResp.builder()
                    .categoryCode(e.getCategoryCode())
                    .categoryName(e.getCategoryName())
                    .level(e.getLevel())
                    .build());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(e.getParentList())) {
                for (GoodsCategoryQueryResp parent : e.getParentList()) {
                    respList.add(GoodsCategoryQueryResp.builder()
                            .categoryCode(parent.getCategoryCode())
                            .categoryName(parent.getCategoryName())
                            .level(e.getLevel())
                            .build());
                }
            }
            categoryQueryRespMap.put(e.getCategoryCode(), respList);
        }

        return categoryQueryRespMap;
    }

    private int isWithinTimeRange(LocalTime currentTime, LocalTime startTime, LocalTime endTime) {
        // 开始时间<结束时间 说明是第一天 取审核时间当天
        if (startTime.isBefore(endTime)) { //开始时间小于结束时间 说明不跨天
            if (!currentTime.isAfter(endTime)) { // 当前时间<= 结束时间 取当天
                return 0;
            } else {    // 审核时间 > 结束时间 取第二天
                return 100;
            }
        } else { //开始时间大于结束时间 说明跨天
            if (!currentTime.isAfter(endTime)) { //审核时间<=结束时间  说明是第二天 审核时间需减一天
                return -1;
            } else { //审核时间 > 结束时间  说明是第一天 取审核时间当天
                return 1;
            }
        }
    }
}
