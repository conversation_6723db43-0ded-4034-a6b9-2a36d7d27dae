package com.meta.supplychain.entity.dto.pms.resp.purch;

import com.meta.supplychain.entity.dto.common.resp.BillAdjustLogResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "采购订单详情含预约/履行明细")
public class PurchaseBillWithDetailAllResp {
    @Schema(description = "单据信息")
    PurchaseBillResp billInfo;

    @Schema(description = "采购订单明细")
    List<PurchaseBillDetailResp> detailList;

    @Schema(description = "预约明细")
    List<AppointmentDetailResp> appointmentList;

    @Schema(description = "履行明细")
    List<AcceptDetailResp> acceptList;

    @Schema(description = "调整明细")
    List<BillAdjustLogResp> adjustList;

    @Schema(description = "直流部门明细")
    List<DirectDeptDetailResp> directDeptList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppointmentDetailResp {

        @Schema(description = "预约单号")
        private String billNo;

        @Schema(description = "预约单行号")
        private Long insideId;

        @Schema(description = "预约单创建人工号")
        private String createCode;

        @Schema(description = "预约单创建人名称")
        private String createName;

        @Schema(description = "预约单创建时间")
        private LocalDateTime createTime;

        @Schema(description = "预约单提交时间")
        private LocalDateTime submitTime;

        @Schema(description = "预约到达时间")
        private LocalDateTime planArrivalTime;

        @Schema(description = "预约完成时间")
        private LocalDateTime planCompleteTime;

        @Schema(description = "预约数量")
        private BigDecimal appointmentQty;

        @Schema(description = "采购订单号")
        private String purchBillNo;

        @Schema(description = "采购订单行号")
        private Long purchInsideId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AcceptDetailResp{

        @Schema(description = "验收退单号")
        private String billNo;

        @Schema(description = "验收退单行号")
        private Long insideId;

        @Schema(description = "商品类型 0.普通商品 1.附赠商品")
        private Integer skuType;

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "商品名称")
        private String skuName;

        @Schema(description = "条码")
        private String barcode;

        @Schema(description = "验收退单创建人工号")
        private String createCode;

        @Schema(description = "验收退单创建人名称")
        private String createName;

        @Schema(description = "验收退单创建时间")
        private LocalDateTime createTime;

        @Schema(description = "验收退单审核人工号")
        private String auditManCode;

        @Schema(description = "验收退单审核人名称")
        private String auditManName;

        @Schema(description = "验收退单审核时间")
        private LocalDateTime auditTime;

        @Schema(description = "验收退数量")
        private BigDecimal acceptQty;

        @Schema(description = "验收退金额")
        private BigDecimal purchTaxMoney;

        @Schema(description = "采购订单号")
        private String purchBillNo;

        @Schema(description = "采购订单行号")
        private Long purchInsideId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DirectDeptDetailResp{
        @Schema(description = "仓库编码")
        private String whCode;

        @Schema(description = "仓库名称")
        private String whName;

        @Schema(description = "配送单据号")
        private String billNo;

        @Schema(description = "入货部门编码")
        private String inDeptCode;

        @Schema(description = "入货部门名称")
        private String inDeptName;

        @Schema(description = " 配送单据类型 1.仓库配送、2.仓间调拨")
        private Integer billType;

        @Schema(description = "配送订单单内序号")
        private Long insideId;

        @Schema(description = "商品类型 0.普通商品 1.附赠商品")
        private Integer skuType;

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "商品名称")
        private String skuName;

        @Schema(description = "条码")
        private String barcode;

        @Schema(description = "商品货号")
        private String goodsNo;

        @Schema(description = "品类编码")
        private String categoryCode;

        @Schema(description = "品类名称")
        private String categoryName;

        @Schema(description = "品牌编码")
        private String brandCode;

        @Schema(description = "品牌名称")
        private String brandName;

        @Schema(description = "计量单位;商品计量单位")
        private BigDecimal unit;

        @Schema(description = "单位")
        private String basicUnit;

        @Schema(description = "整件单位")
        private String packageUnit;

        @Schema(description = "单位比率，包装率")
        private BigDecimal unitRate;

        @Schema(description = "计量属性;0普通|2称重")
        private Integer uomAttr;

        @Schema(description = "商品规格")
        private String skuModel;

        @Schema(description = "进项税率")
        private BigDecimal inputTaxRate;

        @Schema(description = "销项税率")
        private BigDecimal outputTaxRate;

        @Schema(description = "是否直流[0 非直流 1 直流 2 全部]")
        private Integer directSign;

        @Schema(description = "停靠点")
        private String dockCode;

        @Schema(description = "直流采购订单单号")
        private String directPurchBillNo;

        @Schema(description = "直流采购订单行号")
        private Long directPurchInsideId;

        @Schema(description = "部门商品配送价")
        private BigDecimal deptDistPrice;

        @Schema(description = "档案配送价")
        private BigDecimal skuDistPrice;

        @Schema(description = "配送部门成本价")
        private BigDecimal deptCostPrice;

        @Schema(description = "最后进价")
        private BigDecimal lastPurchPrice;

        @Schema(description = "部门商品档案进价")
        private BigDecimal deptSkuPurchPrice;

        @Schema(description = "档案进价")
        private BigDecimal skuPurchPrice;

        @Schema(description = "配送单价")
        private BigDecimal deliveryPrice;

        @Schema(description = "配送数量")
        private BigDecimal deliveryQty;

        @Schema(description = "箱数")
        private BigDecimal wholeQty;

        @Schema(description = "零头数量")
        private BigDecimal oddQty;

        @Schema(description = "配送金额")
        private BigDecimal deliveryTaxMoney;

        @Schema(description = "配送税金")
        private BigDecimal deliveryTax;

        @Schema(description = "包装比率")
        private BigDecimal packageRate;

        @Schema(description = "订货单位比率")
        private BigDecimal orderUnitRate;

        //打印用
        /***配送金额**/
        private String deliveryTaxMoneyStr;
        /***配送税金**/
        private String deliveryTaxStr;
        /***配送单价**/
        private String deliveryPriceStr;
        private String deptDistPriceStr;
        private String skuDistPriceStr;
        private String deptCostPriceStr;
        private String lastPurchPriceStr;
        private String deptSkuPurchPriceStr;
        private String skuPurchPriceStr;
    }
}
