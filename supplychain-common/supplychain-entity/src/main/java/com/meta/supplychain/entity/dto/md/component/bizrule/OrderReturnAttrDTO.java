package com.meta.supplychain.entity.dto.md.component.bizrule;

import lombok.Data;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/19 14:14
 **/
@Data
public class OrderReturnAttrDTO {



    /**
     * 属性编码
     */
    private String attributeCode;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 是否系统 0否 1是
     */
    private Integer systemMode;

    /**
     * 默认有效期
     */
    private Integer defaultValidityPeriod;

    /**
     * 默认送货期
     */
    private Integer defaultDeliveryTime;

    /**
     * 原因是否必选
     */
    private Integer reasonMandatory;

    /**
     * 附件是否必选
     */
    private Integer attachmentMandatory;

    /**
     * 订货包装控制
     */
    private Integer orderPackagingControl;

    /**
     * 送货期是否可修改
     */
    private Integer deliveryModify;

    /**
     * 退配拆单方式 枚举：0 不拆单、1 按配送第一供应商拆单；2 仅直流商品按直流供应商拆单
     */
    private Integer refundSplitType;


}
