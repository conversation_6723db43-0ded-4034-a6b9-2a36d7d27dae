package com.meta.supplychain.entity.dto.md.component.bizrule;

import lombok.Data;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/04/21 23:12
 **/
@Data
public class DemandProcessStrategyAutoQueryDTO {
    /**
     * 订货部门编码
     */
    private String deptCode;

    /**
     * 配送部门编码
     */
    private String dcCode;

    /**
     * 单据类别（1订货、2退货）
     */
    private Integer billType;

    /**
     * 经营状态编码
     */
    private String workStateCode;

    /**
     * 流转途径编码
     */
    private String circulationModeCode;

    /**
     * 订退货属性
     */
    private String orderAttributeCode;


    /**
     * 出货途径 （1采购、2配送）
     */
    private Integer shippingWay;

    /**
     * 是否直流
     */
    private Integer isDirect;
}
