package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "过账单相关枚举",code = "PmsAccountEnum")
public enum PmsAccountEnum implements VerifiableEnum<Integer> {
//   billType 过账类型 0-代配验收，1-代配调拨，2-代配退补
//   billDirection 单据方向 0-采购验收 1-采购验收冲红 2-采购退货 3-采购退货冲红 4-门店调拨，5-门店调拨冲红，6-退补，7-退补冲红
//   redeployType 调拨类型 0-拨出部门和过账配送中心核算单位一致 1-拨入部门和过账配送中心核算单位一致 2-拨出拨入部门都和过账配送中心核算单位不一致

    /**过账类型**/
    ACCOUNT_BILL_TYPE_YS("billType", 0,"代配验收"),
    ACCOUNT_BILL_TYPE_DB("billType",1, "代配调拨"),
    ACCOUNT_BILL_TYPE_TB("billType", 2,"代配退补"),

    /**过账单据方向**/
    ACCOUNT_BILL_DIRECT_PA("billDirection", 0,"采购验收"),
    ACCOUNT_BILL_DIRECT_PAR("billDirection",1, "采购验收冲红"),
    ACCOUNT_BILL_DIRECT_PR("billDirection", 2,"采购退货"),
    ACCOUNT_BILL_DIRECT_PRR("billDirection", 3,"采购退货冲红"),
    ACCOUNT_BILL_DIRECT_TO("billDirection", 4,"门店调拨"),
    ACCOUNT_BILL_DIRECT_TOR("billDirection", 5,"门店调拨冲红"),
    ACCOUNT_BILL_DIRECT_TB("billDirection", 6,"退补"),
    ACCOUNT_BILL_DIRECT_TBR("billDirection", 7,"退补冲红"),

    /**过账调拨类型**/
    ACCOUNT_REDEPLOY_TYPE_OUT("redeployType", 0,"拨出部门和过账配送中心核算单位一致"),
    ACCOUNT_REDEPLOY_TYPE_IN("redeployType",1, "拨入部门和过账配送中心核算单位一致"),
    ACCOUNT_REDEPLOY_TYPE_IOD("redeployType", 2,"拨出拨入部门都和过账配送中心核算单位不一致"),
    ;

    /**枚举类型*/
    private String enumType;

    /**枚举值*/
    private Integer code;

    /**枚举值描述*/
    private String desc;

}
