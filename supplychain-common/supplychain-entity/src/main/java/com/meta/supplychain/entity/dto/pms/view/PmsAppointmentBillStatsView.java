package com.meta.supplychain.entity.dto.pms.view;

import com.alibaba.ageiport.processor.core.annotation.ViewField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预约单统计信息导出视图
 * <AUTHOR>
 */
@Data
public class PmsAppointmentBillStatsView {

    @ViewField(headerName = "预约单号")
    private String billNo;

    @ViewField(headerName = "部门编码")
    private String deptCode;

    @ViewField(headerName = "部门名称")
    private String deptName;

    @ViewField(headerName = "供应商编码")
    private String supplierCode;

    @ViewField(headerName = "供应商名称")
    private String supplierName;

    @ViewField(headerName = "单据来源")
    private String opSource;

    @ViewField(headerName = "订单截止开始时间")
    private String startValidityTime;

    @ViewField(headerName = "订单截止结束时间")
    private String endValidityTime;

    @ViewField(headerName = "订单送货开始时间")
    private String startDeliverTime;

    @ViewField(headerName = "订单送货结束时间")
    private String endDeliverTime;

    @ViewField(headerName = "预约类别")
    private String billDirection;

    @ViewField(headerName = "是否直流")
    private String directSign;

    @ViewField(headerName = "预约方式")
    private String appointmentMode;

    @ViewField(headerName = "默认预约数量")
    private String defaultQtySign;

    @ViewField(headerName = "停靠点编码")
    private String dockCode;

    @ViewField(headerName = "停靠点名称")
    private String dockName;

    @ViewField(headerName = "承运方式")
    private String transportMode;

    @ViewField(headerName = "承运人")
    private String transportMan;

    @ViewField(headerName = "承运联系人")
    private String transportContacts;

    @ViewField(headerName = "承运联系手机")
    private String transportMobile;

    @ViewField(headerName = "车辆类型")
    private String carType;

    @ViewField(headerName = "计划到达时间")
    private String planArrivalTime;

    @ViewField(headerName = "计划停留时长(分钟)")
    private Integer planStayMinute;

    @ViewField(headerName = "承运备注")
    private String transportRemark;

    @ViewField(headerName = "预约备注")
    private String appointmentRemark;

    @ViewField(headerName = "状态")
    private String status;

    @ViewField(headerName = "提交人")
    private String submitManName;

    @ViewField(headerName = "提交时间")
    private String submitTime;

    @ViewField(headerName = "作废人")
    private String cancelManName;

    @ViewField(headerName = "作废时间")
    private String cancelTime;

    @ViewField(headerName = "创建人")
    private String createName;

    @ViewField(headerName = "创建时间")
    private String createTime;

    @ViewField(headerName = "修改人")
    private String updateName;

    @ViewField(headerName = "修改时间")
    private String updateTime;

    @ViewField(headerName = "关联采购订单数量")
    private Integer purchBillCount;

    @ViewField(headerName = "商品SKU数量")
    private Integer skuCount;

    @ViewField(headerName = "预约数量总计")
    private BigDecimal appointmentQtyCount;

    @ViewField(headerName = "预约整件数量总计")
    private BigDecimal appointmentWholeQtyCount;

    @ViewField(headerName = "预约零头数量总计")
    private BigDecimal appointmentOddQtyCount;
} 