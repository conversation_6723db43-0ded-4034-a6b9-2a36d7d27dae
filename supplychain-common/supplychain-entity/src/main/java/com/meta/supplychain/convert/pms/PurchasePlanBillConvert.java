package com.meta.supplychain.convert.pms;

import com.meta.supplychain.entity.dto.pms.req.purch.PurchasePlanBillDetailReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchasePlanBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPlan4PurcheseResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchasePlanBillResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PmsPurchasePlanDetailResp;
import com.meta.supplychain.entity.dto.pms.view.PmsPurchasePlanBillView;
import com.meta.supplychain.entity.dto.pms.view.PmsPurchasePlanDetailView;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.enums.goods.SkuTypeEnum;
import com.meta.supplychain.enums.pms.PmsPurchPlanStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

@Mapper
public interface PurchasePlanBillConvert {
    PurchasePlanBillConvert INSTANCE = Mappers.getMapper(PurchasePlanBillConvert.class);

    PmsPurchasePlanBillPO convertReqToPo(PurchasePlanBillReq req);

    @Mapping(source = "status", target = "status")
    @Mapping(source = "status", target = "statusDesc", qualifiedByName = "statusToDesc")
    PmsPurchasePlanBillResp convertPoToResp(PmsPurchasePlanBillPO po);

    @Mapping(source = "totalTaxMoney", target = "totalTaxMoney", qualifiedByName = "doubleToStr")
    @Mapping(source = "totalTax", target = "totalTax", qualifiedByName = "doubleToStr")
    PmsPurchasePlanBillView convertRespToView(PmsPurchasePlanBillResp resp);

    PmsPlan4PurcheseResp convertRespToResp(PmsPurchasePlanBillResp resp);

    PmsPurchasePlanDetailView convertDetailPoToResp(PmsPurchasePlanDetailResp resp);

    PmsPurchasePlanDetailPO convertDetailReqToPo(PurchasePlanBillDetailReq po);

    @Mapping(source = "skuType", target = "skuType")
    @Mapping(source = "skuType", target = "skuTypeDesc", qualifiedByName = "skuTypeToDesc")
    PmsPurchasePlanDetailResp convertDetailPoToResp(PmsPurchasePlanDetailPO po);

    @Named("statusToDesc")
    default String statusToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        PmsPurchPlanStatusEnum enumValue = PmsPurchPlanStatusEnum.getInstance(code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }

    @Named("skuTypeToDesc")
    default String skuTypeToDesc(Integer code) {
        if (code == null) {
            return "";
        }
        SkuTypeEnum enumValue = SkuTypeEnum.getInstance(code);
        return enumValue != null ? enumValue.getDesc() : code.toString();
    }

    @Named("doubleToStr")
    default String doubleToStr(BigDecimal num) {
        if (num == null) {
            return "";
        }
        return num.stripTrailingZeros().toPlainString();
    }

}
