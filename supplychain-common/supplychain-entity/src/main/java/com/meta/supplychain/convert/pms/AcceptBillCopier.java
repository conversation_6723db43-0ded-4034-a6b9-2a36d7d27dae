package com.meta.supplychain.convert.pms;

import com.meta.supplychain.entity.dto.pms.req.accept.AcceptBillDTO;
import com.meta.supplychain.entity.dto.pms.req.accept.AcceptBillDetailDTO;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillView;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptDetailResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptDetailView;
import com.meta.supplychain.entity.po.pms.PmsAcceptBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsAcceptBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> cat
 * @date 2024/3/22 9:58
 */
@Mapper
public interface AcceptBillCopier {

    /**
     * 单例对象
     */
    AcceptBillCopier INSTANCE = Mappers.getMapper(AcceptBillCopier.class);

    PmsAcceptBillPO convertStAcceptBill(AcceptBillDTO acceptBillDTO);

    @Mappings({
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "submitTime", target = "submitTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "confirmTime", target = "confirmTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "suppUpdateTime", target = "suppUpdateTime"),
            @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "auditTime", target = "auditTime"),
            @Mapping(source = "wmsBillNo",target = "wmsBillNo"),
            @Mapping(source = "deptOperateMode",target = "deptOperateMode")
    })
    AcceptBillResp convertToResp(PmsAcceptBillPO acceptanceReq);

    List<AcceptBillResp> convert2RespList(List<PmsAcceptBillPO> acceptanceReq);

    PmsAcceptBillPO convertUpdate2Accept(AcceptBillDTO req);

    PmsAcceptBillDetailPO convertBillDetailDtoToPo(AcceptBillDetailDTO req);
}
