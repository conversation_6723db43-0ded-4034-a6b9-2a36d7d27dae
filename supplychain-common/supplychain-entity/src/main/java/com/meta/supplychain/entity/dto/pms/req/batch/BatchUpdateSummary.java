package com.meta.supplychain.entity.dto.pms.req.batch;

import lombok.*;

import java.math.BigDecimal;

/**
 * 批次
 * <AUTHOR> cat
 * @date 2024/8/2 14:19
 */
@Getter
@Setter
@Builder
public class BatchUpdateSummary {

    /**
     * 商品行号
     */
    private Long insideId;

    BatchInfo batchInfo;
    /**
     * 单价
     */
    private BigDecimal costPrice;
    /**
     * 金额
     */
    private BigDecimal totalMoney;
    /**
     * 税金
     */
    private BigDecimal totalTax;


    @Builder
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchInfo {
        /**
         * 商品数量
         */
        private BigDecimal batchQty;
        /**
         * 更新前单价
         */
        private BigDecimal oldPrice;
        /**
         * 成本单价
         */
        private BigDecimal costPrice;

        /**
         * 含税成本金额
         */
        private BigDecimal taxCostMoney;
        /**
         * 合计成本金额
         */
        private BigDecimal costMoney;

        /**
         * 合计成本税金
         */
        private BigDecimal taxMoney;
    }
}
