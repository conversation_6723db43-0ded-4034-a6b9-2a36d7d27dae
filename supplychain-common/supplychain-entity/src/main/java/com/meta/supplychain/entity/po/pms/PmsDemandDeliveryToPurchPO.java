package com.meta.supplychain.entity.po.pms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meta.supplychain.entity.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value ="pms_demand_delivery_to_purch")
@Getter
@Setter
@ToString
public class PmsDemandDeliveryToPurchPO extends BaseEntity {
    /** ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 需求单号 */
    private String billNo;

    /** 单内序号 */
    private Long insideId;

    /** 转采类型,0不可转采, 1可转采 */
    private Integer type;

    /** 转采状态,0未转采, 1已转采 */
    private Integer status;

    /** 商品类型,0主品,1赠品 */
    private Integer goodsType;

    /** 商品编码 */
    private String skuCode;

    /** 商品名称 */
    private String skuName;

    /** 商品规格 */
    private String skuModel;

    /** 商品货号 */
    private String goodsNo;

    /** 品类编码 */
    private String categoryCode;

    /** 品类名称 */
    private String categoryName;

    /** 全路径品类编码,英文逗号分割 */
    private String categoryCodeAll;

    /** 商品条码 */
    private String barcode;

    /** 单位 */
    private String unit;

    /** 整件单位 */
    private String packageUnit;

    /** 进项税率，13%存13 */
    private BigDecimal inputTaxRate;

    /** 销项税率，13%存13 */
    private BigDecimal outputTaxRate;

    /** 配送部门实际库存 */
    private BigDecimal distDeptStockRealQty;

    /** 配送部门可用库存 */
    private BigDecimal distDeptStockAtpQty;

    /** 停靠点编码 */
    private String dockCode;

    /** 停靠点名称 */
    private String dockName;

    /** 采购批次 */
    private String purchBatchNo;

    /** 送货方式 0-到店，1-到客户 */
    private Integer sendMode;

    /** 配送包装率 */
    private BigDecimal deliveryUnitRate;

    /** 配送数量 */
    private BigDecimal deliveryQty;

    /** 确认转采购整件数量 */
    private BigDecimal dtpWholeQty;

    /** 确认转采购零头数量 */
    private BigDecimal dtpOddQty;

    /** 确认转采购数量 */
    private BigDecimal dtpQty;

    /** 不可转采原因 */
    private String reason;

    @Schema(description = "配送部门编码")
    private String distDeptCode;

    @Schema(description = "配送部门名称")
    private String distDeptName;
}