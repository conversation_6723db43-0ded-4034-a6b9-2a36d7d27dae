package com.meta.supplychain.entity.dto.pms.req.deliverytopurch;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/20 11:41
 **/
@Schema(description = "生成配转采数据入参")
@Data
public class DeliveryToPurchReq {
    @Schema(description = "来源1:需求单2:追加追减3:手工配转采")
    private Integer srcType;

    @Schema(description = "部门商品信息")
    private List<DeliveryToPurchDeptGoodsInfo> deptGoodsList;

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeliveryToPurchDeptGoodsInfo {
        @Schema(description = "部门商品的单内序号，前端生成,当前业务唯一")
        private Long insideId;

        @Schema(description = "是否直流 0-非直流 1-直流")
        private Integer directSign;

        @Schema(description = "要货部门编码")
        private String deptCode;

        @Schema(description = "要货部门名称")
        private String deptName;

        @Schema(description = "商品类型,1主品,2赠品")
        private Integer goodsType;

        @Schema(description = "商品编码")
        private String skuCode;

        @Schema(description = "商品名称")
        private String skuName;

        @Schema(description = "商品规格")
        private String skuModel;

        @Schema(description = "商品货号")
        private String goodsNo;

        @Schema(description = "品类编码")
        private String categoryCode;

        @Schema(description = "品类名称")
        private String categoryName;

        @Schema(description = "全路径品类编码,英文逗号分割")
        private String categoryCodeAll;

        @Schema(description = "商品条码")
        private String barcode;

        @Schema(description = "单位")
        private String unit;

        @Schema(description = "整件单位")
        private String packageUnit;

        @Schema(description = "进项税率，13%存13")
        private BigDecimal inputTaxRate;

        @Schema(description = "销项税率，13%存13")
        private BigDecimal outputTaxRate;

        @Schema(description = "采购批次")
        private String purchBatchNo;

        @Schema(description = "送货方式 0-到店，1-到客户")
        private Integer sendMode;

        @Schema(description = "配送包装率")
        private BigDecimal deliveryUnitRate;

        @Schema(description = "配送数量")
        private BigDecimal deliveryQty;

        @Schema(description = "分组字段,服务端处理")
        private String deptGoodsDeliveryToPurchKey;

        @Schema(description = "转采类型,0不可转采, 1可转采.服务端计算用，不用传")
        private Integer type;

        @Schema(description = "不可转采原因.服务端计算用，不用传")
        private String reason;

        @Schema(description = "订货申请信息,可以不传,需求单时必填")
        private List<OrderApplyInfo> orderApplyInfoList;

        public String getDeptGoodsDeliveryToPurchKey() {
            //部门商品表：商品类型 + 商品编码  + 需求批次 + 送货方式
            String key = goodsType + "_" + skuCode + "_" + purchBatchNo + "_" + sendMode;
            return key;
        }

        @Schema(description = "出货方-配送信息")
        private List<DeliveryToPurchDeliveryInfo> deliveryList;

    }

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderApplyInfo {
        @Schema(description = "订货申请单号")
        private String applyBillNo;

        @Schema(description = "订货申请单内序号")
        private Long applyInsideId;

        @Schema(description = "客户编码")
        private String customerCode;

        @Schema(description = "客户名称")
        private String customerName;

        @Schema(description = "要货数量")
        private BigDecimal qty;
    }

    @Builder
    @Data
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeliveryToPurchDeliveryInfo {
        @Schema(description = "当配送订单时，必填配送订单号")
        private String billNo;

        @Schema(description = "配送出货方的单内序号，前端生成,当前业务唯一或者配送订单商品明细行")
        private Long insideId;

        @Schema(description = "上一级部门商品单内序号")
        private Long pinsideId;

        @Schema(description = "停靠点编码")
        private String dockCode;

        @Schema(description = "停靠点名称")
        private String dockName;

        @Schema(description = "配送部门编码")
        private String distDeptCode;

        @Schema(description = "配送部门名称")
        private String distDeptName;

        @Schema(description = "配送包装率")
        private BigDecimal deliveryUnitRate;

        @Schema(description = "是否直流 0-非直流 1-直流")
        private Integer directSign;

        @Schema(description = "配送数量")
        private BigDecimal deliveryQty;

        @Schema(description = "配送部门可用库存")
        private BigDecimal distDeptStockAtpQty;

        @Schema(description = "配送部门实际库存")
        private BigDecimal distDeptStockRealQty;

        @Schema(description = "分组字段,服务端处理,部门商品传入")
        @TableField(exist = false)
        private String deptGoodsDeliveryToPurchKey;

        @Schema(description = "分组字段,服务端处理")
        @TableField(exist = false)
        private String deliveryToPurchKey;

        @Schema(description = "上级的商品信息,服务端处理")
        @ToString.Exclude // 排除 deptGoodsDetail 避免循环引用
        private DeliveryToPurchDeptGoodsInfo deptGoodsDetail;

        public String getDeliveryToPurchKey() {
            //配送出货方表：配送部门 + 停靠点 + 直流标记
            String key = deptGoodsDeliveryToPurchKey + "_" + distDeptCode + "_" + dockCode + "_" + directSign;
            return key;
        }

    }
}
