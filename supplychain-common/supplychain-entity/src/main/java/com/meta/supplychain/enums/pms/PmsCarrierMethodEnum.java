package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 承运方式枚举
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "承运方式", code = "pmsCarrierMethodEnum")
public enum PmsCarrierMethodEnum implements VerifiableEnum<Integer> {

    SELF_TRANSPORT(1, "自运"),
    CONSIGNMENT(2, "托运");

    private final Integer code;
    private final String desc;
} 