package com.meta.supplychain.enums.goods;

import com.meta.supplychain.enums.pms.PmsPurchPlanStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 商品类型（1：单规格 2：多规格 3：组合商品）
 *
 * <AUTHOR> cat
 * @date 2024/5/16 15:23
 */
@Getter
@AllArgsConstructor
public enum SkuTypeEnum {

    NORMAL(0,"普通品"),
    ADDITION(1,"附赠品"),
    GIFT(2,"附赠商品"),

    ;
    private Integer code;

    private String desc;

    public static SkuTypeEnum getInstance(Integer code) {
        return Arrays.stream(SkuTypeEnum.values())
                .filter(e -> Objects.equals(e.code, code))
                .findFirst()
                .orElse(null);
    }

}
