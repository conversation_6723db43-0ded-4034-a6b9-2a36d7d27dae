package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "单据确认送货标记",code = "PmsConfirmDeliverSignEnum")
public enum PmsConfirmDeliverSignEnum implements VerifiableEnum<Integer> {
    NO_NEED_CONFIRM(0, "无需确认"),
    NOT_CONFIRMED(1, "未确认"),
    CONFIRMED(2, "已确认");

    private Integer code;

    private String desc;

    public static PmsConfirmDeliverSignEnum getByCode(Integer code) {
        PmsConfirmDeliverSignEnum[] values = values();
        for (PmsConfirmDeliverSignEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}