package com.meta.supplychain.entity.dto.pms.resp.accept;

import cn.linkkids.framework.croods.common.date.LocalDates;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.meta.supplychain.enums.YesOrNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订货申请请求
 */
@Data
public class AcceptanceInfoResp {

    @Schema(description = "单据来源 0 普通 1 批销 2 大数据 3 朝批")
    private Integer billSource;

    @Schema(description = "单据来源描述")
    private String billSourceDesc;

    @Schema(description = "申请单号")
    private String billNumber;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "供应商编码")
    private String supplierCode;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "直发供应商编码")
    private String directSuppCode;

    @Schema(description = "直发供应商名称")
    private String directSuppName;

    @Schema(description = "合同号")
    private String contractNumber;

    @Schema(description = "补签序号")
    private String repairsignId;

    @Schema(description = "单据方向 1.验收，-1.退货")
    private Integer billDirection;

    @Schema(description = "是否冲单 0-否，1-是")
    private Integer isCancelBill;

    @Schema(description = "验收类型 0-普通验收 1-联营验收")
    private Integer acceptType;

    @Schema(description = "直流标志 0-非直流 1-直流")
    private Integer directSign;

    @Schema(description = "是否标记 0-未标记，1-已标记")
    private Integer isFlag;

    @Schema(description = "单据状态 0-草稿 1-待审核 2-已审核 3-作废")
    private Integer billState;

    @Schema(description = "追加截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate addDate;

    @Schema(description = "直流部门编码")
    private String directDeptCode;

    @Schema(description = "制单人编码")
    private String buildManCode;

    @Schema(description = "制单人名称")
    private String buildManName;

    @Schema(description = "制单部门编码")
    private String buildDeptCode;

    @Schema(description = "制单部门名称")
    private String buildDeptName;

    @Schema(description = "申请备注")
    private String remark;

    @Schema(description = "标记备注")
    private String flagRemark;

    @Schema(description = "修改人code")
    private String modifyManCode;

    @Schema(description = "修改人名称")
    private String modifyManName;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime modifyTime;

    @Schema(description = "审核人code")
    private String auditManCode;

    @Schema(description = "审核人姓名")
    private String auditManName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "作废人code")
    private String blankManCode;

    @Schema(description = "作废人名称")
    private String blankManName;

    @Schema(description = "作废时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime blankTime;

    @Schema(description = "入账时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate entryTime;

    @Schema(description = "确认人code")
    private String confirmManCode;

    @Schema(description = "确认人名称")
    private String confirmManName;

    @Schema(description = "确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmTime;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    @Schema(description = "提交人code")
    private String submitManCode;

    @Schema(description = "提交人名称")
    private String submitManName;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @Schema(description = "供应商确认人编码")
    private String suppConfirmCode;

    @Schema(description = "供应商确认人名称")
    private String suppConfirmName;

    @Schema(description = "供应商确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime suppConfirmTime;

    @Schema(description = "供应商确认状态 0-未确认 1-已确认")
    private Integer suppConfirmState;

    @Schema(description = "供应商更新人编码")
    private String suppUpdateCode;

    @Schema(description = "供应商更新人名称")
    private String suppUpdateName;

    @Schema(description = "供应商更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime suppUpdateTime;

    @Schema(description = "送货单号")
    private String sendBillNumber;

    @Schema(description = "采购单号")
    private String acceptBillNumber;

    @Schema(description = "冲红单号")
    private String cancelBillNumber;

    @Schema(description = "冲红对应的原单号")
    private String originBillNumber;

    @Schema(description = "数量")
    private BigDecimal amount;

    @Schema(description = "金额")
    private BigDecimal money;

    @Schema(description = "管理分类项编码")
    private String assistCategoryClass;

    @Schema(description = "管理分类编码")
    private String assistCategory;

    @Schema(description = "管理分类名称")
    private String assistCategoryName;

    @Schema(description = "明细", type = "array", implementation = AcceptDetailInfo.class)
    private List<AcceptDetailInfo> detailInfoList;

    @Schema(description = "打印使用字段-部门地址wholeAddress")
    private String deptAddress;

    @Schema(description = "打印使用字段-部门联系人managerUserName")
    private String deptManage;

    @Schema(description = "打印使用字段-部门联系电话serviceHotline")
    private String deptHotline;

    @Schema(description = "打印使用字段-供应商地址wholeAddress")
    private String supplierAddress;

    @Schema(description = "打印使用字段-供应商联系人linkname")
    private String supplierManage;

    @Schema(description = "打印使用字段-供应商联系电话phone")
    private String supplierHotline;

    @Schema(description = "打印使用字段-合同经营方式")
    private Integer contractManageMode;

    @Schema(description = "打印次数")
    private Long printCount;

    @Schema(description = "批次供应商编码")
    private String supplierCodeBatch;

    @Schema(description = "批次供应商名称")
    private String supplierNameBatch;


    public static void convertResp(AcceptanceInfoResp input, AcceptBillResp target) {
        // 单据来源
        input.setBillSource(target.getBillSource());
        // 单据来源描述
        input.setBillSourceDesc(target.getBillSourceDesc());
        // 申请单号（采购单号）
        input.setBillNumber(target.getPurchBillNo());
        // 部门编码
        input.setDeptCode(target.getDeptCode());
        // 部门名称
        input.setDeptName(target.getDeptName());
        // 供应商编码
        input.setSupplierCode(target.getSupplierCode());
        // 供应商名称
        input.setSupplierName(target.getSupplierName());
        // 直发供应商编码
        input.setDirectSuppCode(target.getDirectSupplierCode());
        // 直发供应商名称
        input.setDirectSuppName(target.getDirectSupplierName());
        // 合同号
        input.setContractNumber(target.getContractNo());
        // 补签序号
        input.setRepairsignId(target.getRepairSignId());
        // 单据方向
        input.setBillDirection(target.getBillDirection());
        // 是否冲单
        input.setIsCancelBill(target.getReversalBillSign());
        // 验收类型
        input.setAcceptType(target.getType());
        // 直流标志
        input.setDirectSign(target.getDirectSign());
        // 单据状态
        input.setBillState(target.getStatus());
        // 追加截止日期
        input.setAddDate(target.getAppendValidity());
        // 直流部门编码
        input.setDirectDeptCode(target.getDirectDeptCode());
        // 制单人编码
        input.setBuildManCode(target.getCreateCode());
        // 制单人名称
        input.setBuildManName(target.getCreateName());
        // 制单部门编码
        input.setBuildDeptCode(target.getDeptCode());
        // 制单部门名称
        input.setBuildDeptName(target.getDeptName());
        // 申请备注
        input.setRemark(target.getRemark());
        // 修改人编码
        input.setModifyManCode(target.getUpdateCode());
        // 修改人名称
        input.setModifyManName(target.getUpdateName());
        // 更新时间
        input.setModifyTime(target.getUpdateTime());
        // 审核人编码
        input.setAuditManCode(target.getAuditManCode());
        // 审核人姓名
        input.setAuditManName(target.getAuditManName());
        // 创建时间
        input.setCreateTime(target.getCreateTime());
        // 作废人编码
        input.setBlankManCode(target.getCancelManCode());
        // 作废人名称
        input.setBlankManName(target.getCancelManName());
        // 作废时间
        input.setBlankTime(target.getCancelTime());
        // 入账时间
        input.setEntryTime(target.getAccDate());
        // 确认人编码
        input.setConfirmManCode(target.getConfirmManCode());
        // 确认人名称
        input.setConfirmManName(target.getConfirmManName());
        // 确认时间
        input.setConfirmTime(target.getConfirmTime());
        // 审核时间
        input.setAuditTime(target.getAuditTime());
        // 提交人编码
        input.setSubmitManCode(target.getSubmitManCode());
        // 提交人名称
        input.setSubmitManName(target.getSubmitManName());
        // 提交时间
        input.setSubmitTime(target.getSubmitTime());
        // 供应商确认人编码
        input.setSuppConfirmCode(target.getSuppConfirmCode());
        // 供应商确认人名称
        input.setSuppConfirmName(target.getSuppConfirmName());
        // 供应商确认时间
        input.setSuppConfirmTime(target.getSuppConfirmTime());
        // 供应商确认状态
        input.setSuppConfirmState(target.getSuppConfirmStatus());
        // 供应商更新人编码
        input.setSuppUpdateCode(target.getSuppUpdateCode());
        // 供应商更新人名称
        input.setSuppUpdateName(target.getSuppUpdateName());
        // 供应商更新时间
        input.setSuppUpdateTime(target.getSuppUpdateTime());
        // 送货单号
        input.setSendBillNumber(target.getSendBillNo());
        // 采购单号（已处理）
        input.setAcceptBillNumber(target.getBillNo());
        // 冲红单号
        input.setCancelBillNumber(target.getReversalBillNo());
        // 冲红对应的原单号
        input.setOriginBillNumber(target.getOriginBillNo());
        // 数量
        input.setAmount(target.getTotalQty());
        // 金额
        input.setMoney(target.getTotalMoney());
        // 管理分类项编码
        input.setAssistCategoryClass(target.getManageCategoryClass());
        // 管理分类编码
        input.setAssistCategory(target.getManageCategoryCode());
        // 管理分类名称
        input.setAssistCategoryName(target.getManageCategoryName());
        // 部门地址
        input.setDeptAddress(target.getDeptAddress());
        // 部门联系人
        input.setDeptManage(target.getDeptManage());
        // 部门联系电话
        input.setDeptHotline(target.getDeptHotline());
        // 供应商地址
        input.setSupplierAddress(target.getSupplierAddress());
        // 供应商联系人
        input.setSupplierManage(target.getSupplierManage());
        // 供应商联系电话
        input.setSupplierHotline(target.getSupplierHotline());
        // 打印次数
        input.setPrintCount(target.getPrintCount());
        // 批次供应商编码
        input.setSupplierCodeBatch(target.getSupplierCodeBatch());
        // 批次供应商名称
        input.setSupplierNameBatch(target.getSupplierNameBatch());

        // 明细
        input.setDetailInfoList(convertDetailResp(target.getDetailList()));
    }


    public static   List<AcceptDetailInfo> convertDetailResp(List<AcceptDetailResp> acceptDetailRespList) {
        return acceptDetailRespList.stream().map(target->{
            AcceptDetailInfo input = new AcceptDetailInfo();
            // 申请单号
            input.setBillNumber(target.getPurchBillNo());
            // 部门编码
            input.setDeptCode(target.getDeptCode());
            // 部门名称
            input.setDeptName(target.getDeptName());
            // 是否附赠品
            input.setIsAdditionalGifts(YesOrNoEnum.NO.getCode().equals(target.getSkuType())?YesOrNoEnum.NO.getCode():YesOrNoEnum.YES.getCode());
            // 包装率单位名称
            input.setPackageUnitName(target.getPackageUnitName());
            // 单内序号
            input.setInsideId(target.getInsideId());
            // 商品编码
            input.setGoodsCode(target.getSkuCode());
            // 商品类型
            input.setGoodsType(target.getSkuType());
            // 商品名称
            input.setGoodsName(target.getSkuName());
            // 商品货号
            input.setGoodsNo(target.getGoodsNo());
            // 商品SKU编码
            input.setSkuCode(target.getSkuCode());
            // 商品条码
            input.setBarcode(target.getBarcode());
            // 商品规格
            input.setGoodsSpec(target.getSkuModel());
            // 销售模式
            input.setSaleMod(target.getSaleMode());
            // 商品类别
            input.setCategoryCode(target.getCategoryCode());
            // 商品分类名称
            input.setCategoryName(target.getCategoryName());
            // 订货途径
            input.setOrderRoute(target.getOrderRoute());
            // 直流标志
            input.setDirectSign(target.getDirectSign());
//            // 直流供应商编码
//            input.setDistCode(target.getDistCode());
//            // 出货途径
//            input.setShippingWay(target.getShippingWay());
//            // 出货方式描述
//            input.setShippingWaysDesc(target.getShippingWaysDesc());
            // 订货数量
            input.setAmount(target.getAcceptQty());
            // 箱数量
            input.setBoxAmount(target.getWholeQty());
            // 零头数量
            input.setRemnantAmount(target.getOddQty());
            // 订单数量
            input.setOrderAmount(target.getOrderQty());
//            // 赠品订单数量
//            input.setGiftOrderAmount(target.getGiftOrderAmount());
//            // 赠品数量
//            input.setGiftAmount(target.getGiftAmount());
            // 基础单位
            input.setBasicUnit(target.getBasicUnit());
            // 整箱单位
            input.setWholeUnit(target.getWholeUnit());
            // 单位比率
            input.setUnitRate(target.getUnitRate());
            // 税率
            input.setTaxRate(target.getInputTaxRate());
            // 订货单价
            input.setPurchPrice(target.getPurchPrice());
            // 订货金额（含税）
            input.setPurchMoney(target.getPurchMoney());
            // 订货税金
            input.setPurchTax(target.getPurchTax());
//            // 商品毛重
//            input.setSkuWeight(target.getSkuWeight());
//            // 单个商品毛重
//            input.setSingleSkuWeight(target.getSingleSkuWeight());
            // 商品档案进价
            input.setSinglePrice(target.getLastPurchPrice());
            // 零售金额
            input.setSingleMoney(target.getSaleMoney());
            // 特供价
            input.setSpecialOfferPrice(target.getSpecialPrice());
            // 批次号
            input.setBatchManageNum(target.getBatchNo());
            // 效期条码
            input.setValidityBarcode(target.getPeriodBarcode());
//            // 出货方名称
//            input.setShipperName(target.getShipperName());
//            // 出货方编码
//            input.setShipperCode(target.getShipperCode());
            // 申请备注
            input.setRemark(target.getRemark());
            // 合同号
            input.setContractNumber(target.getContractNo());
            // 履行时间
//            input.setPerformTime(target.getPerformTime());
            // 效期商品标识
            input.setPeriodFlag(target.getPeriodFlag());
            // 生产日期
            input.setProductDate(target.getProductDate());
            // 过期日期
            input.setExpirateDate(target.getExpireDate());
            // 计量属性
            input.setMeasureProperty(target.getUomAttr());
            // 在途库存
            input.setOnWayStockNum(target.getOnWayStockQty());
            // 商品库存
            input.setStockNum(target.getStockQty());
            // 管理分类项编码
            input.setAssistCategoryClass(target.getManageCategoryClass());
            // 管理分类编码
            input.setAssistCategory(target.getManageCategoryCode());
            // 管理分类名称
            input.setAssistCategoryName(target.getManageCategoryName());
            // 促销编码
//            input.setPromotionId(target.getProductDate());
            // 验收退补价格
            input.setAcceptRefundPrice(target.getAcceptRefundPrice());
            // 合同商品进价
            input.setContractPurchPrice(target.getContractPrice());
            // 合同商品特供价
            input.setContractSpecialPrice(target.getContractSpecialPrice());
            // 价格类型
            input.setPriceType(target.getPriceType());
            // 打印字段--订货无税金额
            input.setNoTaxMoney(target.getPurchMoney());
            // 销项税率
            input.setOutTaxRate(target.getOutputTaxRate());
            return input;
        }).collect(Collectors.toList());
    }
}
