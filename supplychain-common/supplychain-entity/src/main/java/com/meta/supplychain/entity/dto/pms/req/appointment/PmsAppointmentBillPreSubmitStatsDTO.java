package com.meta.supplychain.entity.dto.pms.req.appointment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 供应商预约单统计数据
 */
@Data
public class PmsAppointmentBillPreSubmitStatsDTO {
    @Schema(description = "本次预约商品数")
    private Integer skuCount;

    @Schema(description = "本次预约涉及到的采购订单数")
    private Integer purchBillCount;

    @Schema(description = "本次预约总件数")
    private BigDecimal totalAppointmentWholeQty = BigDecimal.ZERO;

    @Schema(description = "本次预约零头总数量")
    private BigDecimal totalAppointmentOddQty = BigDecimal.ZERO;

    @Schema(description = "本次预约总数量")
    private BigDecimal totalAppointmentQty = BigDecimal.ZERO;
}
