package com.meta.supplychain.entity.dto.pms.req.appointment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meta.supplychain.annotation.LocalDatetimePattern;
import com.meta.supplychain.enums.md.MdDeliveryDockConstraintRuleEnum;
import com.meta.supplychain.enums.md.MdDeliveryDockTypeEnum;
import com.meta.supplychain.serializes.LocalDateDeserializer;
import com.meta.supplychain.serializes.LocalDateSerializer;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 查询停靠点预约信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PmsDeliveryDockLimitQueryDTO {

    @Schema(description = "部门编码列表")
    private List<String> deptCodeList;

    @Schema(description = "停靠点编码")
    private String dockCode;

    @EnumValue(type = MdDeliveryDockTypeEnum.class)
    @Schema(description = "停靠点类型 枚举mdDeliveryDockTypeEnum")
    private List<Integer> dockType;

    @EnumValue(type = MdDeliveryDockConstraintRuleEnum.class)
    @Schema(description = "限量规则 枚举mdDeliveryDockConstraintRuleEnum")
    private Integer constraintRule;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Schema(description = "起始时间")
    private LocalDate startDate;

    @LocalDatetimePattern("yyyy-MM-dd")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @Schema(description = "截止时间")
    private LocalDate endDate;

    @Schema(description = "时段行号")
    private Integer timeInsideId;
}
