package com.meta.supplychain.entity.dto.wds;

import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustReq;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
public class WdsDeliveryAdjustDTO {
    // 调整记录流水号
    private String adjustBillNo;
    WdsDeliveryOrderAdjustReq req;
    WdDeliveryBillPO oldBill;
    WdDeliveryBillPO newBill;
    /**
     * 所有明细汇总
     */
    Map<Long, WdDeliveryBillDetailPO> existDetailMap;
    /**
     * 修改的明细
     */
    List<WdDeliveryBillDetailPO> changeBillDetailList;
    /**
     * 用来更新的明细数据 包含 id 要更新的字段
     */
    List<WdDeliveryBillDetailPO> detailPOListForUpdate;
}
