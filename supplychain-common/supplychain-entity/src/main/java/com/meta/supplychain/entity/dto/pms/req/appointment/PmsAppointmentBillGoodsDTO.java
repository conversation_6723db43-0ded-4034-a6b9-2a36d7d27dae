package com.meta.supplychain.entity.dto.pms.req.appointment;

import com.meta.supplychain.annotation.DecimalScale;
import com.meta.supplychain.entity.dto.BaseDTO;
import com.meta.supplychain.enums.common.SkuTypeEnum;
import com.meta.supplychain.enums.goods.MeasurePropertyEnum;
import com.meta.supplychain.validation.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 供应商预约单关联商品明细DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "供应商预约单关联商品明细DTO")
public class PmsAppointmentBillGoodsDTO extends BaseDTO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "预约单据号")
    private String appointmentBillNo;

    @NotBlank(message = "采购订单号不能为空")
    @Schema(description = "采购订单号")
    private String purchBillNo;

    @NotNull(message = "采购订单商品明细行id不可为空")
    @Schema(description = "采购订单商品明细行id")
    private Long purchGoodsDetailId;

    @NotNull(message = "采购订单商品明细行行号不可为空")
    @Schema(description = "采购订单商品明细行行号")
    private Long purchGoodsDetailInsideId;

    @NotNull(message = "商品类型不能为空")
    @EnumValue(type = SkuTypeEnum.class, required = true, message = "商品类型值无效")
    @Schema(description = "商品类型")
    private Integer skuType;

    @NotBlank(message = "商品编码不能为空")
    @Schema(description = "商品编码")
    private String skuCode;

    @NotBlank(message = "商品名称不能为空")
    @Schema(description = "商品名称")
    private String skuName;

    @NotBlank(message = "商品条码不能为空")
    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "品类编码")
    private String categoryCode;

    @Schema(description = "品类名称")
    private String categoryName;

    @Schema(description = "品牌编码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "基本单位")
    private String basicUnit;

    @Schema(description = "整件单位")
    private String packageUnit;

    @Schema(description = "商品规格")
    private String skuModel;

    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;

    @NotNull(message = "商品包装率不可为空")
    @Schema(description = "商品包装率")
    private BigDecimal unitRate;

    @Schema(description = "订货包装率")
    private BigDecimal purchUnitRate;

    @EnumValue(type = MeasurePropertyEnum.class, message = "非法的计量属性枚举")
    @Schema(description = "计量属性")
    private Integer uomAttr;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "整件数量")
    private BigDecimal wholeQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "零头数量")
    private BigDecimal oddQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "采购数量")
    private BigDecimal purchQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "可约整件数量")
    private BigDecimal canAppointmentWholeQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "可约零头数量")
    private BigDecimal canAppointmentOddQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "可约数量")
    private BigDecimal canAppointmentQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @Schema(description = "已预约数量(快照时)")
    private BigDecimal appointedQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @NotNull(message = "本地预约件数不可为空")
    @Schema(description = "本次预约件数")
    private BigDecimal appointmentWholeQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @NotNull(message = "本次预约零头数量不能为空")
    @Schema(description = "本次预约零头数量")
    private BigDecimal appointmentOddQty = BigDecimal.ZERO;

    @DecimalScale(value = 4)
    @Digits(integer = 18, fraction = 4, message = "非法的数值格式（整数部分不超过18位，小数部分不超过4位）")
    @NotNull(message = "本次预约数量不能为空")
    @Schema(description = "本次预约数量")
    private BigDecimal appointmentQty = BigDecimal.ZERO;

    @Schema(description = "商品关联订单信息")
    private PmsAppointmentBillPurchDTO refPurchBill;

    @Schema(description = "商品维度订单信息")
    private List<PmsAppointmentBillPurchDTO> purchList = new ArrayList<>();

    /**
     * 差异字段
     * 仅当改字段为true时，差异字段才有参考意义
     */
    @Schema(description = "当前采购单数据是否有变化")
    private Boolean changed = false;

    @Schema(description = "是否有对应的商品明细行")
    private Boolean exist = true;

    @Schema(description = "整件数量")
    private BigDecimal wholeQtyChanged;

    @Schema(description = "零头数量")
    private BigDecimal oddQtyChanged;

    @Schema(description = "采购数量")
    private BigDecimal purchQtyChanged;

    @Schema(description = "可约整件数量")
    private BigDecimal canAppointmentWholeQtyChanged;

    @Schema(description = "可约零头数量")
    private BigDecimal canAppointmentOddQtyChanged;

    @Schema(description = "可约数量")
    private BigDecimal canAppointmentQtyChanged;

    @Schema(description = "已预约数量(快照时)")
    private BigDecimal appointedQtyChanged;

    /**
     * 预约数量校验
     */
    @SuppressWarnings("DuplicatedCode")
    public boolean validateAppointmentQty() {
        if (canAppointmentQty.compareTo(appointmentQty) < 0) {
            return false;
        }
        if (canAppointmentWholeQty.compareTo(appointmentWholeQty) < 0) {
            return false;
        }
        if (canAppointmentOddQty.compareTo(appointmentOddQty) < 0) {
            return false;
        }
        return true;
    }

    /**
     * 差异处理
     */
    public void markChanged(PmsAppointmentBillGoodsDTO latest) {
        if (latest == null) {
            return;
        }
        if (wholeQty.compareTo(latest.getWholeQty()) != 0) {
            changed = true;
            wholeQtyChanged = latest.getWholeQty();
        }
        if (oddQty.compareTo(latest.getOddQty()) != 0) {
            changed = true;
            oddQtyChanged = latest.getOddQty();
        }
        if (purchQty.compareTo(latest.getPurchQty()) != 0) {
            changed = true;
            purchQtyChanged = latest.getPurchQty();
        }
        if (canAppointmentQty.compareTo(latest.getCanAppointmentQty()) != 0) {
            changed = true;
            canAppointmentQtyChanged = latest.getCanAppointmentQty();
        }
        if (canAppointmentWholeQty.compareTo(latest.getCanAppointmentWholeQty()) != 0) {
            changed = true;
            canAppointmentWholeQtyChanged = latest.getCanAppointmentWholeQty();
        }
        if (canAppointmentOddQty.compareTo(latest.getCanAppointmentOddQty()) != 0) {
            changed = true;
            canAppointmentOddQtyChanged = latest.getCanAppointmentOddQty();
        }
    }

    /**
     * 数量校验
     * 总数量 = 整件数量 * 商品包装率 + 零头数量
     * 零头数量不大于整件数量
     * 数量字段不能小于零
     */
    public Boolean validateQty() {
        // 校验商品包装率
        if (this.unitRate == null || this.unitRate.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        // 校验数量字段不能为null且不能小于零
        if (wholeQty == null || wholeQty.compareTo(BigDecimal.ZERO) < 0 ||
            oddQty == null || oddQty.compareTo(BigDecimal.ZERO) < 0 ||
            purchQty == null || purchQty.compareTo(BigDecimal.ZERO) < 0 ||
            canAppointmentWholeQty == null || canAppointmentWholeQty.compareTo(BigDecimal.ZERO) < 0 ||
            canAppointmentOddQty == null || canAppointmentOddQty.compareTo(BigDecimal.ZERO) < 0 ||
            canAppointmentQty == null || canAppointmentQty.compareTo(BigDecimal.ZERO) < 0 ||
            appointmentWholeQty == null || appointmentWholeQty.compareTo(BigDecimal.ZERO) < 0 ||
            appointmentOddQty == null || appointmentOddQty.compareTo(BigDecimal.ZERO) < 0 ||
            appointmentQty == null || appointmentQty.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }

        // 总数量 = 整件数量 * 商品包装率 + 零头数量
        BigDecimal expectedPurchQty = wholeQty.multiply(unitRate).add(oddQty);
        if (purchQty.compareTo(expectedPurchQty) != 0) {
            return false;
        }

        // 总数量 = 整件数量 * 商品包装率 + 零头数量
        BigDecimal expectedCanAppointmentQty = canAppointmentWholeQty.multiply(unitRate).add(canAppointmentOddQty);
        if (canAppointmentQty.compareTo(expectedCanAppointmentQty) != 0) {
            return false;
        }

        // 总数量 = 整件数量 * 商品包装率 + 零头数量
        BigDecimal expectedAppointmentQty = appointmentWholeQty.multiply(unitRate).add(appointmentOddQty);
        if (appointmentQty.compareTo(expectedAppointmentQty) != 0) {
            return false;
        }
        
        return true;
    }
}