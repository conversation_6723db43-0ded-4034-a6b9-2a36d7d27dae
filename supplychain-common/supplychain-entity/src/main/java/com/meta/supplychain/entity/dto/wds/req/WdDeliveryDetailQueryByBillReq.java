package com.meta.supplychain.entity.dto.wds.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-4-2 15:21:44
 */
@Schema(description = "调配送订单")
@Getter
@Setter
@ToString
public class WdDeliveryDetailQueryByBillReq extends WdDeliveryBillQueryReq {

    @Schema(description = "入货部门编码列表")
     List<String> inDeptCodeList;

    /**
     * 匹配存在该商品的配送订单，支持按照编码、条码、货号、名称模糊匹配
     */

    @Schema(description = "匹配存在该商品的配送订单，支持按照编码、条码、货号、名称模糊匹配")
    private String skuKeyWord;

    // 匹配配送订单制单人，支持编码、名称模糊匹配
    @Schema(description = "匹配配送订单制单人，支持编码、名称模糊匹配")
    private String createCodeKeyWord;

    // 匹配配送订单到店日期
    @Schema(description = "匹配配送订单到店/送货日期  yyyy-MM-dd")
    private String toStoreDate;


    @Schema(description = "有效日期  yyyy-MM-dd")
    private String validDate;

}
