package com.meta.supplychain.entity.dto.md.component.bizrule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/07 17:21
 **/
@Data
public class GetBillNoReq {
    @Schema(description = "单据号类型;contract,合同号;" +
            "contract_define,合同商品定义号;" +
            "purchase_order,采购订货申请;" +
            "purchase_return,采购退货申请;" +
            "delivery_order,配送订货申请;" +
            "delivery_return,配送退货申请;" +
            "cross_docking_order,直流申请;" +
            "procurement_acceptance,采购验收单;" +
            "acceptance_return,采购退货单;" +
            "incoming_delivery,配送验收单;" +
            "work_order,加工单;" +
            "split_order,拆分单;" +
            "transfer_order,门店调拨单;" +
            "inventory_adjustment,库存调整单;" +
            "inventory_count,盘点计划;" +
            "bom_order,BOM单;" +
            "incoming_return,配送退货单;" +
            "initial_stocktaking,盘点初盘;" +
            "replay_stocktaking,盘点复盘;" +
            "product_conversion,转码单;" +
            "allocation_order,分货申请;" +
            "vo_validity,效期管理;" +
            "inventory_list,盘点清单;" +
            "WM,波次管理;" +
            "PL,拣货单;" +
            "SN,配送发货单;" +
            "RA,退货收货;" +
            "PL,商品移位;" +
            "order_request,订货申请;" +
            "order_request,退货申请;")
    private String billNoType;

    @Schema(description = "部门编码,非必填,根据业务场景传入")
    private String deptCode;
}
