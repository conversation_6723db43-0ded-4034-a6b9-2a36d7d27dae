package com.meta.supplychain.entity.dto.pms.resp.demand;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class PmsDemandPruchDeliveryRefResp{
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 需求单号 */
    private String billNo;

    /** 需求商品部门表单内序号,pms_demand_dept_goods_detail.inside_id */
    private Long deptGoodsInsideId;

    /** 需求商品部门表单内序号,pms_demand_goods_detail.inside_id */
    private Long goodsInsideId;

    /** 配送出货方单内序号pms_demand_delivery_shipper.inside_id */
    private Long deliveryShipperInsideId;

    /** 采购供应商出货方单内序号,pms_demand_purch_shipper.inside_id */
    private Long purchShipperInsideId;

    /** 下游(配送订单或采购订单)明细.inside_id */
    private Long refInsideId;

    /** 分摊数量 */
    private BigDecimal qty;

    @Schema(description = "来源需求数量")
    private BigDecimal srcDemandQty;

    /** 下游单号(配送订单号或采购订单号) */
    private String refBillNo;

    /** 单据类型,0采购, 1配送,2配转采的采购,3配转采的采购超出的部分，4直流采购 */
    private Integer type;

    @Schema(description = "订货申请单号")
    private String applyBillNo;

    @Schema(description = "订货申请商品明细行号")
    private Long applyInsideId;

    @Schema(description = "商品编码")
    private String skuCode;

    /**
     * 商品类型 0普通商品 1赠品
     */
    @Schema(title = "商品类型",description = "商品类型 0普通商品 1赠品")
    private Integer skuType;

    @Schema(description = "来源订货价")
    private BigDecimal srcOrderPrice;
}