package com.meta.supplychain.convert.wds;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.wds.resp.ShipDiffExcelView;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBatchDetailResp;
import com.meta.supplychain.entity.dto.wds.resp.WdShipAccDiffBillResp;
import com.meta.supplychain.entity.po.wds.WdRefundAcceptBatchDetailPO;
import com.meta.supplychain.entity.po.wds.WdShipAccDiffBatchDetailPO;
import com.meta.supplychain.entity.po.wds.WdShipAccDiffBillPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ShiDiffConvert extends StandardEnumConvert {
    ShiDiffConvert INSTANCE = Mappers.getMapper(ShiDiffConvert.class);

    // WdShipAccDiffBillPO 转 WdShipAccDiffBillResp
    WdShipAccDiffBillResp convertToResult(WdShipAccDiffBillPO po);

    List<WdShipAccDiffBillResp> convertToResultList(List<WdShipAccDiffBillPO> poList);

    // WdShipAccDiffBillResp 转为 ShipDiffExcelView
    @Mapping(target = "approveTime", source = "approveTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "createTime", source = "createTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "shipTime", source = "shipTime",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Mapping(target = "statusDesc", expression = "java(convertToDesc(\"WDAcceptDiffStatusEnum\", result.getStatus()))")
    @Mapping(target = "totalDiffTaxMoney", expression = "java(formatMoney(result.getTotalDiffTaxMoney()))")
    ShipDiffExcelView convertToExcelView(WdShipAccDiffBillResp result);



    List<ShipDiffExcelView> convertToExcelViewList(List<WdShipAccDiffBillResp> wdShipAccDiffBillResps);

    // WdShipAccDiffBatchDetailPO 转 WdShipAccDiffBatchDetailResp
    WdShipAccDiffBatchDetailResp convertToBatchDetailResp(WdShipAccDiffBatchDetailPO po);

    List<WdShipAccDiffBatchDetailResp> convertToBatchDetailRespList(List<WdShipAccDiffBatchDetailPO> poList);



}
