package com.meta.supplychain.convert.pms;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ViewField;
import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.pms.resp.PmsAppointmentBillStatsDTO;
import com.meta.supplychain.entity.dto.pms.view.PmsAppointmentBillStatsView;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 预约单统计信息转换器
 * <AUTHOR>
 */
@Mapper
public interface PmsAppointmentBillStatsConvert extends StandardEnumConvert {

    PmsAppointmentBillStatsConvert INSTANCE = Mappers.getMapper(PmsAppointmentBillStatsConvert.class);

    /**
     * 将PmsAppointmentBillStatsDTO转换为PmsAppointmentBillStatsView（导出视图）
     * @param dto 预约单统计信息DTO
     * @return 预约单统计信息导出视图
     */
    @Mapping(target = "opSource", expression = "java(convertToDesc(\"pmsBookingDocumentSourceEnum\", dto.getOpSource()))")
    @Mapping(target = "billDirection", expression = "java(convertToDesc(\"pmsBookingCategoryEnum\", dto.getBillDirection()))")
    @Mapping(target = "directSign", expression = "java(convertToDesc(\"YesOrNoEnum\", dto.getDirectSign()))")
    @Mapping(target = "appointmentMode", expression = "java(convertToDesc(\"pmsBookingMethodEnum\", dto.getAppointmentMode()))")
    @Mapping(target = "defaultQtySign", expression = "java(convertToDesc(\"pmsDefaultBookingQuantityEnum\", dto.getDefaultQtySign()))")
    @Mapping(target = "transportMode", expression = "java(convertToDesc(\"pmsCarrierMethodEnum\", dto.getTransportMode()))")
    @Mapping(target = "status", expression = "java(convertToDesc(\"pmsBookingStatusEnum\", dto.getStatus()))")
    @Mapping(target = "createName", expression = "java(dto.getCreateCode() + \"-\" + dto.getCreateName())")
    @Mapping(target = "updateName", expression = "java(dto.getUpdateCode() + \"-\" + dto.getUpdateName())")
    @Mapping(target = "submitManName", expression = "java(dto.getSubmitManCode() != null ? dto.getSubmitManCode() + \"-\" + dto.getSubmitManName() : null)")
    @Mapping(target = "cancelManName", expression = "java(dto.getCancelManCode() != null ? dto.getCancelManCode() + \"-\" + dto.getCancelManName() : null)")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "startValidityTime", target = "startValidityTime")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "endValidityTime", target = "endValidityTime")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "startDeliverTime", target = "startDeliverTime")
    @Mapping(dateFormat = "yyyy-MM-dd", source = "endDeliverTime", target = "endDeliverTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "planArrivalTime", target = "planArrivalTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "submitTime", target = "submitTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "cancelTime", target = "cancelTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "updateTime", target = "updateTime")
    PmsAppointmentBillStatsView dto2view(PmsAppointmentBillStatsDTO dto);

    @AfterMapping
    default void afterMapping(@MappingTarget Object target) {
        try {
            Field[] fields = target.getClass().getDeclaredFields();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ViewField.class) && field.getType().equals(BigDecimal.class)) {
                    field.setAccessible(true);
                    BigDecimal value = (BigDecimal) field.get(target);
                    if (value != null) {
                        BigDecimal stripped = value.stripTrailingZeros();
                        // 避免出现科学计数法
                        if (stripped.scale() < 0) {
                            stripped = stripped.setScale(0, RoundingMode.UNNECESSARY);
                        }
                        field.set(target, stripped);
                    }
                }
            }
        } catch (Exception e) {
            Logs.error("MapStruct Convert BigDecimal字段处理失败", e);
        }
    }
} 