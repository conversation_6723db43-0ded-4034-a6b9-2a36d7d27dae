package com.meta.supplychain.entity.dto.replenishment.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 单号规则配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryBillNoRule implements Serializable {

    /**
     * 单据类型
     */
    @NotBlank(message = "单据类型不能为空")
    private String billType;

    private String deptCode;

    private Integer qty = 1;

}
