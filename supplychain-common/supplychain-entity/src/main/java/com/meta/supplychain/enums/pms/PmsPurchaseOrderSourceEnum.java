package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采购订单来源 0-手工单，1-需求单，2-配转采
 */
@Getter
@AllArgsConstructor
@EnumMark(name = "采购订单来源",code = "PmsPurchaseOrderSourceEnum")
public enum PmsPurchaseOrderSourceEnum implements VerifiableEnum<Integer> {
    MANUAL(0, "手工"),
    DEMAND(1, "需求单"),
    DELIVERY(2, "配转采"),
    ;

    ;
    private Integer code;

    private String desc;

    public static PmsPurchaseOrderSourceEnum getByCode(Integer code) {
        PmsPurchaseOrderSourceEnum[] values = values();
        for (PmsPurchaseOrderSourceEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
