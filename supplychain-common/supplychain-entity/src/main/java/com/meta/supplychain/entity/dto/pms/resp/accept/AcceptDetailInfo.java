package com.meta.supplychain.entity.dto.pms.resp.accept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
public class AcceptDetailInfo {

    @Schema(description = "申请单号")
    private String billNumber;

    @Schema(description = "部门编码")
    private String deptCode;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "是否附赠品：0.否 1.是")
    private Integer isAdditionalGifts;

    @Schema(description = "包装率单位名称")
    private String packageUnitName;

    @Schema(description = "单内序号")
    private Long insideId;

    @Schema(description = "商品编码")
    private String goodsCode;

    @Schema(description = "商品类型 0商品1附赠商品2附赠赠品")
    private Integer goodsType;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品货号")
    private String goodsNo;

    @Schema(description = "商品sku编码")
    private String skuCode;

    @Schema(description = "商品条码")
    private String barcode;

    @Schema(description = "父商品编码")
    private String pgoodsCode;

    @Schema(description = "父商品名称")
    private String pgoodsName;

    @Schema(description = "父商品货号")
    private String pgoodsNo;

    @Schema(description = "父商品条码")
    private String pbarcode;

    @Schema(description = "商品规格")
    private String goodsSpec;

    @Schema(description = "销售模式 1-自营 2-联营 7-联营管库存 8-租赁 9-生鲜A进A出 10-生鲜A进B出 12-生鲜归集码")
    private Integer saleMod;

    @Schema(description = "商品类别")
    private String categoryCode;

    @Schema(description = "商品分类名称")
    private String categoryName;

    @Schema(description = "小数控制 0-否 1-是")
    private Integer isDecimal;

    @Schema(description = "订货途径 0-采购，1-配送")
    private Integer orderRoute;

    @Schema(description = "直流标志 0-否，1-是")
    private Integer directSign;

    @Schema(description = "直流供应商编码")
    private String distCode;

    @Schema(description = "出货途径 1.采购 2.配送")
    private Integer shippingWay;

    @Schema(description = "出货方式描述")
    private String shippingWaysDesc;

    @Schema(description = "订货数量")
    private BigDecimal amount;

    @Schema(description = "箱数量")
    private BigDecimal boxAmount;

    @Schema(description = "零头数量")
    private BigDecimal remnantAmount;

    @Schema(description = "订单数量")
    private BigDecimal orderAmount;

    @Schema(description = "赠品订单数量")
    private BigDecimal giftOrderAmount;

    @Schema(description = "赠品数量")
    private BigDecimal giftAmount;

    @Schema(description = "单位 存储枚举，需要通过配置转换")
    private String basicUnit;

    @Schema(description = "整箱单位")
    private String wholeUnit;

    @Schema(description = "单位比率，包装率")
    private BigDecimal unitRate;

    @Schema(description = "税率")
    private BigDecimal taxRate;

    @Schema(description = "订货单价 单位元")
    private BigDecimal purchPrice;

    @Schema(description = "订货金额（含税） 单位元")
    private BigDecimal purchMoney;

    @Schema(description = "订货税金 单位元")
    private BigDecimal purchTax;

    @Schema(description = "商品毛重")
    private BigDecimal skuWeight;

    @Schema(description = "单个商品毛重")
    private BigDecimal singleSkuWeight;

    @Schema(description = "商品档案进价 单位元")
    private BigDecimal singlePrice;

    @Schema(description = "零售金额 单位元")
    private BigDecimal singleMoney;

    @Schema(description = "特供价 单位元")
    private BigDecimal specialOfferPrice;

    @Schema(description = "批次号")
    private String batchManageNum;

    @Schema(description = "效期条码")
    private String validityBarcode;

    @Schema(description = "出货方名称")
    private String shipperName;

    @Schema(description = "出货方编码 1.申请类型=采购，供应商编码 2-申请类型=配送，部门档案（部门类型=配送中心 且 状态<>停用）")
    private String shipperCode;

    @Schema(description = "申请备注")
    private String remark;

    @Schema(description = "合同号")
    private String contractNumber;

    @Schema(description = "合同经营方式 (0-经销 1-代销 2-联营 3-租赁)")
    private String manageMode;

    @Schema(description = "履行时间")
    private String performTime;

    @Schema(description = "效期商品标识 1是 0否，默认0")
    private Integer periodFlag;

    @Schema(description = "生产日期")
    private LocalDate productDate;

    @Schema(description = "过期日期")
    private LocalDate expirateDate;

    @Schema(description = "计量属性（0：普通 1：计量 2：称重）")
    private Integer measureProperty;

    @Schema(description = "在途库存")
    private BigDecimal onWayStockNum;

    @Schema(description = "商品库存")
    private BigDecimal stockNum;

    @Schema(description = "管理分类项编码")
    private String assistCategoryClass;

    @Schema(description = "管理分类编码")
    private String assistCategory;

    @Schema(description = "管理分类名称")
    private String assistCategoryName;

    @Schema(description = "促销编码")
    private Long promotionId;

    @Schema(description = "验收退补价格")
    private BigDecimal acceptRefundPrice;

    @Schema(description = "合同商品进价")
    private BigDecimal contractPurchPrice;

    @Schema(description = "合同商品特供价")
    private BigDecimal contractSpecialPrice;

    @Schema(description = "价格类型")
    private Integer priceType;

    @Schema(description = "打印字段--订货无税金额 单位元 (purchMoney - purchTax)")
    private BigDecimal noTaxMoney;

    @Schema(description = "销项税率")
    private BigDecimal outTaxRate;

}