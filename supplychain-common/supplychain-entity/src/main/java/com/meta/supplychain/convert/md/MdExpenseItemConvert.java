package com.meta.supplychain.convert.md;

import com.meta.supplychain.convert.StandardEnumConvert;
import com.meta.supplychain.entity.dto.md.resp.expense.MdExpenseItemResponseDTO;
import com.meta.supplychain.entity.po.md.MdExpenseItemPO;
import com.meta.supplychain.entity.dto.md.view.MdExpenseItemView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MdExpenseItemConvert extends StandardEnumConvert {

    MdExpenseItemConvert INSTANCE = Mappers.getMapper(MdExpenseItemConvert.class);

    MdExpenseItemResponseDTO po2dto(MdExpenseItemPO po);

    
    /**
     * 将MdExpenseItemPO转换为MdExpenseItemView（导出视图）
     * @param po 费用项目PO对象
     * @return 费用项目导出视图
     */
    @Mapping(target = "expenseType", expression = "java(convertToDesc(\"expenseTypeEnum\", po.getExpenseType()))")
    @Mapping(target = "status", expression = "java(convertToDesc(\"validStatusEnum\", po.getStatus()))")
    MdExpenseItemView po2view(MdExpenseItemPO po);
    
    /**
     * 将MdExpenseItemResponseDTO转换为MdExpenseItemView（导出视图）
     * @param dto 费用项目响应DTO
     * @return 费用项目导出视图
     */
    @Mapping(target = "expenseType", expression = "java(convertToDesc(\"expenseTypeEnum\", dto.getExpenseType()))")
    @Mapping(target = "status", expression = "java(convertToDesc(\"mdExpenseStatusEnum\", dto.getStatus()))")
    @Mapping(target = "expenseItemCategory", expression = "java(dto.getExpenseItemCategoryCode() + \"-\" + dto.getExpenseItemCategoryName())")
    @Mapping(target = "createName", expression = "java(dto.getCreateCode() + \"-\" + dto.getCreateName())")
    @Mapping(target = "updateName", expression = "java(dto.getUpdateCode() + \"-\" + dto.getUpdateName())")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "createTime", target = "createTime")
    @Mapping(dateFormat = "yyyy-MM-dd HH:mm:ss", source = "updateTime", target = "updateTime")
    MdExpenseItemView dto2view(MdExpenseItemResponseDTO dto);
} 