package com.meta.supplychain.enums.pms;

import com.meta.supplychain.validation.annotation.EnumMark;
import com.meta.supplychain.validation.interfaces.VerifiableEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@EnumMark(name = "采购类型",code = "PmsBillTypeEnum")
public enum PmsBillTypeEnum implements VerifiableEnum<Integer> {
    ST_PURCH(0, "门店采购"),
    DC_PURCH(1, "配送采购"),
    ;
    private Integer code;

    private String desc;

    public static PmsBillTypeEnum getByCode(Integer code) {
        PmsBillTypeEnum[] values = values();
        for (PmsBillTypeEnum value : values) {
            if(value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
