<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.md.MdDeliveryDockGoodsCategoryMapper">
  <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.md.MdDeliveryDockGoodsCategoryPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="inside_id" jdbcType="INTEGER" property="insideId" />
    <result column="dock_code" jdbcType="VARCHAR" property="dockCode" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="class_code" jdbcType="VARCHAR" property="classCode" />
    <result column="class_name" jdbcType="VARCHAR" property="className" />
    <result column="class_level" jdbcType="TINYINT" property="classLevel" />
    <result column="create_uid" jdbcType="BIGINT" property="createUid" />
    <result column="create_code" jdbcType="VARCHAR" property="createCode" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_uid" jdbcType="BIGINT" property="updateUid" />
    <result column="update_code" jdbcType="VARCHAR" property="updateCode" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, inside_id, dock_code, type, sku_code, sku_name, class_code, class_name, 
    class_level, create_uid, create_code, create_name, create_time, update_uid, update_code, 
    update_name, update_time, del_flag
  </sql>

  <select id="listDockGoods" resultMap="BaseResultMap" parameterType="com.meta.supplychain.entity.dto.md.deliveryappointment.ListDeliveryAppointmentStrategyDTO">
    select <include refid="Base_Column_List" /> from md_delivery_dock_goods_category
    where dock_code in
    <foreach collection="dockList" item="item" open="(" separator="," close=")">
      #{item.dockCode}
    </foreach>
    and (sku_code,type) in
    <foreach collection="skuCodeList" item="item" open="(" separator="," close=")">
      (#{item},1)
    </foreach>

    union

    select <include refid="Base_Column_List" /> from md_delivery_dock_goods_category
    where dock_code in
    <foreach collection="dockList" item="item" open="(" separator="," close=")">
      #{item.dockCode}
    </foreach>
    and (class_code,type) in
    <foreach collection="categoryCodeList" item="item" open="(" separator="," close=")">
      (#{item},2)
    </foreach>

  </select>
</mapper>