<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meta.supplychain.infrastructure.repository.mapper.md.MdDeliveryDockDeptMapper">
  <resultMap id="BaseResultMap" type="com.meta.supplychain.entity.po.md.MdDeliveryDockDeptPO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="inside_id" jdbcType="INTEGER" property="insideId" />
    <result column="dock_code" jdbcType="VARCHAR" property="dockCode" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_type" jdbcType="TINYINT" property="deptType" />
    <result column="create_uid" jdbcType="BIGINT" property="createUid" />
    <result column="create_code" jdbcType="VARCHAR" property="createCode" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_uid" jdbcType="BIGINT" property="updateUid" />
    <result column="update_code" jdbcType="VARCHAR" property="updateCode" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="TINYINT" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, inside_id, dock_code, dept_code, dept_name, dept_type, create_uid, 
    create_code, create_name, create_time, update_uid, update_code, update_name, update_time, 
    del_flag
  </sql>
  <select id="listDockDept" resultMap="BaseResultMap" parameterType="com.meta.supplychain.entity.dto.md.deliveryappointment.ListDeliveryAppointmentStrategyDTO">
    select <include refid="Base_Column_List" /> from md_delivery_dock_dept
    where dock_code in
    <foreach collection="dockList" item="item" open="(" separator="," close=")">
        #{item.dockCode}
    </foreach>
    and (dept_code,dept_type) in
    <foreach collection="deptList" item="item" open="(" separator="," close=")">
      (#{item.deptCode},#{item.deptType})
    </foreach>

  </select>
</mapper>