package com.meta.supplychain.infrastructure.config;

import cn.linkkids.framework.business.gull.language.manager.ErrorMessageManager;
import cn.linkkids.framework.croods.common.logger.Logs;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.*;

/**
 * 多语言上报hook
 */
@Component
public class LangDataUpdateHook implements ApplicationRunner {

    @Resource
    private ErrorMessageManager errorMessageManager;

    @Override
    public void run(ApplicationArguments args) throws ExecutionException, InterruptedException, TimeoutException {
        ExecutorService tempThreadPool = Executors.newCachedThreadPool();
        CompletableFuture<Object> updateResult = null;
        try {
            updateResult = CompletableFuture.runAsync(() -> {
                Logs.info("开始上报多语言错误信息");
                errorMessageManager.pushErrorMessage(true);
                Logs.info("上报多语言错误信息结束");
            }, tempThreadPool).handleAsync((r, throwable) -> {
                if (throwable != null) {
                    Logs.error("错误信息上报处理失败：", throwable);
                }
                return null;
            }, tempThreadPool);
        }finally {
            if(updateResult != null) {
                updateResult.get(1, TimeUnit.MINUTES);
            }
            tempThreadPool.shutdown();
        }
    }
}
