package com.meta.supplychain.infrastructure.repository.service.impl.pms;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.context.ApplicationContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPruchDeliveryRefReq;
import com.meta.supplychain.entity.dto.pms.resp.demand.PmsDemandPruchDeliveryRefResp;
import com.meta.supplychain.entity.dto.pms.resp.PmsDemandSourceDetailDTO;
import com.meta.supplychain.entity.po.pms.PmsDemandDetailSourceRefPO;
import com.meta.supplychain.entity.po.pms.PmsDemandPruchDeliveryRefPO;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsDemandPruchDeliveryRefMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandDetailSourceRefRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandPruchDeliveryRefRepositoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/24 14:38
 **/
@Service
public class PmsDemandPruchDeliveryRefRepositoryServiceImpl extends ServiceImpl<PmsDemandPruchDeliveryRefMapper, PmsDemandPruchDeliveryRefPO>
        implements IPmsDemandPruchDeliveryRefRepositoryService {

    @Autowired
    private PmsDemandPruchDeliveryRefMapper pmsDemandPruchDeliveryRefMapper;

    private ApplicationContext applicationContext;

    @Override
    public PmsDemandPruchDeliveryRefMapper getPmsDemandPruchDeliveryRefMapper() {
        return pmsDemandPruchDeliveryRefMapper;
    }

    private IPmsDemandDetailSourceRefRepositoryService getIPmsDemandDetailSourceRefRepositoryService(){
        IPmsDemandDetailSourceRefRepositoryService pmsDemandDetailSourceRefRepositoryService = ApplicationContextHolder.get(IPmsDemandDetailSourceRefRepositoryService.class);
        return pmsDemandDetailSourceRefRepositoryService;
    }

    @Override
    public List<PmsDemandPruchDeliveryRefResp> listPmsDemandPruchDeliveryByDemandBillNo(PmsDemandPruchDeliveryRefReq pmsDemandPruchDeliveryRefReq) {
        LambdaQueryWrapper<PmsDemandPruchDeliveryRefPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(pmsDemandPruchDeliveryRefReq.getBillNo()),PmsDemandPruchDeliveryRefPO::getBillNo,pmsDemandPruchDeliveryRefReq.getBillNo());
        queryWrapper.in(CollectionUtils.isNotEmpty(pmsDemandPruchDeliveryRefReq.getTypeList()),PmsDemandPruchDeliveryRefPO::getType,pmsDemandPruchDeliveryRefReq.getTypeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(pmsDemandPruchDeliveryRefReq.getRefBillNoList()),PmsDemandPruchDeliveryRefPO::getRefBillNo,pmsDemandPruchDeliveryRefReq.getRefBillNoList());

        List<PmsDemandPruchDeliveryRefResp> list = CglibCopier.copy(pmsDemandPruchDeliveryRefMapper.selectList(queryWrapper), PmsDemandPruchDeliveryRefResp.class);

        LambdaQueryWrapper<PmsDemandDetailSourceRefPO> detailSourceRefQueryWrapper = new LambdaQueryWrapper<>();
        detailSourceRefQueryWrapper.eq(PmsDemandDetailSourceRefPO::getDemandBillNo,pmsDemandPruchDeliveryRefReq.getBillNo());
        detailSourceRefQueryWrapper.eq(PmsDemandDetailSourceRefPO::getType,1);

        List<PmsDemandDetailSourceRefPO> pmsDemandDetailSourceRefPOS = getIPmsDemandDetailSourceRefRepositoryService().getPmsDemandDetailSourceRefMapper().selectList(detailSourceRefQueryWrapper);
        Map<String,PmsDemandDetailSourceRefPO> detailSourceRefMap = new HashMap<>();
        for (PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO : pmsDemandDetailSourceRefPOS) {
            String key = pmsDemandDetailSourceRefPO.getApplyBillNo() + "_" + pmsDemandDetailSourceRefPO.getSrcInsideId();
            detailSourceRefMap.put(key,pmsDemandDetailSourceRefPO);
        }

        List<PmsDemandPruchDeliveryRefResp> resultList = new ArrayList<>();
        for (PmsDemandPruchDeliveryRefResp pmsDemandPruchDeliveryRefResp : list) {
            String key = pmsDemandPruchDeliveryRefResp.getApplyBillNo() + "_" + pmsDemandPruchDeliveryRefResp.getApplyInsideId();
            if(detailSourceRefMap.containsKey(key)){
                PmsDemandDetailSourceRefPO pmsDemandDetailSourceRefPO = detailSourceRefMap.get(key);
                pmsDemandPruchDeliveryRefResp.setSkuType(pmsDemandDetailSourceRefPO.getGoodsType());
                pmsDemandPruchDeliveryRefResp.setSrcDemandQty(pmsDemandDetailSourceRefPO.getSrcDemandQty());
                pmsDemandPruchDeliveryRefResp.setSrcOrderPrice(pmsDemandDetailSourceRefPO.getSrcOrderPrice());
                resultList.add(pmsDemandPruchDeliveryRefResp);
            }
        }

        return resultList;
    }

    @Override
    public PmsDemandSourceDetailDTO getDemandSourceDetail(String refBillNo, Long refInsideId) {
        return pmsDemandPruchDeliveryRefMapper.selectDemandSourceDetail(refBillNo, refInsideId);
    }
}
