package com.meta.supplychain.util;

import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.nacos.common.utils.StringUtils;
import com.meta.supplychain.entity.dto.bds.resp.SupplierByCodeResp;
import com.meta.supplychain.infrastructure.feign.BaseDataSystemFeignClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 门店基础数据工具类
 */
@Component
public class BaseStoreUtil {
    @Resource
    BaseDataSystemFeignClient baseDataSystemFeignClient;

    @Resource
    RedisTemplate<String, String> redisTemplate;

    public static final String SUPPLIER_INFO = "_SUPPLIER_INFO_";

    /**
     * 获取供应商信息 同时缓存 6小时
     * @return
     */
    public SupplierByCodeResp getSupplierCache(String supplierCode) {
        SupplierByCodeResp resp = SupplierByCodeResp.builder().build();
        if(StringUtils.isEmpty(supplierCode)){
            return resp;
        }
        final String key = TenantContext.get()+ SUPPLIER_INFO + supplierCode;
        String cache = redisTemplate.opsForValue().get(key);
        if (StringUtils.hasText(cache)) {
            resp = Jsons.toBean(cache, SupplierByCodeResp.class);
        }else {
            resp = baseDataSystemFeignClient.querySupplierByCode(supplierCode);
            if(!Objects.isNull(resp)){
                String wholeAddress = resp.getProvinceName()+resp.getCityName()+resp.getDistrictName()+resp.getStreetName()+resp.getAddress();
                resp.setWholeAddress(wholeAddress.replace("null",""));
            }
            redisTemplate.opsForValue().set(key, Jsons.toJson(resp),
                    6, TimeUnit.HOURS);
        }

        return resp;
    }

}
