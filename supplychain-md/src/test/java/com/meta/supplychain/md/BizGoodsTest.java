package com.meta.supplychain.md;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.ageiport.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizGoodsRuleService;
import com.meta.supplychain.entity.dto.md.component.goodsrule.DemandBatchRecordGoodsDTO;
import com.meta.supplychain.entity.dto.md.component.goodsrule.GeneratePurchBatchDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.BatchApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyDetail4DemandDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryDetailByInsideIdReq;
import com.meta.supplychain.entity.dto.pms.resp.ApplyBillResp;
import com.meta.supplychain.entity.po.pms.PmsApplyBillDetailPO;
import com.meta.supplychain.enums.OrderAttributeEnum;
import com.meta.supplychain.enums.pms.ShippingWayEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsApplyBillDetailRepositoryService;
import org.apache.commons.lang.ObjectUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description:
 * @date 2024年08月26日 10:54
 */
@SpringBootTest
public class BizGoodsTest {
    @Autowired
    private ISupplychainBizGoodsRuleService supplychainBizGoodsRuleService;


    @Test
    public void testGenerateProcurementBatch(){
        TenantContext.set("153658");
        GeneratePurchBatchDTO generatePurchBatchDTO = new GeneratePurchBatchDTO();
        List<DemandBatchRecordGoodsDTO> demandBatchRecordGoodsList = new ArrayList<>();
        DemandBatchRecordGoodsDTO demandBatchRecordGoodsDTO = new DemandBatchRecordGoodsDTO();
        demandBatchRecordGoodsDTO.setDeptCode("015927");
        demandBatchRecordGoodsDTO.setSkuCode("543298");
        demandBatchRecordGoodsDTO.setCategoryCode("009001001");
        demandBatchRecordGoodsList.add(demandBatchRecordGoodsDTO);
//        demandBatchRecordGoodsDTO.setDeptCode("015929");
//        demandBatchRecordGoodsDTO.setSkuCode("110748");
//        demandBatchRecordGoodsDTO.setCategoryCode("092001001");
//        demandBatchRecordGoodsList.add(demandBatchRecordGoodsDTO);
//        demandBatchRecordGoodsDTO.setDeptCode("015929");
//        demandBatchRecordGoodsDTO.setSkuCode("542910");
//        demandBatchRecordGoodsDTO.setCategoryCode("094001001");
//        demandBatchRecordGoodsList.add(demandBatchRecordGoodsDTO);
//
//        demandBatchRecordGoodsDTO = new DemandBatchRecordGoodsDTO();
//        demandBatchRecordGoodsDTO.setDeptCode("001426");
//        demandBatchRecordGoodsDTO.setSkuCode("543229");
//        demandBatchRecordGoodsDTO.setCategoryCode("009001001");
//        demandBatchRecordGoodsList.add(demandBatchRecordGoodsDTO);

        generatePurchBatchDTO.setDemandBatchRecordGoodsList(demandBatchRecordGoodsList);
        generatePurchBatchDTO.setDeliverDays(3);
        generatePurchBatchDTO.setDate(new Date());

        supplychainBizGoodsRuleService.generateProcurementBatch(generatePurchBatchDTO);
        System.out.println("testGenerateProcurementBatch" + JSON.toJSONString(generatePurchBatchDTO));
    }


}
