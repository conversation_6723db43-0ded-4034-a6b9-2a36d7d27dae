package com.meta.supplychain.demand.purch.processor.export;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.beans.CglibCopier;
import com.alibaba.ageiport.common.utils.CollectionUtils;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.pms.PurchaseBillConvert;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchBillService;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchaseOrderDomainService;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchaseBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailSumResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailWithDirectResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillWithDetailAllResp;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseDeliverySkuResp;
import com.meta.supplychain.entity.dto.pms.view.PmsPurchaseDirectScmExportView;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.common.SkuTypeEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseOrderRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购订单直流明细列表导出-SCM
 */
@ExportSpecification(code = "PmsPurchaseDirectScmProcessor", name = "采购订单直流明细列表导出-SCM", executeType = ExecuteType.CLUSTER)
public class PmsPurchaseDirectScmProcessor implements ExportProcessor<QueryPurchaseBillReq, PurchaseBillDetailWithDirectResp, PmsPurchaseDirectScmExportView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryPurchaseBillReq req) throws BizException {
        PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseDetailRepositoryService.class);
        PmsPurchBillService pmsPurchBillService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillService.class);
        //条件预处理 数据权限
        pmsPurchBillService.doQueryCondition(req);
        return pmsPurchaseDetailRepositoryService.getSumBillCount(req);
    }

    @Override
    public List<PurchaseBillDetailWithDirectResp> queryData(BizUser bizUser, QueryPurchaseBillReq req, BizExportPage bizExportPage) throws BizException {
        PmsPurchaseOrderDomainService pmsPurchaseDetailRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseOrderDomainService.class);
        PmsPurchBillService pmsPurchBillService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillService.class);
        //条件预处理 数据权限
        pmsPurchBillService.doQueryCondition(req);
        req.setCurrent(Long.valueOf(bizExportPage.getNo()));
        req.setPageSize(Long.valueOf(bizExportPage.getSize()));
        PageResult<PurchaseBillDetailWithDirectResp> detailList = pmsPurchaseDetailRepositoryService.queryPurchDirectList(req);
        return detailList.getRows();
    }

    @Override
    public List<PmsPurchaseDirectScmExportView> convert(BizUser bizUser, QueryPurchaseBillReq req, List<PurchaseBillDetailWithDirectResp> list) throws BizException {
        List<PmsPurchaseDirectScmExportView> purchaseDetailExportViews = new ArrayList<>();
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_PURCHASE_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        for (PurchaseBillDetailWithDirectResp directResp : list) {
            PmsPurchaseDirectScmExportView purchaseDetailExportView = PurchaseBillConvert.INSTANCE.convertDirectExportViewScm(directResp);
            purchaseDetailExportView.setSkuTypeDesc(SkuTypeEnum.getByCode(directResp.getSkuType()).getDesc());
            purchaseDetailExportView.setBillDirectionDesc(PmsBillDirectionEnum.getByCode(directResp.getBillDirection()).getPurchDesc());
            purchaseDetailExportView.setDirectSignDesc(DirectSignEnum.getByCode(directResp.getDirectSign()).getDesc());
            purchaseDetailExportView.setBillTypeDesc(PmsBillTypeEnum.getByCode(directResp.getBillType()).getDesc());
            purchaseDetailExportView.setSendModeDesc(PmsSendModeEnum.getByCode(directResp.getSendMode()).getDesc());
            purchaseDetailExportView.setReadSignDesc(YesOrNoEnum.NO.getCode().equals(directResp.getReadSign())?"未读":"已读");
            purchaseDetailExportView.setShipSignDesc(YesOrNoEnum.NO.getCode().equals(directResp.getShipSign())?"未发货":"已发货");
            purchaseDetailExportView.setStatusDesc(PmsPurchaseBillStatusEnum.getEnumByCode(directResp.getStatus()).getDesc());
            purchaseDetailExportView.setConfirmSignDesc(PmsConfirmDeliverSignEnum.getByCode(directResp.getConfirmSign()).getDesc());
            if(StringUtils.hasText(directResp.getAuditName())){
                purchaseDetailExportView.setAuditName(directResp.getAuditCode()+"_"+directResp.getAuditName());
            }
            if (!showPriceFlag) {
                purchaseDetailExportView.setPurchPrice(SysConstants.ENCRYPT);
                purchaseDetailExportView.setPurchMoney(SysConstants.ENCRYPT);
                purchaseDetailExportView.setPurchTax(SysConstants.ENCRYPT);
                purchaseDetailExportView.setTotalTax(SysConstants.ENCRYPT);
                purchaseDetailExportView.setTotalTaxMoney(SysConstants.ENCRYPT);
                purchaseDetailExportView.setFulfilMoney(SysConstants.ENCRYPT);
                purchaseDetailExportView.setDeliveryPrice(SysConstants.ENCRYPT);
                purchaseDetailExportView.setDeliveryTaxMoney(SysConstants.ENCRYPT);
                purchaseDetailExportView.setDeliveryTax(SysConstants.ENCRYPT);
            }
            purchaseDetailExportViews.add(purchaseDetailExportView);
        }
        return purchaseDetailExportViews;
    }
}
