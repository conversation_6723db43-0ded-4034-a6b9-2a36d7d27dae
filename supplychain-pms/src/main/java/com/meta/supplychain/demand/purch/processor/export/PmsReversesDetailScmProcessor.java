package com.meta.supplychain.demand.purch.processor.export;

import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.pms.PurchaseBillConvert;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchBillService;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchaseBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillDetailSumResp;
import com.meta.supplychain.entity.dto.pms.view.PmsReversesDetaiScmExportView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.common.SkuTypeEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseDetailRepositoryService;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 采退订单明细汇总列表导出-SCM
 */
@ExportSpecification(code = "PmsReversesDetailScmProcessor", name = "采退订单明细汇总列表导出-SCM", executeType = ExecuteType.CLUSTER)
public class PmsReversesDetailScmProcessor implements ExportProcessor<QueryPurchaseBillReq, PurchaseBillDetailSumResp, PmsReversesDetaiScmExportView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryPurchaseBillReq req) throws BizException {
        PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseDetailRepositoryService.class);
        PmsPurchBillService pmsPurchBillService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillService.class);
        //条件预处理 数据权限
        pmsPurchBillService.doQueryCondition(req);
        return pmsPurchaseDetailRepositoryService.getSumBillCount(req);
    }

    @Override
    public List<PurchaseBillDetailSumResp> queryData(BizUser bizUser, QueryPurchaseBillReq req, BizExportPage bizExportPage) throws BizException {
        PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseDetailRepositoryService.class);
        PmsPurchBillService pmsPurchBillService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillService.class);
        //条件预处理 数据权限
        pmsPurchBillService.doQueryCondition(req);
        Page<PurchaseBillDetailSumResp> page = new Page<>(Long.valueOf(bizExportPage.getNo()), Long.valueOf(bizExportPage.getSize()));
        IPage<PurchaseBillDetailSumResp> detailList = pmsPurchaseDetailRepositoryService.queryPurchDetailList(req,page);
        return detailList.getRecords();
    }

    @Override
    public List<PmsReversesDetaiScmExportView> convert(BizUser bizUser, QueryPurchaseBillReq req, List<PurchaseBillDetailSumResp> list) throws BizException {
        List<PmsReversesDetaiScmExportView> purchaseDetailExportViews = new ArrayList<>();
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_PURCHASE_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        for (PurchaseBillDetailSumResp detailSum : list) {
            PmsReversesDetaiScmExportView purchaseDetailExportView = PurchaseBillConvert.INSTANCE.convertReversesDetailExportViewScm(detailSum);
            purchaseDetailExportView.setSkuTypeDesc(SkuTypeEnum.getByCode(detailSum.getSkuType()).getDesc());
            purchaseDetailExportView.setBillDirectionDesc(PmsBillDirectionEnum.getByCode(detailSum.getBillDirection()).getPurchDesc());
            purchaseDetailExportView.setDirectSignDesc(DirectSignEnum.getByCode(detailSum.getDirectSign()).getDesc());
            purchaseDetailExportView.setBillTypeDesc(PmsBillTypeEnum.getByCode(detailSum.getBillType()).getDesc());
            purchaseDetailExportView.setSendModeDesc(PmsSendModeEnum.getByCode(detailSum.getSendMode()).getDesc());
            purchaseDetailExportView.setReadSignDesc(YesOrNoEnum.NO.getCode().equals(detailSum.getReadSign())?"未读":"已读");
            purchaseDetailExportView.setStatusDesc(PmsPurchaseBillStatusEnum.getEnumByCode(detailSum.getStatus()).getDesc());
            purchaseDetailExportView.setConfirmSignDesc(PmsConfirmDeliverSignEnum.getByCode(detailSum.getConfirmSign()).getDesc());
            if(StringUtils.hasText(detailSum.getAuditName())){
                purchaseDetailExportView.setAuditName(detailSum.getAuditCode()+"_"+detailSum.getAuditName());
            }
            if (!showPriceFlag) {
                purchaseDetailExportView.setPurchPrice(SysConstants.ENCRYPT);
                purchaseDetailExportView.setPurchMoney(SysConstants.ENCRYPT);
                purchaseDetailExportView.setPurchTax(SysConstants.ENCRYPT);
                purchaseDetailExportView.setTotalTax(SysConstants.ENCRYPT);
                purchaseDetailExportView.setTotalTaxMoney(SysConstants.ENCRYPT);
                purchaseDetailExportView.setFulfilMoney(SysConstants.ENCRYPT);
            }
            purchaseDetailExportViews.add(purchaseDetailExportView);
        }


        return purchaseDetailExportViews;
    }
}
