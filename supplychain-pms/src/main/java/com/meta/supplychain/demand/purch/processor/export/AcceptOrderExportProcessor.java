package com.meta.supplychain.demand.purch.processor.export;


import cn.hutool.core.bean.BeanUtil;
import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.demand.purch.application.intf.PmsAcceptApplicationService;
import com.meta.supplychain.entity.dto.pms.req.accept.QueryAcceptDTO;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillResp;
import com.meta.supplychain.entity.dto.pms.resp.accept.AcceptBillView;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.PmsAcceptBillStateEnum;
import com.meta.supplychain.enums.pms.PmsBillDirectionEnum;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.List;

@ExportSpecification(code = "AcceptOrderExportProcessor", name = "验收单列表导出", executeType = ExecuteType.CLUSTER)
public class AcceptOrderExportProcessor implements ExportProcessor<QueryAcceptDTO, AcceptBillResp, AcceptBillView> {

    private PmsAcceptApplicationService pmsAcceptApplicationService(){
        return SpringContextUtil.getApplicationContext().getBean(PmsAcceptApplicationService.class);
    }
    @Override
    public Integer totalCount(BizUser bizUser, QueryAcceptDTO queryAcceptDTO) throws BizException {
        return Math.toIntExact(pmsAcceptApplicationService().queryAcceptList(queryAcceptDTO).getData().getTotal());
    }

    @Override
    public List<AcceptBillResp> queryData(BizUser bizUser, QueryAcceptDTO queryAcceptDTO, BizExportPage bizExportPage) throws BizException {
        queryAcceptDTO.setCurrent(bizExportPage.getNo().longValue());
        queryAcceptDTO.setPageSize(bizExportPage.getSize().longValue());
        Result<PageResult<AcceptBillResp>> pageResultResult = pmsAcceptApplicationService().queryAcceptList(queryAcceptDTO);
        return pageResultResult.getData().getRows();
    }

    @Override
    public List<AcceptBillView> convert(BizUser bizUser, QueryAcceptDTO queryAcceptDTO, List<AcceptBillResp> list) throws BizException {
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        boolean showPriceFlag = false;
        if(PmsBillDirectionEnum.NORMAL.getCode().equals(queryAcceptDTO.getBillDirection())){
            showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_CGYS_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        }else {
            showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_CGTH_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        }
        Logs.info("用户{}是否拥有查看价格权限{}",bizUser.getBizUserId(),showPriceFlag);
        boolean finalShowPriceFlag = showPriceFlag;
        return Lists.transform(list, input -> {
            AcceptBillView acceptBillView = new AcceptBillView();
            BeanUtil.copyProperties(input,acceptBillView);
            acceptBillView.setStatusDesc(PmsAcceptBillStateEnum.getNameByCode(input.getStatus()));
            acceptBillView.setCreator(input.getCreateCode()+input.getCreateName());
            acceptBillView.setUpdater(input.getUpdateCode()+input.getUpdateName());
            acceptBillView.setAuditMan(input.getAuditManCode()+input.getAuditManName());
            acceptBillView.setSubmitMan(input.getSubmitManCode()+input.getSubmitManName());
            acceptBillView.setReversalFlagDesc(YesOrNoEnum.getDescByCode(input.getReversalFlag()));
            if(YesOrNoEnum.YES.getCode().equals(input.getReversalBillSign())){
                acceptBillView.setTotalQty(formatMoney(input.getTotalQty().multiply(BigDecimal.valueOf(-1))));
                acceptBillView.setTotalMoney(formatMoney(input.getTotalMoney().multiply(BigDecimal.valueOf(-1))));
            }else {
                acceptBillView.setTotalQty(formatMoney(input.getTotalQty()));
                acceptBillView.setTotalMoney(formatMoney(input.getTotalMoney()));
            }
            if(!finalShowPriceFlag){
                acceptBillView.setTotalMoney(SysConstants.ENCRYPT);
            }
            return acceptBillView;
        });
    }

    public static String formatMoney(BigDecimal money) {
        if (money == null) {
            return "0";
        }
        // 去除末尾零
        BigDecimal stripped = money.stripTrailingZeros();
        // 如果是整数（scale == 0），直接返回整数形式
        if (stripped.scale() <= 0) {
            return stripped.toBigIntegerExact().toString();
        }
        // 设置 scale 至少为 2
        int scale = Math.max(stripped.scale(), 2);
        BigDecimal rounded = stripped.setScale(scale, RoundingMode.UNNECESSARY);
        // 构建格式化模式
        StringBuilder pattern = new StringBuilder("#.");
        for (int i = 0; i < scale; i++) {
            pattern.append('0');
        }
        DecimalFormat df = new DecimalFormat(pattern.toString());
        return df.format(rounded);
    }

    public static void main(String[] args) {
        System.out.println(formatMoney(new BigDecimal("30.000000"))); // 30
        System.out.println(formatMoney(new BigDecimal("0.000000")));   // 0
        System.out.println(formatMoney(new BigDecimal("1.200000")));  // 1.20
        System.out.println(formatMoney(new BigDecimal("1.234500")));  // 1.2345
        System.out.println(formatMoney(new BigDecimal("1.230000")));  // 1.23
        System.out.println(formatMoney(new BigDecimal("1.200001")));  // 1.200001
    }
}
