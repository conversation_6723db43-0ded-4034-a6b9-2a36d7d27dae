package com.meta.supplychain.demand.purch.processor.export;

import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.pms.PurchaseBillConvert;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchBillService;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchaseBillReq;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchaseBillResp;
import com.meta.supplychain.entity.dto.pms.view.PmsPurchaseOrderExportView;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseOrderRepositoryService;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单列表导出
 */
@ExportSpecification(code = "PmsPurchaseOrderProcessor", name = "采购订单列表导出", executeType = ExecuteType.CLUSTER)
public class PmsPurchaseOrderProcessor implements ExportProcessor<QueryPurchaseBillReq, PurchaseBillResp, PmsPurchaseOrderExportView> {
    @Override
    public Integer totalCount(BizUser bizUser, QueryPurchaseBillReq req) throws BizException {
        PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseOrderRepositoryService.class);
        PmsPurchBillService pmsPurchBillService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillService.class);
        //条件预处理 数据权限
        pmsPurchBillService.doQueryCondition(req);
        return pmsPurchaseOrderRepositoryService.getBillCount(req);
    }

    @Override
    public List<PurchaseBillResp> queryData(BizUser bizUser, QueryPurchaseBillReq req, BizExportPage bizExportPage) throws BizException {
        PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsPurchaseOrderRepositoryService.class);
        PmsPurchBillService pmsPurchBillService = SpringContextUtil.getApplicationContext().getBean(PmsPurchBillService.class);
        //条件预处理 数据权限
        pmsPurchBillService.doQueryCondition(req);
        Page<PmsPurchaseOrderPO> page = new Page<>(Long.valueOf(bizExportPage.getNo()), Long.valueOf(bizExportPage.getSize()));
        IPage<PmsPurchaseOrderPO> purchBill = pmsPurchaseOrderRepositoryService.queryPurchList(req,page);
        IPage<PurchaseBillResp> list = purchBill.convert(PurchaseBillConvert.INSTANCE::convertPo2Vo);
        return list.getRecords();
    }

    @Override
    public List<PmsPurchaseOrderExportView> convert(BizUser bizUser, QueryPurchaseBillReq req, List<PurchaseBillResp> list) throws BizException {
        List<PmsPurchaseOrderExportView> purchaseOrderExportViews = new ArrayList<>();
        String supplierCode = req.getOperatorInfo().getBusinessCode();
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_PURCHASE_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        for (PurchaseBillResp purchaseBill : list) {
            PmsPurchaseOrderExportView purchaseOrderExportView = PurchaseBillConvert.INSTANCE.convertExportView(purchaseBill);
            purchaseOrderExportView.setCreateName(purchaseBill.getCreateCode()+"_"+purchaseBill.getCreateName());
            purchaseOrderExportView.setBillDirectionDesc(PmsBillDirectionEnum.getByCode(purchaseBill.getBillDirection()).getPurchDesc());
            purchaseOrderExportView.setDirectSignDesc(DirectSignEnum.getByCode(purchaseBill.getDirectSign()).getDesc());
            purchaseOrderExportView.setBillTypeDesc(PmsBillTypeEnum.getByCode(purchaseBill.getBillType()).getDesc());
            purchaseOrderExportView.setSendModeDesc(PmsSendModeEnum.getByCode(purchaseBill.getSendMode()).getDesc());
            purchaseOrderExportView.setBillSourceDesc(PmsPurchaseOrderSourceEnum.getByCode(purchaseBill.getBillSource()).getDesc());
            purchaseOrderExportView.setPrintCount(StringUtils.hasText(supplierCode)?purchaseBill.getSuppPrintCount():purchaseBill.getPrintCount());
            purchaseOrderExportView.setReadSignDesc(YesOrNoEnum.NO.getCode().equals(purchaseBill.getReadSign())?"未读":"已读");
            purchaseOrderExportView.setConfirmDeliverSignDesc(PmsConfirmDeliverSignEnum.getByCode(purchaseBill.getConfirmDeliverSign()).getDesc());
            purchaseOrderExportView.setShipSignDesc(YesOrNoEnum.NO.getCode().equals(purchaseBill.getShipSign())?"未发货":"已发货");
            purchaseOrderExportView.setStatusDesc(PmsPurchaseBillStatusEnum.getEnumByCode(purchaseBill.getStatus()).getDesc());
            purchaseOrderExportView.setConfirmDeliverSignDesc(PmsConfirmDeliverSignEnum.getByCode(purchaseBill.getConfirmDeliverSign()).getDesc());
            if(StringUtils.hasText(purchaseBill.getAuditName())){
                purchaseOrderExportView.setAuditName(purchaseBill.getAuditCode()+"_"+purchaseBill.getAuditName());
            }
            if(StringUtils.hasText(purchaseBill.getCancelManName())){
                purchaseOrderExportView.setCancelManName(purchaseBill.getCancelManCode()+"_"+purchaseBill.getCancelManName());
            }

            if (!showPriceFlag) {
                purchaseOrderExportView.setTotalTaxMoney(SysConstants.ENCRYPT);
                purchaseOrderExportView.setTotalTax(SysConstants.ENCRYPT);
            }
            purchaseOrderExportViews.add(purchaseOrderExportView);
        }

        return purchaseOrderExportViews;
    }
}
