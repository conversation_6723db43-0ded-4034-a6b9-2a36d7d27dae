package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.exception.BizExceptions;
import com.alibaba.nacos.common.utils.StringUtils;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.convert.pms.AccountBillConvert;
import com.meta.supplychain.demand.purch.domain.intf.PmsAccountBillDomainService;
import com.meta.supplychain.entity.dto.pms.req.account.AccountBillCreateReq;
import com.meta.supplychain.entity.po.pms.PmsAccountBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsAccountBillPO;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAccountBillRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsAccountDetailRepositoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

@Service
@RequiredArgsConstructor
public class PmsAccountBillDomainServiceImpl implements PmsAccountBillDomainService {
    @Autowired
    private PmsAccountBillRepositoryService pmsAccountBillRepositoryService;

    @Autowired
    private PmsAccountDetailRepositoryService pmsAccountDetailRepositoryService;

    private final ISupplychainControlEngineService supplychainControlEngineService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PmsAccountBillPO saveAccountBill(AccountBillCreateReq req){
        //查询单号是否已生成过账单，存在直接返回
        if (!StringUtils.hasText(req.getSrcBillNo())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P001);
        }
        PmsAccountBillPO accountBill = pmsAccountBillRepositoryService.getAccountByBillNo(req.getSrcBillNo());
        if (Objects.nonNull(accountBill)) {
           return accountBill;
        }

        //生成过账单号
        String accBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ACCOUNT_BILL,req.getDcCode());
        PmsAccountBillPO accountPO = AccountBillConvert.INSTANCE.convertToAccountBillPO(req);
        req.setBillNo(accBillNo);
        accountPO.setBillNo(accBillNo);

        //明细
        List<PmsAccountBillDetailPO> detailPOList = AccountBillConvert.INSTANCE.convertToAccountDetailPOList(req.getDetailList());
        rehandleDetail(accountPO,detailPOList);

        //落DB
        pmsAccountBillRepositoryService.save(accountPO);
        pmsAccountDetailRepositoryService.saveBatch(detailPOList);
        return accountPO;
    }

    /**
     * 重新处理过账单明细
     */
    public void rehandleDetail(PmsAccountBillPO accountBill, List<PmsAccountBillDetailPO> detailList){
        AtomicLong insideId = new AtomicLong(1);
        detailList.forEach(item->{
            item.setBillNo(accountBill.getBillNo());
            item.setSrcBillNo(accountBill.getSrcBillNo());
            item.setDcCode(accountBill.getDcCode());
            item.setDcName(accountBill.getDcName());
            item.setInsideId(insideId.getAndAdd(1));
        });
    }

}
