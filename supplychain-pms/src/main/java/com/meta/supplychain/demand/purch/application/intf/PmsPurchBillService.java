package com.meta.supplychain.demand.purch.application.intf;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import cn.linkkids.framework.croods.common.exception.BizException;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.google.common.collect.Lists;
import com.meta.supplychain.common.component.service.impl.commonbiz.CommonFranchiseService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonStockService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchasePlanDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.resp.SupplierByCodeResp;
import com.meta.supplychain.entity.dto.franline.req.FranLineAdjustReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPruchDeliveryRefReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchasePlanBillNumChangeReq;
import com.meta.supplychain.entity.dto.pms.req.purch.QueryPurchaseBillReq;
import com.meta.supplychain.entity.dto.pms.resp.demand.PmsDemandPruchDeliveryRefResp;
import com.meta.supplychain.entity.dto.stock.StkTaskIReleaseExecuteDto;
import com.meta.supplychain.entity.dto.stock.StkTaskItemExecuteDto;
import com.meta.supplychain.entity.dto.stock.req.BatchRecordReq;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsPurchaseOrderPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.enums.*;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.common.IBillAdjustLogRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandPruchDeliveryRefRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchaseOrderRepositoryService;
import com.meta.supplychain.util.BaseStoreUtil;
import com.meta.supplychain.util.RedisUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PmsPurchBillService {
    @Autowired
    private PmsPurchaseOrderRepositoryService pmsPurchaseOrderRepositoryService;

    @Autowired
    private PmsPurchaseDetailRepositoryService pmsPurchaseDetailRepositoryService;

    @Autowired
    private PmsPurchasePlanDomainService pmsPurchasePlanDomainService;

    @Autowired
    private IBillAdjustLogRepositoryService billAdjustLogRepositoryService;

    @Autowired
    private IPmsDemandPruchDeliveryRefRepositoryService pmsDemandPruchDeliveryRefRepositoryService;

    @Autowired
    CommonFranchiseService commonFranchiseService;

    @Autowired
    private ICommonStockService iCommonStockService;

    @Autowired
    private BaseStoreUtil baseStoreUtil;

    @Resource
    private RedisUtil redisUtil;

    private static final Long EXPIRE_TIME = 5 * 60 * 1000L;

    /**
     * 查询条件预处理
     * @param query
     */
    public void doQueryCondition(QueryPurchaseBillReq query){
        String supplierCode = query.getOperatorInfo().getBusinessCode();
        if(StringUtils.hasText(supplierCode)){
            query.setSupplierCode(Lists.newArrayList(supplierCode));
        }
        //数据权限
        handleDataScope(query);
        if (CollectionUtils.isNotEmpty(query.getSkuCodeList()) || CollectionUtils.isNotEmpty(query.getCategoryCodeList()) || CollectionUtils.isNotEmpty(query.getBrandCodeList())) {
            query.setWithSku(true);
        }
    }

    /**
     * 处理数据权限
     *
     * @param
     */
    public void handleDataScope(QueryPurchaseBillReq req) {
        OpInfo operatorInfo = req.getOperatorInfo();
        if(operatorInfo.getOriginDeptFlag()) {
            return;
        }
        //分管部门list
        List<String> manageDeptCodeList = operatorInfo.getManageDeptCodeList();
        if(CollectionUtils.isEmpty(operatorInfo.getManageDeptCodeList())) {
            return;
        }
        if(CollectionUtils.isEmpty(req.getDeptCode()) || Objects.isNull(req.getDeptCode())){
            req.setDeptCode(manageDeptCodeList);
        }else {
            //剔除非分管部门的数据
            req.getDeptCode().stream().filter(item-> !manageDeptCodeList.contains(item))
                    .peek(code -> req.getDeptCode().remove(code)
                    ).collect(Collectors.toList());
        }
    }

    /**
     * 处理审核采购订单操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void doAudit(PmsPurchaseOrderPO purchasePO, List<PmsPurchaseBillDetailPO> purchBillDetailList,OpInfo operatorInfo,Boolean isBatchAudit) {
        if(isBatchAudit){
            pmsPurchaseOrderRepositoryService.updateById(purchasePO);
        }
        //需求暂时未提，审核 退供需要校验供应商往来余额
        //1.执行调用采购计划单，则更新剩余可采数量-扣减
        if(StringUtils.hasText(purchasePO.getPlanBillNo()) && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())){
            assemblePlanBill(purchasePO,purchBillDetailList,2);
        }

        FranLineAdjustReq adjustReq = commonFranchiseService.buildReq(purchasePO.getBillNo(),purchasePO.getDeptCode(),purchasePO.getTotalTaxMoney(), FranLineTypeEnum.PURCHASE.getCode(),operatorInfo);
        //2.调整加盟额度 (配转采和直流的采购订单不处理加盟额度)
        SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchasePO.getSupplierCode());
        Boolean isJM = DeptOperateModeEnum.JM.getCode().equals(purchasePO.getDeptOperateMode())
                && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())
                && YesOrNoEnum.NO.getCode().equals(purchasePO.getTransferPurchSign())
                && DirectSignEnum.NOT_DIRECT.getCode().equals(purchasePO.getDirectSign())
                && !PmsPurchaseOrderSourceEnum.DELIVERY.getCode().equals(purchasePO.getBillSource())
                && Objects.nonNull(supplier) && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());
        try {
            if(isJM){
                // 采购订单来自需求单(非配转采，非直流)，审核后需要释放需求单对应的订货申请单
                if(PmsPurchaseOrderSourceEnum.DEMAND.getCode().equals(purchasePO.getBillSource())
                        && YesOrNoEnum.NO.getCode().equals(purchasePO.getTransferPurchSign())
                        && DirectSignEnum.NOT_DIRECT.getCode().equals(purchasePO.getDirectSign())
                        && StringUtils.hasText(purchasePO.getSrcBillNo())){
                    //根据需求单号获取对应 申请单商品明细
                    PmsDemandPruchDeliveryRefReq demandReq = new PmsDemandPruchDeliveryRefReq();
                    demandReq.setBillNo(purchasePO.getSrcBillNo());
                    demandReq.setRefBillNoList(Lists.newArrayList(purchasePO.getBillNo()));
                    demandReq.setTypeList(Lists.newArrayList(0));
                    List<PmsDemandPruchDeliveryRefResp> refResps =  pmsDemandPruchDeliveryRefRepositoryService.listPmsDemandPruchDeliveryByDemandBillNo(demandReq);
                    List<FranLineAdjustReq.OrderNumber> orderNumberList = refResps.stream().map(item ->{
                        FranLineAdjustReq.OrderNumber orderNumber = FranLineAdjustReq.OrderNumber.builder()
                                .orderNumber(item.getApplyBillNo())
                                .amount(item.getSrcOrderPrice().multiply(item.getSrcDemandQty()).doubleValue()).build();
                        return orderNumber;
                    }).collect(Collectors.toList());
                    adjustReq.setOrderNumberList(orderNumberList);
                }
                commonFranchiseService.adjustFranLine(adjustReq);
            }
        }catch (Exception e0){
            //回滚采购计划单剩余可采 1增加 2减少
            if(StringUtils.hasText(purchasePO.getPlanBillNo()) && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())){
                assemblePlanBill(purchasePO,purchBillDetailList,1);
            }
            BizExceptions.throwWithMsg("单号:"+purchasePO.getBillNo()+"加盟额度调整异常："+e0.getMessage());
        }

        try {
            //3.执行审核后上报库存
            BatchRecordReq batchRecordReq = convertStockReq(purchasePO,purchBillDetailList,PmsPurchOptTypeEnum.AUDIT);
            iCommonStockService.costStockExecute(batchRecordReq);

        }catch (Exception e){
            //回滚采购计划单剩余可采 1增加 2减少
            if(StringUtils.hasText(purchasePO.getPlanBillNo()) && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())){
                assemblePlanBill(purchasePO,purchBillDetailList,1);
            }
            //回滚加盟额度
            if(isJM){
                commonFranchiseService.franLineAdjustRollback(adjustReq);
            }
            BizExceptions.throwWithMsg("单号:"+purchasePO.getBillNo()+"库存异常："+e.getMessage());

        }
    }

    /**
     * 处理作废采购订单操作
     */

    @Transactional(rollbackFor = Exception.class)
    public void doCancel(PmsPurchaseOrderPO purchasePO, Integer status, OpInfo operatorInfo, PmsPurchOptTypeEnum optTypeEnum,Boolean isBatchCancel) {
        if(isBatchCancel){
            pmsPurchaseOrderRepositoryService.updateById(purchasePO);
        }
        FranLineAdjustReq adjustReq = commonFranchiseService.buildReq(purchasePO.getBillNo(),purchasePO.getDeptCode(),purchasePO.getTotalTaxMoney(), FranLineTypeEnum.PURCH_CANCEL.getCode(),operatorInfo);

        //已审核作废，收货中作废 需要处理计划单，加盟额度，库存
        if(PmsPurchaseBillStatusEnum.needDoStock(status)){
            List<PmsPurchaseBillDetailPO> purchDetailPOList = pmsPurchaseDetailRepositoryService.queryPurchDetailByBillNo(purchasePO.getBillNo());
            if(CollectionUtils.isEmpty(purchDetailPOList)){
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_000_P003);
            }

            //需要处理的采购数量 = 原采购数量-已履行数量(过滤超额履行商品行)
            List<PmsPurchaseBillDetailPO> detailList = purchDetailPOList.stream().filter(ft -> ft.getPurchQty().subtract(ft.getFulfilQty()).compareTo(BigDecimal.ZERO)>0).map(item -> {
                PmsPurchaseBillDetailPO detailPO = CglibCopier.copy(item,PmsPurchaseBillDetailPO.class);
                detailPO.setPurchQty(item.getPurchQty().subtract(item.getFulfilQty()));
                return detailPO;
            }).collect(Collectors.toList());

//            //1.执行调用采购计划单  采购更新剩余可采数量-加回
//            if(StringUtils.hasText(purchasePO.getPlanBillNo()) && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())){
//                assemblePlanBill(purchasePO,detailList,1);
//            }

            //2.调整加盟额度
            SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchasePO.getSupplierCode());
            Boolean isJM = DeptOperateModeEnum.JM.getCode().equals(purchasePO.getDeptOperateMode())
                    && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())
                    && YesOrNoEnum.NO.getCode().equals(purchasePO.getTransferPurchSign())
                    && DirectSignEnum.NOT_DIRECT.getCode().equals(purchasePO.getDirectSign())
                    && !PmsPurchaseOrderSourceEnum.DELIVERY.getCode().equals(purchasePO.getBillSource())
                    && Objects.nonNull(supplier) && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());

            if(isJM){
                commonFranchiseService.adjustFranLine(adjustReq);
            }
            try {
                // 3.审核后取消上报库存
                BatchRecordReq batchRecordReq = convertStockReq(purchasePO,detailList,optTypeEnum);
                iCommonStockService.costStockExecute(batchRecordReq);
            }catch (Exception e){
                // 回滚加盟额度
                if(isJM){
                    commonFranchiseService.franLineAdjustRollback(adjustReq);
                }
                BizExceptions.throwWithMsg("单号:"+purchasePO.getBillNo()+"库存异常："+e.getMessage());
            }
        }
    }


    public BatchRecordReq convertStockReq(PmsPurchaseOrderPO purchasePO, List<PmsPurchaseBillDetailPO> purchBillDetailList,
                                          PmsPurchOptTypeEnum optTypeEnum) {
        ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
        Integer stockcontrol = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(PMSSystemParamEnum.PURCHASE_REFUND_ENABLE_MINUS_STOCK, purchasePO.getDeptCode());

        CommonBillTypeEnum billTypeEnum = PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection()) ? CommonBillTypeEnum.PO : CommonBillTypeEnum.RNSR;
        CommonOperateEnum operateCode = null;
        switch (optTypeEnum) {
            case AUDIT:
                operateCode = CommonOperateEnum.POST;
                break;
            case CANCEL:
                operateCode = CommonOperateEnum.CANCEL;
                break;
            case EXPIRE:
                operateCode = CommonOperateEnum.EXPIRE;
                break;
            case FINISH:
                operateCode = CommonOperateEnum.COMPLETE;
                break;
            case ADJUST_INCREASE:
                operateCode = CommonOperateEnum.INCR;
                break;
            case SIGN_REDUCE:
                operateCode = CommonOperateEnum.DECR;
                break;
            default:
        }
        final CommonOperateEnum operateCodeEnum = operateCode;

        BatchRecordReq batchRecordReq = BatchRecordReq.builder()
                .tenantId(String.valueOf(purchasePO.getTenantId()))
                .whCode(purchasePO.getDeptCode())
                .whName(purchasePO.getDeptName())
                .deptCode(purchasePO.getDeptCode())
                .deptName(purchasePO.getDeptName())
                .billNo(purchasePO.getBillNo())
                .billType(billTypeEnum.getCode())
                .skuList(purchBillDetailList.stream().map(item -> {
                            StkTaskItemExecuteDto executeDto = StkTaskItemExecuteDto.builder()
                                    .billType(billTypeEnum.getCode())
                                    .operateCode(operateCodeEnum.getCode())
                                    .whCode(purchasePO.getDeptCode())
                                    .whName(purchasePO.getDeptName())
                                    .deptCode(purchasePO.getDeptCode())
                                    .deptName(purchasePO.getDeptName())
                                    .insideId(item.getInsideId())
                                    .supplierCode(item.getSupplierCode())
                                    .supplierName(item.getSupplierName())
                                    .contractNo(item.getContractNo())
                                    .skuCode(item.getSkuCode())
                                    .skuName(item.getSkuName())
                                    .skuType(item.getSkuType())
                                    .barcode(item.getBarcode())
                                    .skuModel(item.getSkuModel())
                                    .saleMode(item.getSaleMode())
                                    .uomAttr(item.getUomAttr())
                                    .periodFlag(item.getPeriodFlag())
                                    .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                    .outTaxRate(item.getOutputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                                    .realQty(item.getPurchQty().abs())//发生数量
                                    .costTaxPrice(item.getPurchPrice())
                                    .costTaxMoney(item.getPurchMoney())
                                    .costTax(item.getPurchTax())
                                    .salePrice(item.getSalePrice())
                                    .saleTaxMoney(item.getSaleMoney())
                                    .build();

                            //采退负库存标识
                            if (PmsBillDirectionEnum.REVERSE.getCode().equals(purchasePO.getBillDirection())) {
                                executeDto.setNegativeAllowedFlag(stockcontrol);
                            }
                            return executeDto;
                        }).collect(Collectors.toList())
                ).build();
        // 采购订单来自需求单（非配转采，非直流），审核后需要释放需求单对应的订货申请单
        if(PmsPurchaseOrderSourceEnum.DEMAND.getCode().equals(purchasePO.getBillSource())
                && YesOrNoEnum.NO.getCode().equals(purchasePO.getTransferPurchSign())
                && DirectSignEnum.NOT_DIRECT.getCode().equals(purchasePO.getDirectSign())
                && PmsPurchOptTypeEnum.AUDIT.getCode().equals(optTypeEnum.getCode()) && StringUtils.hasText(purchasePO.getSrcBillNo())){
            //根据需求单号获取对应 申请单商品明细
            PmsDemandPruchDeliveryRefReq demandReq = new PmsDemandPruchDeliveryRefReq();
            demandReq.setBillNo(purchasePO.getSrcBillNo());
            demandReq.setRefBillNoList(Lists.newArrayList(purchasePO.getBillNo()));
            demandReq.setTypeList(Lists.newArrayList(0));
            List<PmsDemandPruchDeliveryRefResp> refResps =  pmsDemandPruchDeliveryRefRepositoryService.listPmsDemandPruchDeliveryByDemandBillNo(demandReq);
            batchRecordReq.setReleaseSkuList(convertStockReleaseReq(purchasePO.getDeptCode(),refResps,CommonOperateEnum.DECR,purchasePO.getBillDirection()));
        }
        return batchRecordReq;
    }

    public List<StkTaskIReleaseExecuteDto> convertStockReleaseReq(String deptCode, List<PmsDemandPruchDeliveryRefResp> applyDetailList, CommonOperateEnum operateCode,Integer billDirection) {
        return applyDetailList.stream().map(item -> {
            return StkTaskIReleaseExecuteDto.builder()
                    .whCode(deptCode)
                    .operateCode(operateCode.getCode())
                    .outWhCode(deptCode)
                    .skuCode(item.getSkuCode())
                    .realQty(item.getSrcDemandQty().abs())
                    .srcBillNo(item.getApplyBillNo())
                    .srcBillType(PmsBillDirectionEnum.NORMAL.getCode().equals(billDirection)?CommonBillTypeEnum.AO.getCode():CommonBillTypeEnum.RO.getCode())
                    .build();
        }).collect(Collectors.toList());
    }


    /**
     * 采购订单调整
     * 上报库存组装
     */
    public BatchRecordReq convertStockReq4Adjust(String adjustBillNo, PmsPurchaseOrderPO purchasePO, List<PmsPurchaseBillDetailPO> purchBillDetailList) {
        ISupplychainControlEngineService supplychainControlEngineService = SpringContextUtil.getApplicationContext().getBean(ISupplychainControlEngineService.class);
        Integer stockcontrol = supplychainControlEngineService.getSupplychainBizSysParamRuleService().getIntValue(PMSSystemParamEnum.PURCHASE_REFUND_ENABLE_MINUS_STOCK, purchasePO.getDeptCode());

        CommonBillTypeEnum billTypeEnum = PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection()) ? CommonBillTypeEnum.PO : CommonBillTypeEnum.RNSR;

        BatchRecordReq batchRecordReq = BatchRecordReq.builder()
                .tenantId(String.valueOf(purchasePO.getTenantId()))
                .whCode(purchasePO.getDeptCode())
                .whName(purchasePO.getDeptName())
                .deptCode(purchasePO.getDeptCode())
                .deptName(purchasePO.getDeptName())
                .billNo(purchasePO.getBillNo())
                .billType(billTypeEnum.getCode())
                .billTime(LocalDateTime.now())
                .seqNo(adjustBillNo)
                .skuList(purchBillDetailList.stream().map(item -> {
                    String operateCode = item.getPurchQty().compareTo(BigDecimal.ZERO)>0 ? CommonOperateEnum.INCR.getCode():CommonOperateEnum.DECR.getCode();
                    StkTaskItemExecuteDto executeDto = StkTaskItemExecuteDto.builder()
                            .billType(billTypeEnum.getCode())
                            .operateCode(operateCode)
                            .whCode(purchasePO.getDeptCode())
                            .whName(purchasePO.getDeptName())
                            .deptCode(purchasePO.getDeptCode())
                            .deptName(purchasePO.getDeptName())
                            .insideId(item.getInsideId())
                            .supplierCode(item.getSupplierCode())
                            .supplierName(item.getSupplierName())
                            .contractNo(item.getContractNo())
                            .skuCode(item.getSkuCode())
                            .skuName(item.getSkuName())
                            .skuType(item.getSkuType())
                            .barcode(item.getBarcode())
                            .skuModel(item.getSkuModel())
                            .saleMode(item.getSaleMode())
                            .uomAttr(item.getUomAttr())
                            .periodFlag(item.getPeriodFlag())
                            .inTaxRate(item.getInputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                            .outTaxRate(item.getOutputTaxRate().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                            .realQty(item.getPurchQty().abs())//发生数量
                            .salePrice(item.getSalePrice())
                            .saleTaxMoney(item.getSaleMoney())
                            .build();
                    //采退负库存标识
                    if (PmsBillDirectionEnum.REVERSE.getCode().equals(purchasePO.getBillDirection())) {
                        executeDto.setNegativeAllowedFlag(stockcontrol);
                    }
                    return executeDto;
                }).collect(Collectors.toList())
                ).build();

        return batchRecordReq;
    }


    /**
     * 组装数据
     * 采购订单-审核，取消等 更新剩余可采
     * updateType 修改类型 1增加 2减少
     * @return
     */
    public void assemblePlanBill(PmsPurchaseOrderPO purchasePO, List<PmsPurchaseBillDetailPO> purchBillDetailList,Integer updateType){
        List<PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq> goodsList = purchBillDetailList.stream().map(item -> {
            PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq goods = PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq.builder()
                    .skuCode(item.getSkuCode())
                    .skuType(item.getSkuType())
                    .updateQty(item.getPurchQty())
                    .updateType(updateType)
                    .build();
            return goods;
        }).collect(Collectors.toList());

        PurchasePlanBillNumChangeReq req = PurchasePlanBillNumChangeReq.builder()
                .billNo(purchasePO.getPlanBillNo())
                .goodsList(goodsList)
                .build();

        updatePlanBillNum(req);

    }

    /**
     * 组装数据
     * 采购订单调整-更新剩余可采
     * updateType 修改类型 1增加 2减少
     * 调增+正向=2  调增+回滚=1   调减+正向=1 调减+回滚=2
     * @return
     */
    public void assembleAdjustPlanBill(PmsPurchaseOrderPO purchasePO, List<PmsPurchasePlanDetailPO> planDetailList, Boolean isRollback){
        List<PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq> goodsList = planDetailList.stream().map(item -> {
            Integer updateType = isRollback ? (item.getPlanReqQty().compareTo(BigDecimal.ZERO) > 0 ? 1:2)
                    :(item.getPlanReqQty().compareTo(BigDecimal.ZERO) > 0 ? 2:1);

            PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq goods = PurchasePlanBillNumChangeReq.PurchasePlanBillNumDetailChangeReq.builder()
                    .skuCode(item.getSkuCode())
                    .skuType(item.getSkuType())
                    .updateQty(item.getPlanReqQty().abs())
                    .updateType(updateType)
                    .build();
            return goods;
        }).collect(Collectors.toList());

        PurchasePlanBillNumChangeReq req = PurchasePlanBillNumChangeReq.builder()
                .billNo(purchasePO.getPlanBillNo())
                .goodsList(goodsList)
                .build();

        updatePlanBillNum(req);

    }

    public void updatePlanBillNum(PurchasePlanBillNumChangeReq req){
        List<String> redisWords = Arrays.asList(TenantContext.get(), "expirePlanBill", req.getBillNo());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, req.getBillNo(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        try {
            pmsPurchasePlanDomainService.updateNum(req);
        } catch (BizException e) {
            Logs.error(e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            Logs.error(e.getMessage(), e);
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0019);
        } finally {
            // 释放锁
            redisUtil.unlock(redisKey);
        }
    }

    /**
     * 处理调整采购订单操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void doAdjust(PmsPurchaseOrderPO purchBill,
                         List<BillAdjustLogPO> billAdjustLog,
                         PmsPurchaseOrderPO orderPO,
                         List<PmsPurchaseBillDetailPO> afterBillDetail,
                         List<PmsPurchasePlanDetailPO> planDetailList,
                         List<PmsPurchaseBillDetailPO> changeBillDetail,
                         OpInfo operatorInfo,
                         Boolean isBatchAdjust) {

        //调整信息落DB
        billAdjustLogRepositoryService.saveBatch(billAdjustLog);

        //更新原单调整信息
        pmsPurchaseOrderRepositoryService.updatePurchOrderBill(orderPO);
        //商品行信息变更，处理相关数据
        if(CollectionUtils.isNotEmpty(afterBillDetail)){
            afterBillDetail.forEach(p -> {
                pmsPurchaseDetailRepositoryService.updateByBillSku(p);
            });

            //更新剩余可采
            if(CollectionUtils.isNotEmpty(planDetailList)){
                assembleAdjustPlanBill(purchBill,planDetailList,false);
            }

            //调整加盟额度
            BigDecimal amount = orderPO.getTotalTaxMoney().subtract(purchBill.getTotalTaxMoney());
            FranLineAdjustReq adjustReq = commonFranchiseService.buildReq(purchBill.getBillNo(),purchBill.getDeptCode(),amount, FranLineTypeEnum.PURCH_MODIFY.getCode(),operatorInfo);
            SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchBill.getSupplierCode());
            Boolean isJM = DeptOperateModeEnum.JM.getCode().equals(purchBill.getDeptOperateMode())
                    && PmsBillDirectionEnum.NORMAL.getCode().equals(purchBill.getBillDirection())
                    && YesOrNoEnum.NO.getCode().equals(purchBill.getTransferPurchSign())
                    && DirectSignEnum.NOT_DIRECT.getCode().equals(purchBill.getDirectSign())
                    && !PmsPurchaseOrderSourceEnum.DELIVERY.getCode().equals(purchBill.getBillSource())
                    && Objects.nonNull(supplier) && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());
            try {
                if(isJM){
                    commonFranchiseService.adjustFranLine(adjustReq);
                }
            }catch (Exception e0){
                //回滚采购计划单剩余可采 1增加 2减少
                if(CollectionUtils.isNotEmpty(planDetailList)){
                    assembleAdjustPlanBill(purchBill,planDetailList,true);
                }
            }

            //上报库存-调增，调减
            try{
                if (CollectionUtils.isNotEmpty(changeBillDetail)) {
                    BatchRecordReq batchRecordReq = convertStockReq4Adjust(billAdjustLog.get(0).getBillNo(),purchBill,changeBillDetail);
                    iCommonStockService.costStockExecute(batchRecordReq);
                }
            }catch (Exception e){
                //回滚采购计划单剩余可采 1增加 2减少
                if(CollectionUtils.isNotEmpty(planDetailList)){
                    assembleAdjustPlanBill(purchBill,planDetailList,true);
                }
                //上报库存异常，回滚加盟
                if(isJM){
                    commonFranchiseService.franLineAdjustRollback(adjustReq);
                }
                BizExceptions.throwWithMsg(e.getMessage());
            }
        }
    }

    /**
     * 采购订单校验加盟额度
     * @param purchasePO
     */
    public void purchCheckFranLine(PmsPurchaseOrderPO purchasePO,BigDecimal totalTaxMoney){
        SupplierByCodeResp supplier = baseStoreUtil.getSupplierCache(purchasePO.getSupplierCode());
        Boolean isJM = DeptOperateModeEnum.JM.getCode().equals(purchasePO.getDeptOperateMode())
                && PmsBillDirectionEnum.NORMAL.getCode().equals(purchasePO.getBillDirection())
                && YesOrNoEnum.NO.getCode().equals(purchasePO.getTransferPurchSign())
                && DirectSignEnum.NOT_DIRECT.getCode().equals(purchasePO.getDirectSign())
                && !PmsPurchaseOrderSourceEnum.DELIVERY.getCode().equals(purchasePO.getBillSource())
                && Objects.nonNull(supplier) && !SettleModeEnum.SELF_ACCOUNT.getCode().equals(supplier.getSettleMode());
        if(isJM){
            commonFranchiseService.checkFranLine(purchasePO.getDeptCode(),totalTaxMoney);
        }
    }
}
