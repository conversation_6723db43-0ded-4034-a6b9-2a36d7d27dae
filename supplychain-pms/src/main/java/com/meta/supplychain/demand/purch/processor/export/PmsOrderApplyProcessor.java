package com.meta.supplychain.demand.purch.processor.export;

import cn.linkkids.framework.croods.common.logger.Logs;
import com.alibaba.ageiport.processor.core.annotation.ExportSpecification;
import com.alibaba.ageiport.processor.core.constants.ExecuteType;
import com.alibaba.ageiport.processor.core.exception.BizException;
import com.alibaba.ageiport.processor.core.model.api.BizExportPage;
import com.alibaba.ageiport.processor.core.model.api.BizUser;
import com.alibaba.ageiport.processor.core.task.exporter.ExportProcessor;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Sets;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.convert.pms.OrderApplyBillConvert;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.view.ApplyBillExportView;
import com.meta.supplychain.entity.po.pms.PmsApplyBillPO;
import com.meta.supplychain.enums.UserResourceCodeEnum;
import com.meta.supplychain.enums.pms.BillStateEnum;
import com.meta.supplychain.enums.pms.PmsSendModeEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsApplyBillRepositoryService;
import com.meta.supplychain.util.DateUtil;
import com.meta.supplychain.util.UserResourceUtil;
import com.meta.supplychain.util.spring.SpringContextUtil;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 采购订单明细汇总列表导出
 */
@ExportSpecification(code = "PmsOrderApplyProcessor", name = "订货申请列表导出", executeType = ExecuteType.CLUSTER)
public class PmsOrderApplyProcessor implements ExportProcessor<QueryApplyBillDTO, PmsApplyBillPO, ApplyBillExportView> {

    OrderApplyBillConvert orderApplyBillConvert = OrderApplyBillConvert.MAPPER;
    @Override
    public Integer totalCount(BizUser bizUser, QueryApplyBillDTO req) throws BizException {
        PmsApplyBillRepositoryService pmsApplyBillRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsApplyBillRepositoryService.class);
        //条件预处理 数据权限
        return pmsApplyBillRepositoryService.getApplyListCount(req);
    }

    @Override
    public List<PmsApplyBillPO> queryData(BizUser bizUser, QueryApplyBillDTO req, BizExportPage bizExportPage) throws BizException {
        PmsApplyBillRepositoryService pmsApplyBillRepositoryService = SpringContextUtil.getApplicationContext().getBean(PmsApplyBillRepositoryService.class);
        //条件预处理 数据权限
        Page<PmsApplyBillPO> page = new Page<>(Long.valueOf(bizExportPage.getNo()), Long.valueOf(bizExportPage.getSize()));
        IPage<PmsApplyBillPO> detailList = pmsApplyBillRepositoryService.getApplyList(req, page);
        return detailList.getRecords();
    }

    @Override
    public List<ApplyBillExportView> convert(BizUser bizUser, QueryApplyBillDTO req, List<PmsApplyBillPO> list) throws BizException {
        List<ApplyBillExportView> applyBillExportViews = new ArrayList<>();
        UserResourceUtil userResourceUtil = SpringContextUtil.getApplicationContext().getBean(UserResourceUtil.class);
        Boolean showPriceFlag = userResourceUtil.haveAllTheButtonResources(Sets.newHashSet(UserResourceCodeEnum.SCM_PMS_DHSQ_VIEW_PRICE_BUTTON),bizUser.getBizUserId());
        for (PmsApplyBillPO pmsApplyBillPO : list) {
            ApplyBillExportView applyExportView = ApplyBillExportView.builder()
                    .status( BillStateEnum.getEnumByCode(pmsApplyBillPO.getStatus()).getDesc())
                    .billNo(pmsApplyBillPO.getBillNo())
                    .deptName(pmsApplyBillPO.getDeptName())
                    .deptCode(pmsApplyBillPO.getDeptCode())
                    .attributeCode(pmsApplyBillPO.getAttributeCode())
                    .manageCategory(pmsApplyBillPO.getManageCategoryCode() == null ? null : pmsApplyBillPO.getManageCategoryCode()+pmsApplyBillPO.getManageCategoryName())
                    .totalOrderQty(pmsApplyBillPO.getTotalOrderQty())
                    .totalPurchTaxMoney(pmsApplyBillPO.getTotalPurchTaxMoney().toString())
                    .totalPurchTax(pmsApplyBillPO.getTotalPurchTax().toString())
                    .deliverDate(DateUtil.localDateFormateYmd(pmsApplyBillPO.getDeliverDate()))
                    .validityDate(DateUtil.localDateFormateYmd(pmsApplyBillPO.getValidityDate()))
                    .sendMode(PmsSendModeEnum.getByCode(pmsApplyBillPO.getSendMode()).getDesc())
                    .buildTime(DateUtil.ymdHms(pmsApplyBillPO.getCreateTime()))
                    .buildMan(pmsApplyBillPO.getCreateCode()  + "_" +  pmsApplyBillPO.getCreateName())
                    .updateTime(DateUtil.ymdHms(pmsApplyBillPO.getUpdateTime()))
                    .modifyMan(pmsApplyBillPO.getUpdateCode() + "_" + pmsApplyBillPO.getUpdateName())
                    .cancelTime(DateUtil.ymdHms(pmsApplyBillPO.getCancelTime()))
                    .cancelMan(pmsApplyBillPO.getCancelManCode()  + "_" +  pmsApplyBillPO.getCancelManName())
                    .remark(pmsApplyBillPO.getRemark())
                    .build();
            Logs.info("用户{}是否拥有查看价格权限{}",bizUser.getBizUserId(),showPriceFlag);
            if (Boolean.FALSE.equals(showPriceFlag)){
                applyExportView.setTotalPurchTaxMoney("****");
                applyExportView.setTotalPurchTax("****");
            }
            applyBillExportViews.add( applyExportView);
        }
        //todo 查看进价权限

        return applyBillExportViews;
    }
}
