package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.beans.CglibCopier;
import com.meta.supplychain.common.component.service.impl.IWdsAdjustDeliveryOrderCoreService;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.demand.purch.domain.intf.IWmsAdjustDeliveryOrderService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.wds.WdsDeliveryAdjustDTO;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustBaseReq;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustReq;
import com.meta.supplychain.entity.dto.wds.resp.WdsDeliveryOrderAdjustResp;
import com.meta.supplychain.entity.po.pms.BillAdjustLogPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.wds.WDDeliveryOrderBillStatusEnum;
import com.meta.supplychain.enums.wds.WDErrorCodeEnum;
import com.meta.supplychain.infrastructure.repository.service.impl.wds.WdDeliveryBillDetailRepositoryImpl;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.BaseStoreUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class WmsAdjustDeliveryOrderServiceImpl implements IWmsAdjustDeliveryOrderService {

    private final IWdDeliveryBillRepository wdDeliveryBillRepositoryService;
    private final WdDeliveryBillDetailRepositoryImpl wdsDeliveryBillDetailRepositoryService;
    private final ISupplychainControlEngineService supplychainControlEngineService;
    private final IWdsAdjustDeliveryOrderCoreService adjustDeliveryOrderCoreService;
    private final BaseStoreUtil baseStoreUtil;


    @Override
    public List<WdsDeliveryOrderAdjustResp> adjustDeliveryOrder(List<WdsDeliveryOrderAdjustBaseReq> req, OpInfo operatorInfo) {

        List<String> billNoList = req.stream().map(WdsDeliveryOrderAdjustBaseReq::getBillNo).collect(Collectors.toList());
        List<WdDeliveryBillPO> wdDeliveryBillPOS = wdDeliveryBillRepositoryService.queryDeliveryOrderBillByBillNo(billNoList);
        Map<String, WdDeliveryBillPO> deliveryBillPOMap = wdDeliveryBillPOS.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
        List<WdDeliveryBillDetailPO> deliveryBillDetailPOList = wdsDeliveryBillDetailRepositoryService.queryDetailListByBillNo(billNoList);
        Map<String, List<WdDeliveryBillDetailPO>> detailMap = deliveryBillDetailPOList.stream().collect(Collectors.groupingBy(WdDeliveryBillDetailPO::getBillNo));
        List<WdsDeliveryOrderAdjustResp> billResps = new ArrayList<>();
        req.forEach(item -> {
            try {
                //检查单据
                WdDeliveryBillPO wdDeliveryBillPO = deliveryBillPOMap.get(item.getBillNo());
                if (wdDeliveryBillPO == null) {
                    return;
                }
                //单据状态 已审核+未预约
                Boolean adjustCondition = WDDeliveryOrderBillStatusEnum.DELIVERY_STATUS_APPROVED.getCode().equals(wdDeliveryBillPO.getStatus());
                if (!adjustCondition) {
                    billResps.add(new WdsDeliveryOrderAdjustResp(item.getBillNo(), WDErrorCodeEnum.SC_WDS_001_P012.getDesc()));
                }

                //原商品明细
                List<WdDeliveryBillDetailPO> billDetailPOList = detailMap.get(item.getBillNo());
                Map<Long, WdDeliveryBillDetailPO> detailAlreadyMap = billDetailPOList.stream().collect(Collectors.toMap(WdDeliveryBillDetailPO::getInsideId, Function.identity()));


                String adjustBillNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.ADJUST_LOG, wdDeliveryBillPO.getInDeptCode());

                WdsDeliveryAdjustDTO wdsDeliveryAdjustDTO = adjustDeliveryOrderCoreService.assembleAdjustReq(CglibCopier.copy(item, WdsDeliveryOrderAdjustReq.class), detailAlreadyMap);
                wdsDeliveryAdjustDTO.setAdjustBillNo(adjustBillNo);
                wdsDeliveryAdjustDTO.setOldBill(wdDeliveryBillPO);
                wdsDeliveryAdjustDTO.setNewBill(CglibCopier.copy(wdDeliveryBillPO, WdDeliveryBillPO.class));
                List<BillAdjustLogPO> billAdjustLog = adjustDeliveryOrderCoreService.assembleChangeInfo(wdsDeliveryAdjustDTO);
                adjustDeliveryOrderCoreService.doAdjust(wdsDeliveryAdjustDTO, operatorInfo, billAdjustLog);
            } catch (Exception e) {
                billResps.add(new WdsDeliveryOrderAdjustResp(item.getBillNo(), e.getMessage()));
            }
        });

        return billResps;
    }
}
