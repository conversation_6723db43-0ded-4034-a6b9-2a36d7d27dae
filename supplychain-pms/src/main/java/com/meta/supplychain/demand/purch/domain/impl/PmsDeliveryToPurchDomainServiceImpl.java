package com.meta.supplychain.demand.purch.domain.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.Results;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meta.supplychain.common.component.domain.md.intf.IMdDeliveryAppointmentStrategyDomainService;
import com.meta.supplychain.common.component.domain.md.intf.IMdDemandStrategyDomainService;
import com.meta.supplychain.common.component.service.intf.ISupplychainBizGoodsRuleService;
import com.meta.supplychain.common.component.service.intf.ISupplychainPmsBizRuleEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.constants.SysConstants;
import com.meta.supplychain.demand.purch.application.intf.PmsPurchBillApplicationService;
import com.meta.supplychain.demand.purch.domain.intf.IPmsDeliveryToPurchDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.bds.resp.QueryBatchDeptListResp;
import com.meta.supplychain.entity.dto.goods.resp.GoodsQueryResp;
import com.meta.supplychain.entity.dto.md.component.goodsrule.*;
import com.meta.supplychain.entity.dto.md.demandstrategy.MdDemandStrategyStatusMapping4DeptDTO;
import com.meta.supplychain.entity.dto.md.req.demandstrategy.MdDemandStrategyStatusMapping4DeptQueryReq;
import com.meta.supplychain.entity.dto.md.resp.deliveryappointment.DeliveryAppointmentStrategyResultDTO;
import com.meta.supplychain.entity.dto.pms.demand.GroupKey;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryDetailReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.deliverytopurch.DeliveryToPurchSubmitReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryToPurchRefReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandDeliveryToPurchReq;
import com.meta.supplychain.entity.dto.pms.req.demand.PmsDemandPurchShipperReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillChangeReq;
import com.meta.supplychain.entity.dto.pms.req.purch.PurchaseBillDetailCreateReq;
import com.meta.supplychain.entity.dto.pms.resp.deliverytopurch.*;
import com.meta.supplychain.entity.dto.pms.resp.purch.PurchBillOptResp;
import com.meta.supplychain.entity.dto.wds.req.WdDeliveryDetailQueryByBillReq;
import com.meta.supplychain.entity.po.md.MdDeliveryDockDeptPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockGoodsCategoryPO;
import com.meta.supplychain.entity.po.md.MdDeliveryDockStrategyPO;
import com.meta.supplychain.entity.po.pms.PmsDist2purchSupplierRecordPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillDetailPO;
import com.meta.supplychain.entity.po.wds.WdDeliveryBillPO;
import com.meta.supplychain.enums.YesOrNoEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.md.MdDemandStrategyBizConditionEnum;
import com.meta.supplychain.enums.md.MdDemandStrategyStatusConditionEnum;
import com.meta.supplychain.enums.md.MdManualToPurchaseOrderStatusEnum;
import com.meta.supplychain.enums.pms.*;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDist2purchSupplierRecordRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillDetailRepository;
import com.meta.supplychain.infrastructure.repository.service.intf.wds.IWdDeliveryBillRepository;
import com.meta.supplychain.util.Assert;
import com.meta.supplychain.util.RedisUtil;
import com.meta.supplychain.util.UserUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 这是是类描述
 *
 * <AUTHOR>
 * @date 2025/05/20 17:11
 **/
@Service
public class PmsDeliveryToPurchDomainServiceImpl implements IPmsDeliveryToPurchDomainService {

    @Autowired
    private ISupplychainPmsBizRuleEngineService iSupplychainPmsBizRuleEngineService;

    @Resource
    private IMdDeliveryAppointmentStrategyDomainService mdDeliveryAppointmentStrategyDomainService;

    @Resource
    private IPmsDist2purchSupplierRecordRepositoryService pmsDist2purchSupplierRecordRepositoryService;

    @Resource
    private IWdDeliveryBillDetailRepository deliveryBillDetailRepository;
    @Autowired
    private IWdDeliveryBillRepository deliveryBillRepository;
    @Autowired
    private ISupplychainPmsBizRuleEngineService supplychainPmsBizRuleEngineService;

    @Autowired
    private PmsPurchBillApplicationService purchBillApplicationService;

    @Autowired
    private ICommonGoodsService commonGoodsService;

    @Autowired
    private ISupplychainBizGoodsRuleService  iSupplychainBizGoodsRuleService;

    @Autowired
    private IMdDemandStrategyDomainService mdDemandStrategyDomainService;

    @Resource
    private UserUtil userUtil;

    @Autowired
    private RedisUtil redisUtil;

    private static final Long EXPIRE_TIME = 5 * 60 * 1000L;
    /**
     * 加载订单生成可转采数据
     *
     * @param param 配送订单
     * @return 转采信息
     */
    @Override
    public DeliveryToPurchResultResp loadingDeliveryGenerateToPurch(DeliveryDetailReq param) {
        WdDeliveryDetailQueryByBillReq req = new WdDeliveryDetailQueryByBillReq();
        BeanUtil.copyProperties(param, req);
        req.setPageSize(1000L);
        //获取配送订单明细
        List<WdDeliveryBillDetailPO> detailListByBillInfo = deliveryBillDetailRepository.getDetailListByBillInfo(req);
        //筛选出待转采明细
        List<WdDeliveryBillDetailPO> waitTransferPurchBill = detailListByBillInfo.stream().filter(item -> !YesOrNoEnum.YES.getCode().equals(item.getTransferPurchSign())).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isNotEmpty(waitTransferPurchBill), PmsErrorCodeEnum.SC_PMS_008_B004);

        //获取配送订单
        List<WdDeliveryBillPO> wdDeliveryBillList = deliveryBillRepository.queryDeliveryOrderBillByBillNo(waitTransferPurchBill.stream().map(WdDeliveryBillDetailPO::getBillNo).collect(Collectors.toList()));
        Map<String,WdDeliveryBillPO> deliveryBillMap = wdDeliveryBillList.stream().collect(Collectors.toMap(WdDeliveryBillPO::getBillNo, Function.identity()));
        //获取商品品类集合
        List<String> categoryCodeList = waitTransferPurchBill.stream()
                .filter(item -> StringUtils.isNotBlank(item.getCategoryCode()))
                .map(WdDeliveryBillDetailPO::getCategoryCode)
                .distinct()
                .collect(Collectors.toList());
        List<CategoryCodeAll> categoryCodeAllList = iSupplychainBizGoodsRuleService.getCategoryCodeAll(categoryCodeList);
        Map<String, String> categoryCodeAllMap = categoryCodeAllList.stream().collect(Collectors.toMap(CategoryCodeAll::getCategoryCode, CategoryCodeAll::getCategoryCodeAll));
        //部门商品信息
        List<DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo> deptGoodsList = waitTransferPurchBill.stream().map(item -> {
            DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo deliveryToPurchDeptGoodsInfo = new DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo();
            deliveryToPurchDeptGoodsInfo.setInsideId(item.getInsideId());
            deliveryToPurchDeptGoodsInfo.setDirectSign(item.getDirectSign());
            deliveryToPurchDeptGoodsInfo.setDeptCode(item.getInDeptCode());
            deliveryToPurchDeptGoodsInfo.setDeptName(item.getInDeptName());
            deliveryToPurchDeptGoodsInfo.setGoodsType(item.getSkuType());
            deliveryToPurchDeptGoodsInfo.setSkuCode(item.getSkuCode());
            deliveryToPurchDeptGoodsInfo.setSkuName(item.getSkuName());
            deliveryToPurchDeptGoodsInfo.setSkuModel(item.getSkuModel());
            deliveryToPurchDeptGoodsInfo.setGoodsNo(item.getGoodsNo());
            deliveryToPurchDeptGoodsInfo.setCategoryCode(item.getCategoryCode());
            deliveryToPurchDeptGoodsInfo.setCategoryName(item.getCategoryName());
            if(StringUtils.isNotBlank(item.getCategoryCode())){
                deliveryToPurchDeptGoodsInfo.setCategoryCodeAll(categoryCodeAllMap.get(item.getCategoryCode()));
            }
            deliveryToPurchDeptGoodsInfo.setBarcode(item.getBarcode());
            deliveryToPurchDeptGoodsInfo.setUnit(item.getUnit().toString());
            deliveryToPurchDeptGoodsInfo.setPackageUnit(item.getPackageUnit());
            deliveryToPurchDeptGoodsInfo.setInputTaxRate(item.getInputTaxRate());
            deliveryToPurchDeptGoodsInfo.setOutputTaxRate(item.getOutputTaxRate());
            deliveryToPurchDeptGoodsInfo.setDeliveryUnitRate(item.getUnitRate());
            deliveryToPurchDeptGoodsInfo.setDeliveryQty(item.getDeliveryQty());
            deliveryToPurchDeptGoodsInfo.setPurchBatchNo(item.getPurchBatchNo());
            deliveryToPurchDeptGoodsInfo.setSendMode(deliveryBillMap.get(item.getBillNo()).getSendMode());
            deliveryToPurchDeptGoodsInfo.setDeliveryList(convertDeliveryInfo(item));
            return deliveryToPurchDeptGoodsInfo;
        }).collect(Collectors.toList());
        //配转采参数
        DeliveryToPurchReq deliveryToPurchReq = new DeliveryToPurchReq();
        deliveryToPurchReq.setSrcType(3);//3-手工配转采
        deliveryToPurchReq.setDeptGoodsList(deptGoodsList);
        return generateDeliveryToPurch(deliveryToPurchReq);
    }

    /**
     * 保存订单生成可转采数据
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<String>> saveDeliveryToPurch(DeliveryToPurchSubmitReq param) {
        //操作人信息
        OpInfo operatorInfo = userUtil.getOpInfoWithThrow();
        List<String> redisWords = Arrays.asList(TenantContext.get(), "saveDeliveryToPurch", operatorInfo.getUserId());
        String redisKey = String.join(SysConstants.UNDERLINE_DELIMITER, redisWords);
        if (!redisUtil.tryLock(redisKey, operatorInfo.getUserId(), EXPIRE_TIME)){
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_D001);
        }
        List<String> list  = new ArrayList<>();
        try {
            PurchaseOrderSummaryResp preview = preview(param);
            MdDemandStrategyStatusMapping4DeptQueryReq query = new MdDemandStrategyStatusMapping4DeptQueryReq();
            query.setDemandTransferConditionList(Arrays.asList(MdDemandStrategyStatusConditionEnum.MANUAL_TO_PURCHASE_ORDER_STATUS.getCode()));
            query.setDemandTransferConditionBizList(Arrays.asList(MdDemandStrategyBizConditionEnum.STATUS.getCode()));
            List<MdDemandStrategyStatusMapping4DeptDTO> mdDemandStrategyStatusMapping4DeptDTOS = mdDemandStrategyDomainService.queryMdDemandStrategyStatusMappingData(query);
            Integer status = PmsPurchOptTypeEnum.SAVE.getCode();
            if(CollectionUtils.isNotEmpty(mdDemandStrategyStatusMapping4DeptDTOS)){
                Integer val = Integer.valueOf(mdDemandStrategyStatusMapping4DeptDTOS.get(0).getVal());
                status = MdManualToPurchaseOrderStatusEnum.APPROVED.getCode().equals(val) ? PmsPurchOptTypeEnum.AUDIT.getCode() : PmsPurchOptTypeEnum.SAVE.getCode();
            }
            for (PurchaseBillChangeReq item : preview.getPurchaseOrders()) {
                //获取采购单号
                String billNo = supplychainPmsBizRuleEngineService.getBillNo(MdBillNoBillTypeEnum.PMS_PURCHASE_ORDER, param.getDeptCode());
                Logs.info("采购单号：{}", billNo);
                item.setBillNo(billNo);
                item.setOptType(status);
                //创建采购单
                Result<PurchBillOptResp> purchBill = purchBillApplicationService.createPurchBill(item);
                list.add(billNo);
            }
            //配送订单商品map
            Map<Long,PmsDemandDeliveryToPurchReq> deliveryBillDetail = param.getDeliveryGoodsList().stream().collect(Collectors.toMap(PmsDemandDeliveryToPurchReq::getInsideId,Function.identity()));
            deliveryBillDetail.keySet().forEach(key -> {
                PmsDemandDeliveryToPurchReq deliveryToPurchReq = deliveryBillDetail.get(key);
                List<String> billNoList = deliveryToPurchReq.getDeliveryToPurchRefList().stream().map(PmsDemandDeliveryToPurchRefReq::getBillNo).collect(Collectors.toList());
                //更新配送订单商品的转采状态
                deliveryBillDetailRepository.lambdaUpdate()
                        .in(WdDeliveryBillDetailPO::getBillNo, billNoList)
                        .eq(WdDeliveryBillDetailPO::getSkuCode,deliveryToPurchReq.getSkuCode())
                        .set(WdDeliveryBillDetailPO::getTransferPurchSign, YesOrNoEnum.YES.getCode())
                        .update();
            });
        } catch (Exception e) {
            Logs.error("保存转单订单发生异常：{}",e);
            BizExceptions.throwWithMsg("保存转单订单发生异常："+e.getMessage());
        }finally {
            redisUtil.unlock(redisKey);
        }
        return Results.ofSuccess(list);
    }

    /**
     * 预览
     *
     * @param param
     */
    @Override
    public PurchaseOrderSummaryResp preview(DeliveryToPurchSubmitReq param) {
        return generatePurchaseOrderPreview(param.getDeliveryGoodsList(), param.getValidDate(), param.getToStoreDate(), param.getRemark());
    }

    /**
     * 生成配转采数据
     */
    @Override
    public DeliveryToPurchResultResp generateDeliveryToPurch(DeliveryToPurchReq param) {
        List<DeliveryToPurchReq.DeliveryToPurchDeliveryInfo> deliveryList = new ArrayList<>();

        Map<Long, DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo> deliveryToPurchDeptGoodsInfoMap = new HashMap<>();

        //key:要货部门编码  value:商品编码
        Map<String, List<String>> deptGoodsMap = new HashMap<>();

        Set<String> categoryCodeSet = new HashSet<>();
        Set<String> skuCodeSet = new HashSet<>();
        Set<String> deliveryDeptCodeSet = new HashSet<>();

        Map<String, DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo> deptGoodsInfoMap = new HashMap<>();
        for (DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo deliveryToPurchDeptGoodsInfo : param.getDeptGoodsList()) {
            //直流的直接过滤
            if (ObjectUtils.notEqual(DirectSignEnum.NOT_DIRECT.getCode(), deliveryToPurchDeptGoodsInfo.getDirectSign())) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_008_B005, new String[]{deliveryToPurchDeptGoodsInfo.getSkuCode()});
            }
            
            if(null == deliveryToPurchDeptGoodsInfo.getInsideId()){
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_008_B007, new String[]{deliveryToPurchDeptGoodsInfo.getDeptCode(),deliveryToPurchDeptGoodsInfo.getSkuCode()});
            }

            //组装商品和品类信息
            skuCodeSet.add(deliveryToPurchDeptGoodsInfo.getSkuCode());
            String categoryCodeAll = deliveryToPurchDeptGoodsInfo.getCategoryCodeAll();

            if(StringUtils.isEmpty(categoryCodeAll)){
                if(StringUtils.isEmpty(deliveryToPurchDeptGoodsInfo.getCategoryCode())){
                    BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_008_B006, new String[]{deliveryToPurchDeptGoodsInfo.getSkuCode()});
                }
                List<String> categoryCodeList = new ArrayList<>();
                categoryCodeList.add(deliveryToPurchDeptGoodsInfo.getCategoryCode());
                List<CategoryCodeAll> categoryCodeAll1 = iSupplychainBizGoodsRuleService.getCategoryCodeAll(categoryCodeList);
                if(CollectionUtils.isNotEmpty(categoryCodeAll1)){
                    categoryCodeAll = categoryCodeAll1.get(0).getCategoryCodeAll();
                    deliveryToPurchDeptGoodsInfo.setCategoryCodeAll(categoryCodeAll);
                }
            }

            if (StringUtils.isNotEmpty(categoryCodeAll)) {
                String[] split = categoryCodeAll.split(",");
                if (split.length > 0) {
                    List<String> list = Arrays.asList(split);
                    categoryCodeSet.addAll(list);
                }
            }

            if (deptGoodsMap.containsKey(deliveryToPurchDeptGoodsInfo.getDeptCode())) {
                deptGoodsMap.get(deliveryToPurchDeptGoodsInfo.getDeptCode()).add(deliveryToPurchDeptGoodsInfo.getSkuCode());
            } else {
                List<String> skuCodeList = new ArrayList<>();
                skuCodeList.add(deliveryToPurchDeptGoodsInfo.getSkuCode());
                deptGoodsMap.put(deliveryToPurchDeptGoodsInfo.getDeptCode(), skuCodeList);
            }

            if (CollectionUtils.isNotEmpty(deliveryToPurchDeptGoodsInfo.getDeliveryList())) {
                for (DeliveryToPurchReq.DeliveryToPurchDeliveryInfo deliveryToPurchDeliveryInfo : deliveryToPurchDeptGoodsInfo.getDeliveryList()) {
                    deliveryToPurchDeliveryInfo.setDeptGoodsDeliveryToPurchKey(deliveryToPurchDeptGoodsInfo.getDeptGoodsDeliveryToPurchKey());
                    deliveryToPurchDeliveryInfo.setDeptGoodsDetail(deliveryToPurchDeptGoodsInfo);

                    deliveryList.add(deliveryToPurchDeliveryInfo);

                    deliveryDeptCodeSet.add(deliveryToPurchDeliveryInfo.getDistDeptCode());

                    if(ObjectUtils.notEqual(deliveryToPurchDeliveryInfo.getPinsideId(),deliveryToPurchDeptGoodsInfo.getInsideId())){
                        BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_008_B008, new String[]{deliveryToPurchDeptGoodsInfo.getDeptCode(),deliveryToPurchDeptGoodsInfo.getSkuCode(),deliveryToPurchDeliveryInfo.getDistDeptCode()});
                    }
                }
            }

            deliveryToPurchDeptGoodsInfoMap.put(deliveryToPurchDeptGoodsInfo.getInsideId(), deliveryToPurchDeptGoodsInfo);

            String key = deliveryToPurchDeptGoodsInfo.getDeptCode() + "_" + deliveryToPurchDeptGoodsInfo.getSkuCode();
            deptGoodsInfoMap.put(key, deliveryToPurchDeptGoodsInfo);
        }

        //配转采校验商品数据
        //校验 经营状态-允许进货，流转途径 允许供应商送货
        //经营状态流转途径筛选后有效的部门商品
        Map<String, List<String>> effectiveDeptGoodsMap = new HashMap<>();

        checkGoods(deptGoodsMap, deptGoodsInfoMap, effectiveDeptGoodsMap);

        //不存在有效的供应商
        //查询合同商品
        //checkContractGoods(deptGoodsMap, deptGoodsInfoMap, effectiveDeptGoodsMap);

        //配送传入停靠点数据
        //批量根据门店查询所有店组群信息
        //上级店组群信息
        Map<String, QueryBatchDeptListResp.Rows> upDeptMap = Maps.newHashMap();

        DeliveryAppointmentStrategyResultDTO deliveryAppointmentStrategyResultDTO = mdDeliveryAppointmentStrategyDomainService.handleDock(upDeptMap, deptGoodsMap, categoryCodeSet, skuCodeSet, deliveryDeptCodeSet);

        //key:停靠点编码 val:停靠点信息
        Map<String, MdDeliveryDockStrategyPO> dockMap = deliveryAppointmentStrategyResultDTO.getDockStrategyPOList().stream().collect(Collectors.toMap(MdDeliveryDockStrategyPO::getDockCode, Function.identity()));

        //key:配送部门编码 + 部门类型 + 部门编码 val:停靠点编码
        Map<String, String> dockDeptMap = new HashMap<>();

        List<MdDeliveryDockDeptPO> dockDeptPOList = deliveryAppointmentStrategyResultDTO.getDockDeptPOList();
        for (MdDeliveryDockDeptPO mdDeliveryDockDeptPO : dockDeptPOList) {
            if (dockMap.containsKey(mdDeliveryDockDeptPO.getDockCode())) {
                MdDeliveryDockStrategyPO mdDeliveryDockStrategyPO = dockMap.get(mdDeliveryDockDeptPO.getDockCode());
                String key = mdDeliveryDockStrategyPO.getDeptCode() + "_" + mdDeliveryDockDeptPO.getDeptType() + "_" + mdDeliveryDockDeptPO.getDeptCode();
                dockDeptMap.put(key, mdDeliveryDockDeptPO.getDockCode());
            }
        }

        //key:配送部门编码 + 类型 + 商品/品类编码 val:停靠点编码
        Map<String, String> goodsMap = new HashMap<>();
        for (MdDeliveryDockGoodsCategoryPO mdDeliveryDockGoodsCategoryPO : deliveryAppointmentStrategyResultDTO.getDockGoodsCategoryPOList()) {
            if (dockMap.containsKey(mdDeliveryDockGoodsCategoryPO.getDockCode())) {
                MdDeliveryDockStrategyPO mdDeliveryDockStrategyPO = dockMap.get(mdDeliveryDockGoodsCategoryPO.getDockCode());
                //定义类型（1商品；2品类）
                Integer type = mdDeliveryDockGoodsCategoryPO.getType();
                String key = mdDeliveryDockStrategyPO.getDeptCode() + "_" + mdDeliveryDockGoodsCategoryPO.getType()
                        + "_" + (ObjectUtils.equals(1, type) ? mdDeliveryDockGoodsCategoryPO.getSkuCode() : mdDeliveryDockGoodsCategoryPO.getClassCode());
                goodsMap.put(key, mdDeliveryDockGoodsCategoryPO.getDockCode());
            }
        }


        //配转采分组方式：门店配送（非直流）的要货需求 可以判断是否转采购
        // 部门商品表：商品类型 + 商品编码  + 需求批次 + 送货方式
        // 配送出货方表：配送部门 + 停靠点 + 直流标记
        Map<String, List<DeliveryToPurchReq.DeliveryToPurchDeliveryInfo>> deliveryShipperMap = new HashMap<>();
        //上文循环已经写入分组字段了
        for (DeliveryToPurchReq.DeliveryToPurchDeliveryInfo deliveryToPurchDeliveryInfo : deliveryList) {

            //写入停靠点信息
            Set<String> dockSet = new HashSet<>();
            Map<String, String> deliveryCockMap = new HashMap<>();
            //配送部门编码
            String distDeptCode = deliveryToPurchDeliveryInfo.getDistDeptCode();

            //要货部门编码
            String deptCode = deliveryToPurchDeliveryInfo.getDeptGoodsDetail().getDeptCode();
            //1 店组群； 2 部门
            String dockKey = distDeptCode + "_2_" + deptCode;
            if (dockDeptMap.containsKey(dockKey)) {
                String s = dockDeptMap.get(dockKey);
                deliveryCockMap.put(s, s);
            }
            //先根据要货部门编码筛选停靠点
            if (upDeptMap.containsKey(deptCode)) {
                QueryBatchDeptListResp.Rows rows = upDeptMap.get(deptCode);
                for (QueryBatchDeptListResp.DeptGroup deptGroup : rows.getDeptGroupList()) {
                    dockKey = distDeptCode + "_1_" + deptGroup.getClassCode();
                    if (dockDeptMap.containsKey(dockKey)) {
                        String s = dockDeptMap.get(dockKey);
                        deliveryCockMap.put(s, s);

                    }
                }
            }
            //1商品；2品类
            String skuCode = deliveryToPurchDeliveryInfo.getDeptGoodsDetail().getSkuCode();
            dockKey = distDeptCode + "_1_" + skuCode;
            if (goodsMap.containsKey(dockKey)) {
                String s = goodsMap.get(dockKey);
                if (deliveryCockMap.containsKey(s)) {
                    dockSet.add(s);
                }
            }
            String categoryCodeAll = deliveryToPurchDeliveryInfo.getDeptGoodsDetail().getCategoryCodeAll();
            if (StringUtils.isNotEmpty(categoryCodeAll)) {
                String[] split = categoryCodeAll.split(",");
                if (split.length > 0) {
                    for (String categoryCode : split) {
                        dockKey = distDeptCode + "_2_" + categoryCode;
                        if (goodsMap.containsKey(dockKey)) {
                            String s = goodsMap.get(dockKey);
                            if (deliveryCockMap.containsKey(s)) {
                                dockSet.add(s);
                            }
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(dockSet)) {
                ArrayList<String> dockList = new ArrayList<>(dockSet);
                Collections.sort(dockList);
                String dockCode = dockList.get(0);
                deliveryToPurchDeliveryInfo.setDockCode(dockCode);
                MdDeliveryDockStrategyPO mdDeliveryDockStrategyPO = dockMap.get(dockCode);
                if (null != mdDeliveryDockStrategyPO) {
                    deliveryToPurchDeliveryInfo.setDockName(mdDeliveryDockStrategyPO.getDockName());
                }
            }

            String key = deliveryToPurchDeliveryInfo.getDeliveryToPurchKey();
            if (deliveryShipperMap.containsKey(key)) {
                deliveryShipperMap.get(key).add(deliveryToPurchDeliveryInfo);
            } else {
                List<DeliveryToPurchReq.DeliveryToPurchDeliveryInfo> list = new ArrayList<>();
                list.add(deliveryToPurchDeliveryInfo);
                deliveryShipperMap.put(key, list);
            }
        }

        //配转采数据
        List<DeliveryToPurchResp> deliveryToPurchRespList = new ArrayList<>();
        //配转采关联关系
        List<DeliveryToPurchRefResp> deliveryToPurchRefRespList = new ArrayList<>();
        //配转采来源数据
        List<DeliveryToPurchDeliverySourceResp> deliveryToPurchDeliverySourceRespList = new ArrayList<>();

        List<PmsDist2purchSupplierRecordPO> dist2purchSupplierRecordList = new ArrayList<>();
        //生成转采购数据 - 需要合并数量
        for (List<DeliveryToPurchReq.DeliveryToPurchDeliveryInfo> value : deliveryShipperMap.values()) {
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }

            DeliveryToPurchResp deliveryToPurchResp = new DeliveryToPurchResp();

            deliveryToPurchResp.setInsideId((long) (deliveryToPurchRespList.size() + 1));
            deliveryToPurchRespList.add(deliveryToPurchResp);

            int i = 0;
            for (DeliveryToPurchReq.DeliveryToPurchDeliveryInfo deliveryToPurchDeliveryInfo : value) {
                DeliveryToPurchRefResp deliveryToPurchRefResp = new DeliveryToPurchRefResp();
                deliveryToPurchRefResp.setDeliveryShipperInsideId(deliveryToPurchDeliveryInfo.getInsideId());
                deliveryToPurchRefResp.setDeliveryToPurchInsideId(deliveryToPurchResp.getInsideId());
                deliveryToPurchRefResp.setDeptGoodsInsideId(deliveryToPurchDeliveryInfo.getPinsideId());
                deliveryToPurchRefResp.setBillNo(deliveryToPurchDeliveryInfo.getBillNo());

                deliveryToPurchRefRespList.add(deliveryToPurchRefResp);

                DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo deliveryToPurchDeptGoodsInfo = deliveryToPurchDeptGoodsInfoMap.get(deliveryToPurchDeliveryInfo.getPinsideId());

                //不可转采逻辑
                deliveryToPurchRefResp.setType(ObjectUtils.equals(0, deliveryToPurchDeptGoodsInfo.getType()) ? 0 : 1);

                //查询最近的供应商
                if (ObjectUtils.equals(1, deliveryToPurchRefResp.getType())) {
                    PmsDist2purchSupplierRecordPO dist2purchSupplierRecordPO = new PmsDist2purchSupplierRecordPO();
                    dist2purchSupplierRecordPO.setDistDeptCode(deliveryToPurchDeliveryInfo.getDistDeptCode());
                    dist2purchSupplierRecordPO.setDockCode(deliveryToPurchDeliveryInfo.getDockCode());
                    dist2purchSupplierRecordPO.setSkuCode(deliveryToPurchDeptGoodsInfo.getSkuCode());
                    dist2purchSupplierRecordList.add(dist2purchSupplierRecordPO);

                    //查询商品订货包装率
                    GoodsStrageReq goodsStrageReq = new GoodsStrageReq();
                    goodsStrageReq.setDeptCode(deliveryToPurchDeliveryInfo.getDistDeptCode());
                    GoodsStrageReq.GoodsInfo goodsInfo = new GoodsStrageReq.GoodsInfo();
                    goodsInfo.setCategoryCodeAll(deliveryToPurchDeptGoodsInfo.getCategoryCodeAll());
                    goodsInfo.setSkuCode(deliveryToPurchDeptGoodsInfo.getSkuCode());
                    List<GoodsStrageReq.GoodsInfo> goodsInfos = new ArrayList<>();
                    goodsInfos.add(goodsInfo);
                    goodsStrageReq.setGoodsInfos(goodsInfos);
                    GoodsStrategyResp purchGoodsStrategy = iSupplychainPmsBizRuleEngineService.getPurchGoodsStrategy(goodsStrageReq);
                    if (null != purchGoodsStrategy && CollectionUtils.isNotEmpty(purchGoodsStrategy.getGoodsStrategyList())) {
                        GoodsStrategyResp.GoodsStrategy goodsStrategy = purchGoodsStrategy.getGoodsStrategyList().get(0);
                        deliveryToPurchResp.setPurchUnitRate(goodsStrategy.getPurchUnitRate());
                    }
                    else{
                        deliveryToPurchResp.setPurchUnitRate(BigDecimal.ONE);
                    }
                }
                if (0 == i) {
                    deliveryToPurchResp.setGoodsType(deliveryToPurchDeptGoodsInfo.getGoodsType());
                    deliveryToPurchResp.setSkuCode(deliveryToPurchDeptGoodsInfo.getSkuCode());
                    deliveryToPurchResp.setSkuModel(deliveryToPurchDeptGoodsInfo.getSkuModel());
                    deliveryToPurchResp.setSkuName(deliveryToPurchDeptGoodsInfo.getSkuName());
                    deliveryToPurchResp.setGoodsNo(deliveryToPurchDeptGoodsInfo.getGoodsNo());
                    deliveryToPurchResp.setCategoryCode(deliveryToPurchDeptGoodsInfo.getCategoryCode());
                    deliveryToPurchResp.setCategoryCodeAll(deliveryToPurchDeptGoodsInfo.getCategoryCodeAll());
                    deliveryToPurchResp.setCategoryName(deliveryToPurchDeptGoodsInfo.getCategoryName());
                    deliveryToPurchResp.setBarcode(deliveryToPurchDeptGoodsInfo.getBarcode());
                    deliveryToPurchResp.setUnit(deliveryToPurchDeptGoodsInfo.getUnit());
                    deliveryToPurchResp.setPackageUnit(deliveryToPurchDeptGoodsInfo.getPackageUnit());
                    deliveryToPurchResp.setInputTaxRate(deliveryToPurchDeptGoodsInfo.getInputTaxRate());
                    deliveryToPurchResp.setOutputTaxRate(deliveryToPurchDeptGoodsInfo.getOutputTaxRate());
                    deliveryToPurchResp.setDockCode(deliveryToPurchDeliveryInfo.getDockCode());
                    deliveryToPurchResp.setDockName(deliveryToPurchDeliveryInfo.getDockName());
                    deliveryToPurchResp.setDistDeptStockAtpQty(deliveryToPurchDeliveryInfo.getDistDeptStockAtpQty());
                    deliveryToPurchResp.setDistDeptStockRealQty(deliveryToPurchDeliveryInfo.getDistDeptStockRealQty());
                    deliveryToPurchResp.setPurchBatchNo(deliveryToPurchDeptGoodsInfo.getPurchBatchNo());
                    deliveryToPurchResp.setSendMode(deliveryToPurchDeptGoodsInfo.getSendMode());

                    deliveryToPurchResp.setDistDeptCode(deliveryToPurchDeliveryInfo.getDistDeptCode());
                    deliveryToPurchResp.setDistDeptName(deliveryToPurchDeliveryInfo.getDistDeptName());

                    //针对需求单里的 门店配送（非直流）的要货需求 可以判断是否转采购--即按照配送部门汇总转配送部门采购。
                    deliveryToPurchResp.setType(ObjectUtils.equals(0, deliveryToPurchDeptGoodsInfo.getType()) ? 0 : 1);
                    deliveryToPurchResp.setReason(deliveryToPurchDeptGoodsInfo.getReason());

                    if(ObjectUtils.equals(1,deliveryToPurchResp.getType())){
                        checkContractGoods(deliveryToPurchResp);
                    }

                    deliveryToPurchResp.setDeliveryUnitRate(deliveryToPurchDeliveryInfo.getDeliveryUnitRate());

                    deliveryToPurchResp.setDeliveryQty(deliveryToPurchDeliveryInfo.getDeliveryQty());
                } else {
                    deliveryToPurchResp.setDeliveryQty(deliveryToPurchResp.getDeliveryQty().add(deliveryToPurchDeliveryInfo.getDeliveryQty()));
                }

                List<DeliveryToPurchReq.OrderApplyInfo> orderApplyInfoList = deliveryToPurchDeptGoodsInfo.getOrderApplyInfoList();
                if (CollectionUtils.isNotEmpty(orderApplyInfoList)) {
                    for (DeliveryToPurchReq.OrderApplyInfo orderApplyInfo : orderApplyInfoList) {
                        DeliveryToPurchDeliverySourceResp deliveryToPurchDeliverySourceResp = new DeliveryToPurchDeliverySourceResp();
                        deliveryToPurchDeliverySourceResp.setDeptCode(deliveryToPurchDeptGoodsInfo.getDeptCode());
                        deliveryToPurchDeliverySourceResp.setDeptName(deliveryToPurchDeptGoodsInfo.getDeptName());
                        deliveryToPurchDeliverySourceResp.setDeliveryQty(orderApplyInfo.getQty());
                        deliveryToPurchDeliverySourceResp.setApplyBillNo(orderApplyInfo.getApplyBillNo());
                        deliveryToPurchDeliverySourceResp.setApplyInsideId(orderApplyInfo.getApplyInsideId());
                        deliveryToPurchDeliverySourceResp.setDeliveryToPurchInsideId(deliveryToPurchResp.getInsideId());
                        deliveryToPurchDeliverySourceResp.setCustomerCode(orderApplyInfo.getCustomerCode());
                        deliveryToPurchDeliverySourceResp.setCustomerName(orderApplyInfo.getCustomerName());

                        deliveryToPurchDeliverySourceRespList.add(deliveryToPurchDeliverySourceResp);
                    }
                } else {
                    DeliveryToPurchDeliverySourceResp deliveryToPurchDeliverySourceResp = new DeliveryToPurchDeliverySourceResp();
                     deliveryToPurchDeliverySourceResp.setDeptCode(deliveryToPurchDeptGoodsInfo.getDeptCode());
                    deliveryToPurchDeliverySourceResp.setDeptName(deliveryToPurchDeptGoodsInfo.getDeptName());
                    deliveryToPurchDeliverySourceResp.setDeliveryQty(deliveryToPurchDeptGoodsInfo.getDeliveryQty());
                    deliveryToPurchDeliverySourceResp.setApplyBillNo("");
                    deliveryToPurchDeliverySourceResp.setApplyInsideId(null);
                    deliveryToPurchDeliverySourceResp.setDeliveryToPurchInsideId(deliveryToPurchResp.getInsideId());
                    deliveryToPurchDeliverySourceResp.setCustomerCode("");
                    deliveryToPurchDeliverySourceResp.setCustomerName("");

                    deliveryToPurchDeliverySourceRespList.add(deliveryToPurchDeliverySourceResp);
                }


                i++;
            }
        }

        if (CollectionUtils.isNotEmpty(dist2purchSupplierRecordList)) {
            List<PmsDist2purchSupplierRecordPO> pmsDist2purchSupplierRecordPOS = pmsDist2purchSupplierRecordRepositoryService.getPmsDist2purchSupplierRecordMapper().list4LastSupplier(dist2purchSupplierRecordList);
            Map<String, PmsDist2purchSupplierRecordPO> dist2purchSupplierRecordMap = new HashMap<>();
            for (PmsDist2purchSupplierRecordPO pmsDist2purchSupplierRecordPO : pmsDist2purchSupplierRecordPOS) {
                String key = pmsDist2purchSupplierRecordPO.getDistDeptCode() + "_" + pmsDist2purchSupplierRecordPO.getDockCode() + "_" + pmsDist2purchSupplierRecordPO.getSkuCode();
                dist2purchSupplierRecordMap.put(key, pmsDist2purchSupplierRecordPO);
            }

            for (DeliveryToPurchResp deliveryToPurchResp : deliveryToPurchRespList) {
                String key = deliveryToPurchResp.getDistDeptCode() + "_" + deliveryToPurchResp.getDockCode() + "_" + deliveryToPurchResp.getSkuCode();
                if (dist2purchSupplierRecordMap.containsKey(key)) {
                    PmsDist2purchSupplierRecordPO pmsDist2purchSupplierRecordPO = dist2purchSupplierRecordMap.get(key);
                    deliveryToPurchResp.setLastSupplierCode(pmsDist2purchSupplierRecordPO.getSupplierCode());
                    deliveryToPurchResp.setLastSupplierName(pmsDist2purchSupplierRecordPO.getSupplierName());
                }
            }
        }


        Map<Long, List<DeliveryToPurchRefResp>> deliveryToPurchRefMap = deliveryToPurchRefRespList.parallelStream().collect(Collectors.groupingBy(DeliveryToPurchRefResp::getDeliveryToPurchInsideId));

        Map<Long, List<DeliveryToPurchDeliverySourceResp>> deliveryToPurchDeliverySourceMap = deliveryToPurchDeliverySourceRespList.parallelStream().collect(Collectors.groupingBy(DeliveryToPurchDeliverySourceResp::getDeliveryToPurchInsideId));

        DeliveryToPurchResultResp resp = new DeliveryToPurchResultResp();
        resp.setDeliveryToPurchRespList(deliveryToPurchRespList);
        resp.setDeliveryToPurchDeliverySourceMap(deliveryToPurchDeliverySourceMap);
        resp.setDeliveryToPurchRefMap(deliveryToPurchRefMap);

        return resp;

    }

    private void checkContractGoods(DeliveryToPurchResp deliveryToPurchResp) {

        String deptCode = deliveryToPurchResp.getDistDeptCode();
        ContractGoodsDeptQueryDTO contractGoodsDeptQuery = new ContractGoodsDeptQueryDTO();
        contractGoodsDeptQuery.setDeptCode(deptCode);
        List<String> skuCodeList = new ArrayList<>();
        skuCodeList.add(deliveryToPurchResp.getSkuCode());
        contractGoodsDeptQuery.setSkuCodeList(skuCodeList);
        List<ContractGoodsDeptDTO> contractGoodsDeptDTOList = iSupplychainPmsBizRuleEngineService.listContractGoodsDept(contractGoodsDeptQuery);

        if(CollectionUtils.isEmpty(contractGoodsDeptDTOList)){
            deliveryToPurchResp.setType(0);
            deliveryToPurchResp.setReason(PmsErrorCodeEnum.SC_PMS_008_B002.getDesc());
        }

    }

    private void checkGoods(Map<String, List<String>> deptGoodsMap, Map<String, DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo> deptGoodsInfoMap, Map<String, List<String>> effectiveDeptGoodsMap) {
        Iterator<String> iterator = deptGoodsMap.keySet().iterator();
        while (iterator.hasNext()) {
            String deptCode = iterator.next();

            GoodsQueryDTO goodsQueryDTO = new GoodsQueryDTO();
            goodsQueryDTO.setDeptCode(deptCode);
            goodsQueryDTO.setSkuCodeList(deptGoodsMap.get(deptCode));
            goodsQueryDTO.setAttributeNameFlag(true);
            List<GoodsManageAndCirculationDTO> goodsManageAndCirculation = iSupplychainPmsBizRuleEngineService.getGoodsManageAndCirculation(goodsQueryDTO);

            List<String> skuCodeList = new ArrayList<>();

            for (GoodsManageAndCirculationDTO goodsManageAndCirculationDTO : goodsManageAndCirculation) {
                if (!(ObjectUtils.equals(1, goodsManageAndCirculationDTO.getWorkStatusDTO().getAllowPurchaseFlag())
                        && ObjectUtils.equals(1, goodsManageAndCirculationDTO.getCirculationModeDTO().getAllowFromSupplierFlag()))) {
                    String key = deptCode + "_" + goodsManageAndCirculationDTO.getSkuCode();
                    DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo deliveryToPurchDeptGoodsInfo = deptGoodsInfoMap.get(key);
                    if (null != deliveryToPurchDeptGoodsInfo) {
                        deliveryToPurchDeptGoodsInfo.setType(0);
                        deliveryToPurchDeptGoodsInfo.setReason(PmsErrorCodeEnum.SC_PMS_008_B001.getDesc());
                    }
                } else {
                    skuCodeList.add(goodsManageAndCirculationDTO.getSkuCode());
                }
            }
            if (CollectionUtils.isNotEmpty(skuCodeList)) {
                effectiveDeptGoodsMap.put(deptCode, skuCodeList);
            }

        }
    }

    private void checkContractGoods(Map<String, List<String>> deptGoodsMap, Map<String, DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo> deptGoodsInfoMap, Map<String, List<String>> effectiveDeptGoodsMap) {
        Iterator<String> it = effectiveDeptGoodsMap.keySet().iterator();
        while (it.hasNext()) {
            String deptCode = it.next();
            ContractGoodsDeptQueryDTO contractGoodsDeptQuery = new ContractGoodsDeptQueryDTO();
            contractGoodsDeptQuery.setDeptCode(deptCode);
            contractGoodsDeptQuery.setSkuCodeList(deptGoodsMap.get(deptCode));
            List<ContractGoodsDeptDTO> contractGoodsDeptDTOList = iSupplychainPmsBizRuleEngineService.listContractGoodsDept(contractGoodsDeptQuery);

            Map<String, ContractGoodsDeptDTO> contractGoodsDeptMap = contractGoodsDeptDTOList.stream().collect(Collectors.toMap(ContractGoodsDeptDTO::getSkuCode, Function.identity(), (value1, value2) -> value2));

            for (String skuCode : deptGoodsMap.get(deptCode)) {
                if (!contractGoodsDeptMap.containsKey(skuCode)) {
                    String key = deptCode + "_" + skuCode;
                    DeliveryToPurchReq.DeliveryToPurchDeptGoodsInfo deliveryToPurchDeptGoodsInfo = deptGoodsInfoMap.get(key);
                    if (null != deliveryToPurchDeptGoodsInfo) {
                        deliveryToPurchDeptGoodsInfo.setType(0);
                        deliveryToPurchDeptGoodsInfo.setReason(PmsErrorCodeEnum.SC_PMS_008_B002.getDesc());
                    }
                }
            }
        }
    }

    private static class GroupingItem {
        final PmsDemandDeliveryToPurchReq req;
        final PmsDemandPurchShipperReq shipper;

        public GroupingItem(PmsDemandDeliveryToPurchReq req, PmsDemandPurchShipperReq shipper) {
            this.req = req;
            this.shipper = shipper;
        }
    }

    public PurchaseOrderSummaryResp generatePurchaseOrderPreview(List<PmsDemandDeliveryToPurchReq> deliveryToPurchList,
                                                                 LocalDate effectiveDate, LocalDate deliveryDate, String purchaseRemark) {
        PurchaseOrderSummaryResp summary = new PurchaseOrderSummaryResp();
        //  参数校验
//        checkData(deliveryToPurchList, effectiveDate, deliveryDate);
        // 1. 分类统计商品
        classifyItems(deliveryToPurchList, summary);
        // 2. 过滤有效转采项
        List<GroupingItem> validItems = filterConvertibleItems(deliveryToPurchList);
        // 3. 按规则分组
        Map<GroupKey, List<GroupingItem>> groupedItems = groupItems(validItems);
        // 4. 生成预览订单
        List<PurchaseBillChangeReq> orders = generatePreviewOrders(groupedItems, effectiveDate, deliveryDate, purchaseRemark);
        // 5. 设置汇总信息
        summary.setPurchaseOrders(orders);
        summary.setTotalPurchaseOrders(orders.size());
        // 6. 计算总数量和金额
        summary.setTotalPurchaseQuantity(validItems.stream().map(item -> item.req.getDtpQty()).reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setTotalPurchaseAmount(validItems.stream().map(item -> item.shipper.getPurchPrice().multiply(item.req.getDtpQty())).reduce(BigDecimal.ZERO, BigDecimal::add));
        return summary;
    }

    private void checkData(List<PmsDemandDeliveryToPurchReq> deliveryToPurchList, LocalDate effectiveDate, LocalDate deliveryDate) {
        //获取商品skuCode集合
        Set<String> skuCodeSet = deliveryToPurchList.stream().map(PmsDemandDeliveryToPurchReq::getSkuCode).collect(Collectors.toSet());
        List<GoodsQueryResp> queryRespList = iSupplychainPmsBizRuleEngineService.listGoodsInfo(GoodsQueryDTO.builder().skuCodeList(Lists.newArrayList(skuCodeSet)).build());
        Assert.isTrue(CollectionUtils.isNotEmpty(queryRespList), PmsErrorCodeEnum.SC_PMS_005_B001);
        //出参商品中不包含入参的商品skuCode，则报错提示
        //不存在的商品集合
        Set<String> notExistSkuCodeSet = queryRespList.stream().filter(queryResp -> !skuCodeSet.contains(queryResp.getGoodsInfo().getSkuCode())).map(queryResp -> queryResp.getGoodsInfo().getSkuCode()).collect(Collectors.toSet());
        //比较skuCodeSet与queryRespList，判断queryRespList的商品是否一致，不一致则报错
        Assert.isTrue(CollectionUtils.isEmpty(notExistSkuCodeSet), PmsErrorCodeEnum.SC_PMS_005_B001.getErrorCode(), String.format("商品不存在：%s", String.join(",", notExistSkuCodeSet)));

    }

    private void classifyItems(List<PmsDemandDeliveryToPurchReq> list, PurchaseOrderSummaryResp summary) {
        int totalItems = list.size();
        int convertibleItems = 0;
        int notConvertedItems = 0;
        int nonConvertibleItems = 0;

        for (PmsDemandDeliveryToPurchReq req : list) {
            if (req.getType() == 0) {
                nonConvertibleItems++;
            } else if (req.getDtpQty()!=null && req.getDtpQty().compareTo(BigDecimal.ZERO) > 0) {
                convertibleItems++;
            } else {
                notConvertedItems++;
            }
        }

        summary.setTotalItems(totalItems);
        summary.setConvertibleItems(convertibleItems);
        summary.setNotConvertedItems(notConvertedItems);
        summary.setNonConvertibleItems(nonConvertibleItems);
    }

    private List<GroupingItem> filterConvertibleItems(List<PmsDemandDeliveryToPurchReq> list) {
        return list.stream()
                .filter(req -> req.getType() == 1) // 可转采
                .filter(req -> req.getDtpQty()!=null && req.getDtpQty().compareTo(BigDecimal.ZERO) > 0) // 转采数量>0
                .flatMap(req -> req.getPurchShipperList().stream()
                        .filter(shipper -> shipper.getStatus() == 1) // 已选中供应商
                        .map(shipper -> new GroupingItem(req, shipper)))
                .collect(Collectors.toList());
    }

    private Map<GroupKey, List<GroupingItem>> groupItems(List<GroupingItem> items) {
        return items.stream()
                .collect(Collectors.groupingBy(item -> new GroupKey(
                        item.shipper.getSupplierCode(),
                        item.shipper.getContractNo(),
                        item.req.getDockCode(),
                        item.req.getPurchBatchNo(),
                        item.req.getSendMode()
                )));
    }

    private List<PurchaseBillChangeReq> generatePreviewOrders(Map<GroupKey, List<GroupingItem>> groupedItems,
                                                              LocalDate effectiveDate, LocalDate deliveryDate, String purchaseRemark) {
        return groupedItems.entrySet().stream().map(entry -> {
            GroupKey key = entry.getKey();
            List<GroupingItem> items = entry.getValue();
            // 创建预览订单
            PurchaseBillChangeReq order = new PurchaseBillChangeReq();
            // 设置表头信息
            setHeaderInfo(order, key, items, effectiveDate, deliveryDate, purchaseRemark);
            // 设置明细信息
            setDetailsInfo(order, items);
            return order;
        }).collect(Collectors.toList());
    }

    private void setHeaderInfo(PurchaseBillChangeReq order, GroupKey key, List<GroupingItem> items, LocalDate effectiveDate,
                               LocalDate deliveryDate, String purchaseRemark) {
        // 获取第一条记录作为代表信息
        PmsDemandDeliveryToPurchReq firstReq = items.get(0).req;
        PmsDemandPurchShipperReq firstShipper = items.get(0).shipper;
        // 供应商信息
        order.setSupplierCode(key.getSupplierCode());
        order.setSupplierName(firstShipper.getSupplierName());
        // 部门信息
        order.setDeptCode(firstReq.getDistDeptCode());
        order.setDeptName(firstReq.getDistDeptName());
        // 固化信息
        order.setBillDirection(PmsBillDirectionEnum.NORMAL.getCode());
        order.setDirectSign(DirectSignEnum.NOT_DIRECT.getCode());
        order.setBillType(1);
        order.setBillSource(PmsPurchaseOrderSourceEnum.DELIVERY.getCode());
        order.setSrcBillNo(null);
        order.setContractNo(key.getContractNo());
        order.setDeliverDate(deliveryDate);
        order.setSendMode(key.getDeliveryMethod());
        order.setValidityDate(effectiveDate);
        order.setDockCode(key.getDockCode());
        order.setDockName(items.get(0).req.getDockName());
        order.setPurchRemark(purchaseRemark);
        order.setOrderAttributeCode(null);
        order.setPurchBatchNo(firstReq.getPurchBatchNo());

        // 计算商品品项数
        long distinctItems = items.stream().map(item -> item.req.getSkuCode()).distinct().count();
        order.setTotalSkuCount((int) distinctItems);

        // 计算采购数量和税金
        BigDecimal totalQty = items.stream().map(item -> item.req.getDtpQty()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalTax = items.stream().map(item -> item.shipper.getPurchTax()).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalTaxMoney = items.stream().map(item -> item.shipper.getPurchMoney()).reduce(BigDecimal.ZERO, BigDecimal::add);
        order.setTotalQty(totalQty);
        order.setTotalTax(totalTax);
        order.setTotalTaxMoney(totalTaxMoney);
    }

    private void setDetailsInfo(PurchaseBillChangeReq order, List<GroupingItem> items) {
        List<PurchaseBillDetailCreateReq> details = items.stream()
                .map(item -> createDetail(item.req, item.shipper))
                .collect(Collectors.toList());
        order.setDetailList(details);
    }

    private PurchaseBillDetailCreateReq createDetail(PmsDemandDeliveryToPurchReq req,
                                                     PmsDemandPurchShipperReq shipper) {
        PurchaseBillDetailCreateReq detail = new PurchaseBillDetailCreateReq();
        AtomicLong insideId = new AtomicLong(1);
        detail.setInsideId(insideId.getAndIncrement());
        // 商品基础信息
        detail.setSkuType(req.getGoodsType());
        detail.setSkuCode(req.getSkuCode());
        detail.setSkuName(req.getSkuName());
        detail.setBarcode(req.getBarcode());
        detail.setGoodsNo(req.getGoodsNo());
        detail.setCategoryCode(req.getCategoryCode());
        detail.setCategoryName(req.getCategoryName());
        detail.setBasicUnit(req.getUnit());
        detail.setPackageUnit(req.getPackageUnit());
        detail.setSkuModel(req.getSkuModel());
        detail.setInputTaxRate(req.getInputTaxRate());
        detail.setOutputTaxRate(req.getOutputTaxRate());

        // 包装率
        detail.setUnitRate(req.getDeliveryUnitRate()); // 商品包装率（配送包装率）
        detail.setPurchUnitRate(shipper.getPurchUnitRate()); // 订货包装率（采购包装率）

        // 价格信息
        detail.setPromotePeriodPrice(shipper.getPromotePeriodPrice());
        detail.setPromoteActivityCode(shipper.getPromoteActivityCode());
        detail.setPromoteActivityName(shipper.getPromoteActivityName());
        detail.setContractSpecialPrice(shipper.getContractSpecialPrice());
        detail.setContractPrice(shipper.getContractPurchPrice());
        detail.setContractMaxPrice(shipper.getContractMaxPurchPrice());
        detail.setLastPurchPrice(shipper.getLastPurchPrice());
        detail.setPurchPrice(shipper.getPurchPrice());

        // 数量信息
        detail.setWholeQty(req.getDtpWholeQty());
        detail.setOddQty(req.getDtpOddQty());
        detail.setPurchQty(req.getDtpQty());

        // 金额计算
        BigDecimal amount = shipper.getPurchPrice().multiply(req.getDtpQty());
        detail.setPurchMoney(amount);
        detail.setPurchTax(shipper.getPurchTax());
        return detail;
    }

    private List<DeliveryToPurchReq.DeliveryToPurchDeliveryInfo> convertDeliveryInfo(WdDeliveryBillDetailPO deliveryBillDetail) {
        return Lists.newArrayList(
                DeliveryToPurchReq.DeliveryToPurchDeliveryInfo.builder()
                        .billNo(deliveryBillDetail.getBillNo())
                        .insideId(deliveryBillDetail.getInsideId())
                        .pinsideId(deliveryBillDetail.getInsideId())
                        .dockCode(deliveryBillDetail.getDockCode())
                        .distDeptCode(deliveryBillDetail.getWhCode())
                        .distDeptName(deliveryBillDetail.getWhName())
                        .deliveryUnitRate(deliveryBillDetail.getUnitRate())
                        .directSign(deliveryBillDetail.getDirectSign())
                        .deliveryQty(deliveryBillDetail.getDeliveryQty())
                        .distDeptStockAtpQty(deliveryBillDetail.getOutDeptAtpQty())
                        .distDeptStockRealQty(deliveryBillDetail.getOutDeptStkQty())
                        .build()
        );
    }
}
