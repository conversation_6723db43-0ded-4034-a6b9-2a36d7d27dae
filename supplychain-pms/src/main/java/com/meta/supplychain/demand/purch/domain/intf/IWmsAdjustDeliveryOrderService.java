package com.meta.supplychain.demand.purch.domain.intf;

import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustBaseReq;
import com.meta.supplychain.entity.dto.wds.req.WdsDeliveryOrderAdjustReq;
import com.meta.supplychain.entity.dto.wds.resp.WdsDeliveryOrderAdjustResp;

import java.util.List;

/**
 * 配送订单调整
 *
 */
public interface IWmsAdjustDeliveryOrderService {


    List<WdsDeliveryOrderAdjustResp> adjustDeliveryOrder(List<WdsDeliveryOrderAdjustBaseReq> req, OpInfo operatorInfo);
}
