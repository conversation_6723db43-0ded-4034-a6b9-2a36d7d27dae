package com.meta.supplychain.demand.purch;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * <AUTHOR>
 */
@MapperScan("com.meta.supplychain.infrastructure.repository.mapper.*")
@ComponentScan("com.meta.supplychain.*")
@EnableFeignClients("com.meta.supplychain.*")
@SpringBootApplication
@EnableAspectJAutoProxy
public class SupplychainPmsApplication {

    public static void main(String[] args) {
        SpringApplication.run(SupplychainPmsApplication.class, args);
    }

}