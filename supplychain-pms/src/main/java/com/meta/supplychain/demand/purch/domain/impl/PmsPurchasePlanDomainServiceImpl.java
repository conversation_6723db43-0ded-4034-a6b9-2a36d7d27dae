package com.meta.supplychain.demand.purch.domain.impl;

import cn.linkkids.framework.croods.common.PageResult;
import cn.linkkids.framework.croods.common.exception.BizExceptions;
import cn.linkkids.framework.croods.common.json.Jsons;
import cn.linkkids.framework.croods.common.logger.Logs;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meta.supplychain.common.component.service.intf.ISupplychainControlEngineService;
import com.meta.supplychain.common.component.service.intf.commonbiz.ICommonGoodsService;
import com.meta.supplychain.convert.pms.PurchasePlanBillConvert;
import com.meta.supplychain.demand.purch.domain.intf.PmsPurchasePlanDomainService;
import com.meta.supplychain.entity.dto.OpInfo;
import com.meta.supplychain.entity.dto.goods.resp.GoodsSimpleInfo;
import com.meta.supplychain.entity.dto.pms.req.purch.*;
import com.meta.supplychain.entity.dto.pms.resp.purch.*;
import com.meta.supplychain.entity.po.md.MdContractMasPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanBillPO;
import com.meta.supplychain.entity.po.pms.PmsPurchasePlanDetailPO;
import com.meta.supplychain.enums.goods.GoodsSaleModeEnum;
import com.meta.supplychain.enums.goods.MeasurePropertyEnum;
import com.meta.supplychain.enums.goods.SkuTypeEnum;
import com.meta.supplychain.enums.md.MdBillNoBillTypeEnum;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.enums.pms.PmsPurchPlanStatusEnum;
import com.meta.supplychain.infrastructure.repository.service.intf.md.IMdContractMasRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchasePlanDetailRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsPurchasePlanRepositoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PmsPurchasePlanDomainServiceImpl implements PmsPurchasePlanDomainService {

    @Resource
    private ICommonGoodsService commonGoodsService;

    @Resource
    private ISupplychainControlEngineService supplychainControlEngineService;

    @Resource
    private PmsPurchasePlanRepositoryService purchasePlanRepositoryService;

    @Autowired
    private IMdContractMasRepositoryService mdContractMasRepositoryService;

    @Resource
    private PmsPurchasePlanDetailRepositoryService purchasePlanDetailRepositoryService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PurchPlanBillOptResp savePlanBill(PurchasePlanBillReq purchasePlanBillReq) {
        Logs.info("保存采购计划单入参:{}", Jsons.toJson(purchasePlanBillReq));
        PurchPlanBillOptResp purchPlanBillOptResp = new PurchPlanBillOptResp();
        // 组装表头数据
        PmsPurchasePlanBillPO savePo = PurchasePlanBillConvert.INSTANCE.convertReqToPo(purchasePlanBillReq);
        // 是否新增
        Boolean saveFlag = purchasePlanBillReq.getId() == null;
        if (saveFlag) {
            // 获取单号
            String billNo = supplychainControlEngineService.getSupplychainBizBillRuleService().getBillNo(MdBillNoBillTypeEnum.PMS_PURCHASE_PLAN,purchasePlanBillReq.getDeptCode());
            savePo.setBillNo(billNo);
        }

        purchPlanBillOptResp.setBillNo(savePo.getBillNo());
        List<String> skuCodeList = purchasePlanBillReq.getDetailList()
                .stream().map(PurchasePlanBillDetailReq::getSkuCode).distinct().collect(Collectors.toList());
        BigDecimal sumAmount = purchasePlanBillReq.getDetailList().stream().map(PurchasePlanBillDetailReq::getPurchQty).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal sumMoney = purchasePlanBillReq.getDetailList().stream().map(PurchasePlanBillDetailReq::getPurchMoney).reduce(BigDecimal.ZERO,BigDecimal::add);
        BigDecimal sumTax = purchasePlanBillReq.getDetailList().stream().map(PurchasePlanBillDetailReq::getPurchTax).reduce(BigDecimal.ZERO,BigDecimal::add);
        savePo.setTotalSkuCount(skuCodeList.size());
        savePo.setTotalQty(sumAmount);
        savePo.setTotalTaxMoney(sumMoney);
        savePo.setTotalTax(sumTax);

        // 暂存
        if (purchasePlanBillReq.getOpType().equals(1)) {
            savePo.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_DRAFT.getCode());
        } else if(purchasePlanBillReq.getOpType().equals(2)) {
            savePo.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_PENDING_AUDIT.getCode());
        } else if (purchasePlanBillReq.getOpType().equals(3)) {
            savePo.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode());
            savePo.setAuditRemark(purchasePlanBillReq.getAuditRemark());
            savePo.setAuditCode(purchasePlanBillReq.getOperatorInfo().getOperatorCode());
            savePo.setAuditName(purchasePlanBillReq.getOperatorInfo().getOperatorName());
            savePo.setAuditTime(LocalDateTime.now());
        }

        // 组装明细数据
        Long inside = 1L;
        List<PmsPurchasePlanDetailPO> detailPos = Lists.newArrayList();
        for (PurchasePlanBillDetailReq detailReq : purchasePlanBillReq.getDetailList()) {
            PmsPurchasePlanDetailPO detailPo = PurchasePlanBillConvert.INSTANCE.convertDetailReqToPo(detailReq);
            detailPo.setBillNo(savePo.getBillNo());
            detailPo.setDeptCode(savePo.getDeptCode());
            detailPo.setDeptName(savePo.getDeptName());
            detailPo.setSupplierCode(savePo.getSupplierCode());
            detailPo.setSupplierName(savePo.getSupplierName());
            detailPo.setInsideId(inside++);
            detailPo.setPlanReqQty(detailReq.getPurchQty());
            detailPos.add(detailPo);
        }

        if (saveFlag) {
            purchasePlanRepositoryService.save(savePo);
            purchasePlanDetailRepositoryService.saveBatch(detailPos);
        } else {
            purchasePlanRepositoryService.updateById(savePo);
            LambdaQueryWrapper<PmsPurchasePlanDetailPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanDetailPO>()
                    .eq(PmsPurchasePlanDetailPO::getBillNo, purchasePlanBillReq.getBillNo());
            purchasePlanDetailRepositoryService.getBaseMapper().delete(queryWrapper);
            purchasePlanDetailRepositoryService.saveBatch(detailPos);
        }
        return purchPlanBillOptResp;
    }

    @Override
    public Boolean checkAndSet(PurchasePlanBillReq purchasePlanBillReq) {
        Logs.info("校验采购计划单入参:{}", Jsons.toJson(purchasePlanBillReq));
        Boolean saveFlag = null == purchasePlanBillReq.getId();
        if (!saveFlag) {
            LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                    .eq(null != purchasePlanBillReq.getId(), PmsPurchasePlanBillPO::getId, purchasePlanBillReq.getId())
                    .eq(StringUtils.isNotEmpty(purchasePlanBillReq.getBillNo()), PmsPurchasePlanBillPO::getBillNo, purchasePlanBillReq.getBillNo());
            PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);
            if (null == po) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P004);
            }
        }

        // 校验日期
        LocalDate currentDate = LocalDate.now();
        if (purchasePlanBillReq.getValidityDate().isBefore(currentDate)) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P001);
        }

        //校验合同信息
        MdContractMasPO mdContractMasPO = new MdContractMasPO();
        mdContractMasPO.setContractNo(purchasePlanBillReq.getContractNo());
        mdContractMasPO.setTenantId(Long.valueOf(TenantContext.get()));
        MdContractMasPO dbConcract = mdContractMasRepositoryService.getMdContractMasMapper().selectMaxContractSerial(mdContractMasPO);
        if (null == dbConcract) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B006);
        }

        // 获取商品信息
        Set<String> goodsSet = purchasePlanBillReq.getDetailList()
                .stream().map(PurchasePlanBillDetailReq::getSkuCode).collect(Collectors.toSet());
        List<GoodsSimpleInfo> skuList = commonGoodsService.skuSimpleList(new ArrayList<>(goodsSet), "true");
        if (CollectionUtils.isEmpty(skuList)) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B001);
        }
        Map<String, GoodsSimpleInfo> skuMap = skuList.stream()
                .collect(Collectors.toMap(GoodsSimpleInfo::getSkuCode, obj -> obj, (obj1, obj2) -> obj1));
        Set<String> skuSet = Sets.newHashSet();

        for  (int i = 0; i < purchasePlanBillReq.getDetailList().size(); i++) {
            PurchasePlanBillDetailReq item = purchasePlanBillReq.getDetailList().get(i);
            if (!skuMap.containsKey(item.getSkuCode())) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B002, new String[]{item.getSkuCode()});
            }
            GoodsSimpleInfo goods = skuMap.get(item.getSkuCode());
            if (item.getPurchPrice().compareTo(BigDecimal.ZERO) < 0) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B003);
            }

            if (!item.getSkuType().equals(SkuTypeEnum.NORMAL.getCode())
                    || item.getSaleMode().equals(GoodsSaleModeEnum.JOINT_OPERATION_WITH_STOCK.getCode())) {
                item.setPurchPrice(new BigDecimal(0));
            }

            if (item.getWholeQty().stripTrailingZeros().scale() > 0) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B007);
            }

            if (!goods.getMesureProperty().equals(MeasurePropertyEnum.WEIGH.getCode())) {
                if (item.getOddQty().stripTrailingZeros().scale() > 0
                        || item.getPurchQty().stripTrailingZeros().scale() > 0) {
                    BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B004);
                }
            }
            if (item.getPurchQty().compareTo(BigDecimal.ZERO) < 0
                    || item.getPurchQty().compareTo(new BigDecimal(9999999)) > 0) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B005);
            }
            if (skuSet.contains(item.getSkuCode() + "_" + item.getSkuType())) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0023);
            } else {
                skuSet.add(item.getSkuCode() + "_" + item.getSkuType());
            }
        }
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public List<PurchPlanBillOptResp> batchAuditBill(BatchPurchasePlanBillReq batchPurchasePlanBillReq) {
        List<PurchPlanBillOptResp> resps = new ArrayList<>();
        for (String billNo : batchPurchasePlanBillReq.getBillNumbers()) {
            PurchPlanBillOptResp resp = new PurchPlanBillOptResp();
            resps.add(resp);
            resp.setBillNo(billNo);
            LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                    .eq(PmsPurchasePlanBillPO::getBillNo, billNo);
            PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);

            if (null == po) {
                resp.setFailedReason(PmsErrorCodeEnum.SC_PMS_005_P004.getErrorMsg());
                continue;
            }

            if (!Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_PENDING_AUDIT.getCode())) {
                resp.setFailedReason(PmsErrorCodeEnum.SC_PMS_005_B0024.getErrorMsg());
                continue;
            }

            po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode());
            po.setAuditCode(batchPurchasePlanBillReq.getOperatorInfo().getOperatorCode());
            po.setAuditName(batchPurchasePlanBillReq.getOperatorInfo().getOperatorName());
            po.setAuditTime(LocalDateTime.now());
            po.setAuditRemark(batchPurchasePlanBillReq.getRemark());
            purchasePlanRepositoryService.getBaseMapper().updateById(po);
        }

        return resps;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PurchPlanBillOptResp auditBill(PurchasePlanMakeReq purchasePlanMakeReq) {
        PurchPlanBillOptResp resp = new PurchPlanBillOptResp();
        resp.setBillNo(purchasePlanMakeReq.getBillNo());
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                .eq(PmsPurchasePlanBillPO::getBillNo, purchasePlanMakeReq.getBillNo());
        PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == po) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P004);
        }

        if (!Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_PENDING_AUDIT.getCode())
                && !Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_DRAFT.getCode())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B008);
        }

        po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode());
        po.setAuditCode(purchasePlanMakeReq.getOperatorInfo().getOperatorCode());
        po.setAuditName(purchasePlanMakeReq.getOperatorInfo().getOperatorName());
        po.setAuditTime(LocalDateTime.now());
        po.setAuditRemark(purchasePlanMakeReq.getRemark());
        purchasePlanRepositoryService.getBaseMapper().updateById(po);
        return resp;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PurchPlanBillOptResp cancelBill(PurchasePlanMakeReq purchasePlanMakeReq) {
        PurchPlanBillOptResp resp = new PurchPlanBillOptResp();
        resp.setBillNo(purchasePlanMakeReq.getBillNo());
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                .eq(PmsPurchasePlanBillPO::getBillNo, purchasePlanMakeReq.getBillNo());
        PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == po) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P004);
        }

        if (!Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_DRAFT.getCode())
                && !Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_PENDING_AUDIT.getCode())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B009);
        }

        po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_CANCELLED.getCode());
        po.setCancelManCode(purchasePlanMakeReq.getOperatorInfo().getOperatorCode());
        po.setCancelManName(purchasePlanMakeReq.getOperatorInfo().getOperatorName());
        po.setCancelTime(LocalDateTime.now());
        po.setCancelRemark(purchasePlanMakeReq.getRemark());
        purchasePlanRepositoryService.getBaseMapper().updateById(po);
        return resp;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PurchPlanBillOptResp expireBill(PurchasePlanMakeReq purchasePlanMakeReq) {
        PurchPlanBillOptResp resp = new PurchPlanBillOptResp();
        resp.setBillNo(purchasePlanMakeReq.getBillNo());
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                .eq(PmsPurchasePlanBillPO::getBillNo, purchasePlanMakeReq.getBillNo());
        PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == po) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P004);
        }

        if (!Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode())
                && !Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_PART_ACCEPT.getCode())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0010);
        }

        po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_EXPIRED.getCode());
        purchasePlanRepositoryService.getBaseMapper().updateById(po);
        return resp;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PurchPlanBillOptResp closeBill(PurchasePlanMakeReq purchasePlanMakeReq) {
        PurchPlanBillOptResp resp = new PurchPlanBillOptResp();
        resp.setBillNo(purchasePlanMakeReq.getBillNo());
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                .eq(PmsPurchasePlanBillPO::getBillNo, purchasePlanMakeReq.getBillNo());
        PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);

        if (null == po) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P004);
        }

        if (!Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode())
                && !Objects.equals(po.getStatus(), PmsPurchPlanStatusEnum.PURCH_STATUS_PART_ACCEPT.getCode())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0021);
        }

        po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_CANCELLED.getCode());
        po.setCancelManCode(purchasePlanMakeReq.getOperatorInfo().getOperatorCode());
        po.setCancelManName(purchasePlanMakeReq.getOperatorInfo().getOperatorName());
        po.setCancelTime(LocalDateTime.now());
        po.setCancelRemark(purchasePlanMakeReq.getRemark());
        purchasePlanRepositoryService.getBaseMapper().updateById(po);
        return resp;
    }

    @Override
    public PageResult<PmsPurchasePlanBillResp> pagePlanList(QueryPurchasePlanBillReq queryPurchasePlanBillReq) {
        doQueryCondition(queryPurchasePlanBillReq);
        IPage<PmsPurchasePlanBillPO> result =
                purchasePlanRepositoryService.getPmsPurchasePlanMapper().pageList(new Page<>(queryPurchasePlanBillReq.getCurrent(), queryPurchasePlanBillReq.getPageSize()), queryPurchasePlanBillReq);
        List<PmsPurchasePlanBillResp> dto = result.getRecords()
                .stream().map(PurchasePlanBillConvert.INSTANCE::convertPoToResp).collect(Collectors.toList());
        return new PageResult<>(result.getTotal(), dto);
    }

    @Override
    public PageResult<PmsPurchasePlanDetailResp> pageDetailList(QueryPurchasePlanBillReq queryPurchasePlanBillReq) {
        doQueryCondition(queryPurchasePlanBillReq);
        IPage<PmsPurchasePlanDetailPO> result = purchasePlanDetailRepositoryService.pageDetailList(queryPurchasePlanBillReq);
        if (CollectionUtils.isEmpty(result.getRecords())) {
            return new PageResult<>();
        }
        return new PageResult<>(result.getTotal(),
                result.getRecords().stream().map(PurchasePlanBillConvert.INSTANCE::convertDetailPoToResp).collect(Collectors.toList()));
    }

    @Override
    public PmsPurchasePlanNumResp queryListNum(QueryPurchasePlanBillReq queryPurchasePlanBillReq) {
        doQueryCondition(queryPurchasePlanBillReq);
        return purchasePlanRepositoryService.getPmsPurchasePlanMapper().queryListNum(queryPurchasePlanBillReq);
    }

    @Override
    public PmsPurchasePlanBillResp queryPlanDetail(QueryPurchasePlanBillDetailReq queryPurchasePlanBillDetailReq) {
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
        .eq(PmsPurchasePlanBillPO::getBillNo, queryPurchasePlanBillDetailReq.getBillNo());
        PmsPurchasePlanBillPO pmsPurchasePlanBillPO = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);

        PmsPurchasePlanBillResp resp = PurchasePlanBillConvert.INSTANCE.convertPoToResp(pmsPurchasePlanBillPO);

        LambdaQueryWrapper<PmsPurchasePlanDetailPO> detailWrapper = new LambdaQueryWrapper<PmsPurchasePlanDetailPO>()
                .eq(PmsPurchasePlanDetailPO::getBillNo, queryPurchasePlanBillDetailReq.getBillNo());
        List<PmsPurchasePlanDetailPO> detailPOS = purchasePlanDetailRepositoryService.getBaseMapper().selectList(detailWrapper);
        if (CollectionUtils.isNotEmpty(detailPOS)) {
            List<PmsPurchasePlanDetailResp> detailResps = detailPOS.stream().map(PurchasePlanBillConvert.INSTANCE::convertDetailPoToResp).collect(Collectors.toList());
            resp.setDetailList(detailResps);
        }

        return resp;
    }

    @Override
    public void buildPlanDetail(PmsPurchasePlanBillResp resp, List<PurchaseBillDetailResp> orderDetailList) {
        if  (CollectionUtils.isEmpty(orderDetailList)) {
            return;
        }
        Map<String, List<PurchaseBillDetailResp>> skuMap = orderDetailList.stream()
                .collect(Collectors.groupingBy(item -> item.getSkuCode() + "_" + item.getSkuType()));
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(resp.getDetailList())) {
            resp.getDetailList().forEach(item -> {
                if (skuMap.containsKey(item.getSkuCode() + "_" + item.getSkuType())) {
                    List<PurchaseBillDetailResp> detailList = skuMap.get(item.getSkuCode() + "_" + item.getSkuType());
                    BigDecimal sumPurchQty = detailList.stream()
                            .map(PurchaseBillDetailResp::getPurchQty)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal sumPurchMoney = detailList.stream()
                            .map(PurchaseBillDetailResp::getPurchMoney)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    item.setTotalPurchQty(sumPurchQty);
                    item.setTotalPurchTaxMoney(sumPurchMoney);
                }
            });
        }
    }

    @Override
    public void addPrintCount(QueryPurchasePlanBillPrintReq queryPurchasePlanBillReq) {
        //涉及单号打印次数加一
        purchasePlanRepositoryService.getPmsPurchasePlanMapper().addPrintCount(queryPurchasePlanBillReq.getBillNoList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public PurchPlanBillOptResp updateNum(PurchasePlanBillNumChangeReq batchPurchasePlanBillReq) {
        PurchPlanBillOptResp resp = new PurchPlanBillOptResp();
        resp.setBillNo(batchPurchasePlanBillReq.getBillNo());
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<PmsPurchasePlanBillPO>()
                .eq(PmsPurchasePlanBillPO::getBillNo, batchPurchasePlanBillReq.getBillNo());
        PmsPurchasePlanBillPO po = purchasePlanRepositoryService.getBaseMapper().selectOne(queryWrapper);
        // 校验单号
        if (null == po) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_P004);
        }
        if (!po.getStatus().equals(PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode())
                && !po.getStatus().equals(PmsPurchPlanStatusEnum.PURCH_STATUS_PART_ACCEPT.getCode())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0011);
        }
        if (CollectionUtils.isEmpty(batchPurchasePlanBillReq.getGoodsList())) {
            BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0013);
        }

        LambdaQueryWrapper<PmsPurchasePlanDetailPO> detailWrapper = new LambdaQueryWrapper<PmsPurchasePlanDetailPO>()
                .eq(PmsPurchasePlanDetailPO::getBillNo, batchPurchasePlanBillReq.getBillNo());
        List<PmsPurchasePlanDetailPO> detailPOS = purchasePlanDetailRepositoryService.getBaseMapper().selectList(detailWrapper);
        // 扣减库存
        batchPurchasePlanBillReq.getGoodsList().forEach(detailReq -> {
            Optional<PmsPurchasePlanDetailPO> first = detailPOS.stream()
                    .filter(detailPO -> detailPO.getSkuType().equals(detailReq.getSkuType()) && detailPO.getSkuCode().equals(detailReq.getSkuCode()))
                    .findFirst();
            if (!first.isPresent()) {
                BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0014, new String[]{detailReq.getSkuCode()});
            } else {
                PmsPurchasePlanDetailPO detailPO = first.get();
                BigDecimal updateQty = detailReq.getUpdateQty().abs();
                if (detailReq.getUpdateType().equals(2)) {
                    updateQty = updateQty.negate();
                }
                Integer num = purchasePlanDetailRepositoryService.updateDetailNum(detailPO.getId(), updateQty);
                if (num <= 0) {
                    BizExceptions.throwWithErrorCode(PmsErrorCodeEnum.SC_PMS_005_B0012, new String[]{detailReq.getSkuCode()});
                }
            }
        });

        // 如果全部扣减完成，则更新采购计划单
        Boolean ifFinished = true;
        List<PmsPurchasePlanDetailPO> newDetailPos = purchasePlanDetailRepositoryService.getBaseMapper().selectList(detailWrapper);
        for (PmsPurchasePlanDetailPO detailPO : newDetailPos) {
            if (detailPO.getPlanReqQty().compareTo(BigDecimal.ZERO) > 0) {
                ifFinished = false;
            }
        }
        if (ifFinished) {
            po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_FINISHED.getCode());
            purchasePlanRepositoryService.getBaseMapper().updateById(po);
        } else {
            po.setStatus(PmsPurchPlanStatusEnum.PURCH_STATUS_PART_ACCEPT.getCode());
            purchasePlanRepositoryService.getBaseMapper().updateById(po);
        }
        return resp;
    }

    @Override
    public List<PmsPurchasePlanBillPO> getExpirePlanBill() {
        LambdaQueryWrapper<PmsPurchasePlanBillPO> queryWrapper = new LambdaQueryWrapper<>();
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(PmsPurchPlanStatusEnum.PURCH_STATUS_AUDITED.getCode());
        statusList.add(PmsPurchPlanStatusEnum.PURCH_STATUS_PART_ACCEPT.getCode());
        queryWrapper.in(PmsPurchasePlanBillPO::getStatus, statusList);
        queryWrapper.lt(PmsPurchasePlanBillPO::getValidityDate, LocalDate.now());
        List<PmsPurchasePlanBillPO> dbList = purchasePlanRepositoryService.getBaseMapper().selectList(queryWrapper);
        return dbList;
    }

    /**
     * 查询条件预处理
     * @param queryPurchasePlanBillReq
     */
    public void doQueryCondition(QueryPurchasePlanBillReq queryPurchasePlanBillReq){
        //数据权限
        OpInfo operatorInfo = queryPurchasePlanBillReq.getOperatorInfo();
        if(operatorInfo.getOriginDeptFlag()) {
            return;
        }
        //分管部门list
        List<String> manageDeptCodeList = operatorInfo.getManageDeptCodeList();
        if(CollectionUtils.isEmpty(operatorInfo.getManageDeptCodeList())) {
            return;
        }
        if(CollectionUtils.isEmpty(queryPurchasePlanBillReq.getDeptCodeList())){
            queryPurchasePlanBillReq.setDeptCodeList(manageDeptCodeList);
        } else {
            //剔除非分管部门的数据
            queryPurchasePlanBillReq.getDeptCodeList().stream()
                    .filter(item-> !manageDeptCodeList.contains(item))
                    .peek(code -> queryPurchasePlanBillReq.getDeptCodeList().remove(code))
                    .collect(Collectors.toList());
        }
        //分管品牌品类

    }

}
