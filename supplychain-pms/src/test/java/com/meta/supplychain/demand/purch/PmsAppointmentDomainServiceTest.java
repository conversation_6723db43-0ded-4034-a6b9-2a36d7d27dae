package com.meta.supplychain.demand.purch;

import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.meta.supplychain.demand.purch.domain.intf.IPmsAppointmentDomainService;
import com.meta.supplychain.entity.dto.pms.req.appointment.PmsDeliveryDockLimitQueryDTO;
import com.metadata.idaas.client.model.LoginUserDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;

@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles("test")
@SpringBootTest(classes = SupplychainPmsApplication.class)
public class PmsAppointmentDomainServiceTest {

    @Resource
    private IPmsAppointmentDomainService pmsAppointmentDomainService;

    @Before
    public void initTenantId() {
        TenantContext.set(153658);

        LoginUserDTO loginUserDTO = new LoginUserDTO();
        loginUserDTO.setCode("001");
        loginUserDTO.setUid(10149793L);
        loginUserDTO.setName("admin");

        ThreadLocals.setValue("_login_user_idaas", loginUserDTO);
    }

    /**
     * 测试查询停靠点可预约信息方法 - 完整参数
     */
    @Test
    public void testListAvailableDockDetail() {
        // 构建包含所有查询条件的PmsDeliveryDockLimitQueryDTO实例
        PmsDeliveryDockLimitQueryDTO queryDto = new PmsDeliveryDockLimitQueryDTO();
        
//        // 设置部门编码
//        queryDto.setDeptCode("001426");
//
//        // 设置停靠点编码
//        queryDto.setDockCode("DOCK001");
        
        // 设置停靠点类型列表
        queryDto.setDockType(Arrays.asList(1, 2)); // 假设1-普通停靠点，2-特殊停靠点
        
        // 设置约束规则
        queryDto.setConstraintRule(1); // 假设1-时间约束
        
        // 设置查询日期范围
        queryDto.setStartDate(LocalDate.of(2024, 1, 1));
        queryDto.setEndDate(LocalDate.of(2024, 1, 31));

        System.out.println(pmsAppointmentDomainService.listAvailableDockDetail(queryDto));
    }
} 