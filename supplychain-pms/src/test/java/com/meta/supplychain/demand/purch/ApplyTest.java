package com.meta.supplychain.demand.purch;

import cn.linkkids.framework.croods.common.Result;
import cn.linkkids.framework.croods.common.context.ThreadLocals;
import cn.linkkids.framework.croods.tenant.context.TenantContext;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meta.supplychain.demand.purch.application.intf.IPmsApplicationManagerService;
import com.meta.supplychain.demand.purch.application.intf.PmsOrderApplyApplicationService;
import com.meta.supplychain.entity.dto.pms.demand.DemandCheckApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.BatchApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyBillDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.CancelApplyDetail4DemandDTO;
import com.meta.supplychain.entity.dto.pms.req.apply.QueryDetailByInsideIdReq;
import com.meta.supplychain.entity.dto.pms.resp.ApplyBillResp;
import com.meta.supplychain.entity.po.pms.PmsApplyBillDetailPO;
import com.meta.supplychain.entity.po.pms.PmsDemandDetailSourceRefPO;
import com.meta.supplychain.enums.pms.PmsErrorCodeEnum;
import com.meta.supplychain.infrastructure.repository.mapper.pms.PmsApplyBillDetailMapper;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.IPmsDemandDetailSourceRefRepositoryService;
import com.meta.supplychain.infrastructure.repository.service.intf.pms.PmsApplyBillDetailRepositoryService;
import com.metadata.idaas.client.model.LoginUserDTO;
import com.metadata.idaas.client.util.ClientIdentUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description:
 * @date 2024年08月26日 10:54
 */
@SpringBootTest
public class ApplyTest {
    @Autowired
    private IPmsApplicationManagerService IPmsApplicationManagerService;

    @Autowired
    private PmsApplyBillDetailRepositoryService pmsApplyBillDetailMapper;

    @Autowired
    private PmsOrderApplyApplicationService applyApplicationService;

    @Test
    public void testCheckApplyBill(){
        TenantContext.set("153658");
        applyApplicationService.cancelOrderApply4Demand(Lists.newArrayList(CancelApplyDetail4DemandDTO.builder().billNo("RR250609000005").insideId(1L).build(),
                 CancelApplyDetail4DemandDTO.builder().billNo("RR250609000005").insideId(12L).build(),
                 CancelApplyDetail4DemandDTO.builder().billNo("RR250609000005").insideId(28L).build(),
                 CancelApplyDetail4DemandDTO.builder().billNo("RR250609000005").insideId(49L).build()));
        List<PmsApplyBillDetailPO> pmsApplyBillDetailPOS = pmsApplyBillDetailMapper.queryListByInsideId(Lists.newArrayList(QueryDetailByInsideIdReq.builder()
                .billNo("OR250610000003")
                .insideId(1L)
                .build(), QueryDetailByInsideIdReq.builder()
                .billNo("OR250610000003")
                .insideId(1L)
                .build()));

        List<String> billNumbers = new ArrayList<>();
        billNumbers.add("OR250530000004");
        BatchApplyBillDTO batchApplyBillDTO = BatchApplyBillDTO.builder().billNumbers(billNumbers).build();

        Result<List<ApplyBillResp>> listResult = IPmsApplicationManagerService.getPmsOrderApplyApplicationService().auditOrderApply(batchApplyBillDTO);
        System.out.println("testCheckApplyBill:" + JSON.toJSONString(listResult));
    }

    @Test
    public void testGenerateProcurementBatch(){
        TenantContext.set("153658");
        LoginUserDTO loginUser = ClientIdentUtil.getLoginUser();
        if(null == loginUser){
            loginUser = new LoginUserDTO();
            loginUser.setUid(0L);
            loginUser.setCode("system");
            loginUser.setName("system");
            ThreadLocals.setValue("_login_user_idaas", loginUser);
        }
        List<String> billNumbers = new ArrayList<>();
        billNumbers.add("OR250623000043");
        BatchApplyBillDTO batchApplyBillDTO = BatchApplyBillDTO.builder().billNumbers(billNumbers).build();

        Result<List<ApplyBillResp>> listResult = IPmsApplicationManagerService.getPmsOrderApplyApplicationService().auditOrderApply(batchApplyBillDTO);
        System.out.println("testCheckApplyBill:" + JSON.toJSONString(listResult));
    }
}
